import{_ as k,c as M}from"./index-BeQABqnP.js";import x from"./main-UvnCRKF5.js";import{W as U}from"./main-DQAeP9Mx.js";import{u as l,k as j,c as C,r as N,l as y,m as i,H as a,z as t,C as _,y as P,E as S,F as z,h as O}from"./form-create-B86qX0W_.js";import{i as q,j as D,f as E,v as F}from"./form-designer-DQFPUccF.js";var v=(e=>(e.News="news",e.Image="image",e.Voice="voice",e.Video="video",e.Music="music",e.Text="text",e))(v||{}),d=(e=>(e.Published="1",e.Draft="2",e))(d||{});const H=e=>({accountId:l(e).accountId,type:l(e).type,name:null,content:null,mediaId:null,url:null,title:null,description:null,thumbMediaId:null,thumbMediaUrl:null,musicUrl:null,hqMusicUrl:null,introduction:null,articles:[]}),R={key:0,class:"select-item"},w=M(j({__name:"TabNews",props:{modelValue:{},newsType:{}},emits:["update:modelValue"],setup(e,{emit:T}){const V=e,b=T,s=C({get:()=>V.modelValue,set:u=>b("update:modelValue",u)}),n=N(!1),g=u=>{n.value=!1,s.value.articles=u.content.newsItem},h=()=>{s.value.articles=[]};return(u,o)=>{const r=k,m=E,c=D,p=q,I=F;return i(),y("div",null,[a(p,null,{default:t(()=>[l(s).articles&&l(s).articles.length>0?(i(),y("div",R,[a(l(x),{articles:l(s).articles},null,8,["articles"]),a(c,{class:"ope-row"},{default:t(()=>[a(m,{type:"danger",circle:"",onClick:h},{default:t(()=>[a(r,{icon:"ep:delete"})]),_:1})]),_:1})])):_("",!0),l(s).content?_("",!0):(i(),P(c,{key:1,span:24},{default:t(()=>[a(p,{style:{"text-align":"center"},align:"middle"},{default:t(()=>[a(c,{span:24},{default:t(()=>[a(m,{type:"success",onClick:o[0]||(o[0]=f=>n.value=!0)},{default:t(()=>[S(z(u.newsType===l(d).Published?"\u9009\u62E9\u5DF2\u53D1\u5E03\u56FE\u6587":"\u9009\u62E9\u8349\u7A3F\u7BB1\u56FE\u6587")+" ",1),a(r,{icon:"ep:circle-check"})]),_:1})]),_:1})]),_:1})]),_:1})),a(I,{title:"\u9009\u62E9\u56FE\u6587",modelValue:l(n),"onUpdate:modelValue":o[1]||(o[1]=f=>O(n)?n.value=f:null),width:"90%","append-to-body":"","destroy-on-close":""},{default:t(()=>[a(l(U),{type:"news","account-id":l(s).accountId,newsType:u.newsType,onSelectMaterial:g},null,8,["account-id","newsType"])]),_:1},8,["modelValue"])]),_:1})])}}}),[["__scopeId","data-v-bddd0a87"]]),W=Object.freeze(Object.defineProperty({__proto__:null,default:w},Symbol.toStringTag,{value:"Module"}));export{d as N,v as R,w as T,W as a,H as c};
