<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="需求单号" prop="requestNo">
        <el-input
          v-model="queryParams.requestNo"
          placeholder="请输入需求单号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="产品编号" prop="productCode">
        <el-input
          v-model="queryParams.productCode"
          placeholder="请输入产品编号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="产品名称" prop="productName">
        <el-input
          v-model="queryParams.productName"
          placeholder="请输入产品名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>

      <div v-if="expandSearchForm">
        <el-form-item label="客户名称" prop="customerName">
          <el-input
            v-model="queryParams.customerName"
            placeholder="请输入客户名称"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select
            v-model="queryParams.status"
            placeholder="请选择状态"
            clearable
            class="!w-240px"
          >
            <el-option
              v-for="dict in getStrDictOptions(DICT_TYPE.MFG_REQUEST_STATUS)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="来源类型" prop="orderType">
          <el-select
            v-model="queryParams.orderType"
            placeholder="请选择来源类型"
            clearable
            class="!w-240px"
          >
            <el-option label="请选择字典生成" value="" />
          </el-select>
        </el-form-item>
        <el-form-item label="来源单号" prop="orderNo">
          <el-input
            v-model="queryParams.orderNo"
            placeholder="请输入来源单号"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="订单时间" prop="orderDate">
          <el-date-picker
            v-model="queryParams.orderDate"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="交期" prop="deliverDate">
          <el-date-picker
            v-model="queryParams.deliverDate"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="创建时间" prop="createTime">
          <el-date-picker
            v-model="queryParams.createTime"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="准备状态" prop="readyStatus">
          <el-select
            v-model="queryParams.readyStatus"
            placeholder="请选择准备状态"
            clearable
            class="!w-240px"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.MFG_MATERIAL_READY_STATUS)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="审批单号" prop="approveNo">
          <el-input
            v-model="queryParams.approveNo"
            placeholder="请输入审批单号"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="审批状态" prop="approveStatus">
          <el-select
            v-model="queryParams.approveStatus"
            placeholder="请选择审批状态"
            clearable
            class="!w-240px"
          >
            <el-option
              v-for="dict in getStrDictOptions(DICT_TYPE.APPROVE_STATUS)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
      </div>

      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['mfg:request-order:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="danger"
          plain
          @click="handleAnalysis"
          :loading="exportLoading"
          v-hasPermi="['mfg:request-order:analysis']"
        >
          <Icon icon="ep:plus" class="mr-5px" />库存分析
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['mfg:request-order:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
        <el-button
          type="warning"
          plain
          @click="handleApproval"
          v-hasPermi="['mfg:request-order:approve']"
        >
          <Icon icon="ep:check" class="mr-5px" /> 审核
        </el-button>
        <el-button @click="toggleSearchForm" text type="primary" link>
          {{ expandSearchForm ? '收起' : '展开' }}
          <Icon :icon="expandSearchForm ? 'ep:arrow-up' : 'ep:arrow-down'" />
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table
      v-loading="loading"
      :data="list"
      :stripe="true"
      :show-overflow-tooltip="true"
      :border="true"
      ref="tableRef"
      show-summary
      :summary-method="getSummaries"
      @selection-change="handleSelectionChange"
    >
      <!-- 子表的列表 -->
      <el-table-column type="expand" fixed="left" width="60">
        <template #default="scope">
          <el-tabs model-value="requestOrderDetail">
            <el-tab-pane label="生产订单明细" name="requestOrderDetail">
              <RequestOrderDetailList
                :biz-order-id="scope.row.id"
                detail-type="requestOrder"
                :warehouse-map="warehouseMap"
                :location-map="locationMap"
                :unit-map="unitMap"
              />
            </el-tab-pane>
            <el-tab-pane label="BOM明细" name="bomDetail">
              <RequestOrderDetailList
                :biz-order-id="scope.row.id"
                :bom-id="scope.row.bomId"
                detail-type="bom"
                :warehouse-map="warehouseMap"
                :location-map="locationMap"
                :unit-map="unitMap"
              />
            </el-tab-pane>
            <el-tab-pane label="任务单" name="taskOrderDetail">
              <RequestOrderDetailList
                :biz-order-id="scope.row.id"
                detail-type="workOrder"
                :warehouse-map="warehouseMap"
                :location-map="locationMap"
                :unit-map="unitMap"
              />
            </el-tab-pane>
          </el-tabs>
        </template>
      </el-table-column>
      <!-- 增加复选框-->
      <el-table-column type="selection" width="40px" align="center" fixed="left" />
      <!-- 增加序号-->
      <el-table-column type="index" width="50px" align="center" fixed="left" />
      <!-- 增加收藏的图标 作为优先级设定-->
      <el-table-column align="center" prop="id" width="40px" fixed="left">
        <template #default="scope">
          <Icon
            :icon="scope.row.priority === 1 ? 'ep:star-filled' : 'ep:star'"
            :class="['mr-1px', { 'text-yellow-500': scope.row.priority === 1 }]"
            @click="handlePriority(scope.row.id, scope.row.priority)"
          />
        </template>
      </el-table-column>
      <el-table-column label="需求单号" align="left" prop="requestNo" width="220" fixed="left">
        <template #default="scope">
          <div class="request-no-container">
            <div class="order-no-cell" v-if="scope.row.requestNo">
              <div class="order-no-content">
                <el-button
                  link
                  type="primary"
                  @click="openDetailDialog(scope.row.id)"
                  v-hasPermi="['mfg:request-order:query']"
                >
                  {{ scope.row.requestNo }}
                </el-button>
              </div>
              <el-button
                link
                type="info"
                @click="copyOrderNo(scope.row.requestNo)"
                class="copy-btn copy-btn-fixed"
                size="small"
              >
                <Icon icon="ep:copy-document" :size="12"/>
              </el-button>
            </div>
            <div class="status-tags" v-if="scope.row.requestNo">
              <dict-tag
                v-if="scope.row.approveStatus"
                :type="DICT_TYPE.APPROVE_STATUS"
                :value="scope.row.approveStatus"
                class="status-tag"
              />
              <dict-tag
                v-if="scope.row.status"
                :type="DICT_TYPE.MFG_REQUEST_STATUS"
                :value="scope.row.status"
                class="status-tag"
              />
              <dict-tag
                v-if="scope.row.readyStatus"
                :type="DICT_TYPE.MFG_MATERIAL_READY_STATUS"
                :value="scope.row.readyStatus"
                class="status-tag"
              />
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="产品编号"
        align="center"
        prop="productCode"
        width="130"
        fixed="left"
      />
      <el-table-column label="产品名称" align="center" prop="productName" width="180" />
      <!-- <el-table-column label="产品类型" align="center" prop="productSubType" width="100"/> -->
      <el-table-column label="规格" align="center" prop="spec" width="80" />
      <el-table-column label="订单数量" align="center" prop="orderQuantity" width="100">
        <template #default="scope">
          <el-tag type="success" v-if="scope.row.orderQuantity"
            >{{ quantityTableFormatter(null, null, scope.row.orderQuantity, null) }}
            {{ unitMap.get(Number(scope.row.orderUnit)) }}</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column
        label="规格数量"
        align="center"
        prop="orderSpecQuantity"
        width="100"
        :formatter="quantityTableFormatter"
      />

      <el-table-column
        label="已转生产数量"
        align="center"
        prop="fulfilledQuantity"
        width="140px"
        :formatter="quantityTableFormatter"
      />
      <el-table-column
        label="可生产数量"
        align="center"
        prop="readyQuantity"
        width="100"
        :formatter="quantityTableFormatter"
      />
      <!-- <el-table-column label="客户名称" align="center" prop="customerName" /> -->
      <el-table-column label="来源类型" align="center" prop="orderType" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.MFG_ORDER_SOURCE" :value="scope.row.orderType" />
        </template>
      </el-table-column>
      <el-table-column label="来源单号" align="center" prop="orderNo" width="120px">
        <template #default="scope">
          <div class="order-no-cell" v-if="scope.row.orderNo">
            <div class="order-no-content">
              <span>{{ scope.row.orderNo }}</span>
            </div>
            <el-button
              link
              type="info"
              @click="copyOrderNo(scope.row.orderNo)"
              class="copy-btn copy-btn-fixed"
              size="small"
            >
              <Icon icon="ep:copy-document" :size="12"/>
            </el-button>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>

      <!-- <el-table-column label="审批单号" align="center" prop="approveNo" width="120px"/>
      <el-table-column
        label="审批时间"
        align="center"
        prop="approveDate"
        :formatter="dateFormatter"
        width="180px"
      /> -->
      <el-table-column label="BOM" align="center" prop="bomCode" width="180px">
        <template #default="scope">
          <el-tag v-if="scope.row.bomCode">
            {{ scope.row.bomCode }}{{ scope.row.bomVersion ? ':' + scope.row.bomVersion : '' }}
          </el-tag>
          <el-tag v-else>待确认</el-tag>
        </template>
      </el-table-column>

      <el-table-column
        label="订单时间"
        align="center"
        prop="orderDate"
        :formatter="dateFormatter2"
        width="180px"
      />
      <el-table-column
        label="交期"
        align="center"
        prop="deliverDate"
        :formatter="dateFormatter2"
        width="100px"
      />

      <el-table-column label="生产要求" align="center" prop="requirement" width="150">
        <template #default="scope"> {{ scope.row.requirement }} {{ scope.row.remark }} </template>
      </el-table-column>
      <el-table-column
        label="上次分析时间"
        align="center"
        prop="lastAnalysisTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column
        label="操作"
        align="center"
        min-width="180px"
        fixed="right"
        show-overflow-tooltip="false"
      >
        <template #default="scope">
          <div class="action-buttons">
            <el-button
              link
              type="primary"
              @click="openBomForm('confirmBom', scope.row.id)"
              v-hasPermi="['mfg:request-order:setBom']"
              v-if="scope.row.status == 0"
            >
              确认BOM
            </el-button>
            <el-button
              link
              type="primary"
              @click="openBomForm('confirmBom', scope.row.id)"
              v-hasPermi="['mfg:request-order:setBom']"
              v-if="scope.row.status != 0 && scope.row.status != 1"
            >
              修改BOM
            </el-button>
            <el-button
              link
              type="primary"
              @click="openWorkOrderForm('transferMfg', scope.row.id)"
              v-hasPermi="['mfg:request-order:createWorkOrder']"
              v-if="scope.row.status != 0 && scope.row.status != 1 && scope.row.readyStatus != 0"
            >
              转生产
            </el-button>

<!--            <el-button-->
<!--              link-->
<!--              type="primary"-->
<!--              @click="openPurchaseForm('create', scope.row.id)"-->
<!--              v-hasPermi="['mfg:request-order:createPurchaseRequest']"-->
<!--              v-if="scope.row.status != 1 && scope.row.status != 0"-->
<!--            >-->
<!--              转采购-->
<!--            </el-button>-->
            <el-dropdown
              @command="handleCommand($event, scope.row)"
              class="more-btn"
              v-if="
                (scope.row.status != 1 && scope.row.approveStatus !== '3') ||
                (scope.row.status == 0 && scope.row.approveStatus !== '3')
              "
            >
              <el-button link type="primary" class="dropdown-trigger">
                更多
                <el-icon class="el-icon--right">
                  <arrow-down />
                </el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    :icon="Edit"
                    command="update"
                    v-if="scope.row.status != 1 && scope.row.approveStatus !== '3'"
                    v-hasPermi="['mfg:request-order:update']"
                    >编辑</el-dropdown-item
                  >
                  <el-dropdown-item
                    :icon="Delete"
                    command="delete"
                    v-if="scope.row.status == 0 && scope.row.approveStatus !== '3'"
                    v-hasPermi="['mfg:request-order:delete']"
                    >删除</el-dropdown-item
                  >
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <RequestOrderForm v-if="showRequestOrderForm" ref="formRef" @success="getList" />
  <!-- 生产订单 表单弹窗：添加/修改 -->
  <WorkOrderForm v-if="showWorkOrderForm" ref="workOrderFormRef" @success="getList" />
  <!-- 采购订单 表单弹窗：添加/修改 -->
  <RequirementForm v-if="showRequirementForm" ref="requirementFormRef" @success="getList" />
  <!-- 生产订单bom 表单弹窗：添加/修改 -->
  <BomForm v-if="showBomForm" ref="bomFormRef" @success="getList" />
  <!-- 库存分析 表单弹窗：添加/修改 -->
  <AnalysisForm v-if="showAnalysisForm" ref="analysisFormRef" @success="getList" />

  <!-- 审核表单 -->
  <ApproveInfoForm ref="approveInfoFormRef" @success="getList" />
</template>

<script setup lang="ts">
import { getIntDictOptions, getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { dateFormatter, dateFormatter2 } from '@/utils/formatTime'
import download from '@/utils/download'
import { quantityTableFormatter, formatQuantity } from '@/utils/formatter'
import { RequestOrderApi, RequestOrderVO } from '@/api/scm/mfg/requestorder'
import RequestOrderForm from './RequestOrderForm.vue'
import RequestOrderDetailList from './components/RequestOrderDetailList.vue'
import { UnitApi } from '@/api/scm/base/unit'
import { WarehouseApi } from '@/api/scm/inventory/warehouse'
import { WarehouseLocationApi } from '@/api/scm/inventory/warehouselocation'
import { ArrowDown, Edit, Delete } from '@element-plus/icons-vue'
import WorkOrderForm from '../workorder/WorkOrderForm.vue'
import RequirementForm from '../../purchase/requirement/RequirementForm.vue'
import BomForm from './BomForm.vue'
import AnalysisForm from '@/views/scm/mfg/requestorder/AnalysisForm.vue'

import ApproveInfoForm from '../../base/approveinfo/ApproveInfoForm.vue'
import { useClipboard } from '@vueuse/core'

/** 生产订单 列表 */
defineOptions({ name: 'RequestOrder' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const { push } = useRouter() // 路由跳转
const { copy } = useClipboard() // 复制功能

const tableRef = ref() // Add this ref for the table
const loading = ref(true) // 列表的加载中
const list = ref<RequestOrderVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const expandSearchForm = ref(false) // 搜索表单展开状态
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  requestNo: undefined,
  customerId: undefined,
  customerName: undefined,
  orderType: undefined,
  orderId: undefined,
  orderNo: undefined,
  orderDetailId: undefined,
  productId: undefined,
  productCode: undefined,
  productName: undefined,
  productSubType: undefined,
  productUnit: undefined,
  spec: undefined,
  status: undefined,
  approveStatus: undefined,
  approverId: undefined,
  approverName: undefined,
  bomId: undefined,
  bomCode: undefined,
  bomVersion: undefined,
  orderDate: [],
  orderQuantity: undefined,
  fulfilledQuantity: undefined,
  orderSpecQuantity: undefined,
  orderUnit: undefined,
  deliverDate: [],
  requirement: undefined,
  remark: undefined,
  createTime: [],
  readyStatus: undefined,
  readyQuantity: undefined,
  approveNo: undefined,
  approveDate: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

// 基础数据缓存（全局共享，避免重复加载）
const warehouseMap = ref<Map<number, string>>(new Map())
const locationMap = ref<Map<number, string>>(new Map())
const isBasicDataLoaded = ref(false)

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await RequestOrderApi.getRequestOrderPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 切换查询条件展开/收起状态 */
const toggleSearchForm = () => {
  expandSearchForm.value = !expandSearchForm.value
}

/** 添加/修改操作 */
const formRef = ref()
/** 生产订单 操作 */
const workOrderFormRef = ref()
/** 采购订单 操作 */
const requirementFormRef = ref()
/** 生产订单bom 操作 */
const bomFormRef = ref()
/** 库存分析 操作 */
const analysisFormRef = ref()

/** 审核表单 操作 */
const approveInfoFormRef = ref()
/** 选中行 */
const selectedRows = ref<RequestOrderVO[]>([])
/** 当前行 */
const currentRow = ref<RequestOrderVO>({} as RequestOrderVO)

const showRequestOrderForm = ref(false)
const showWorkOrderForm = ref(false)
const showRequirementForm = ref(false)
const showBomForm = ref(false)
const showAnalysisForm = ref(false)

/**
 * 封装打开带 ref 组件的方法，自动处理 nextTick 和 ref 是否存在
 */
const openComponentWithNextTick = (
  showRef: Ref<boolean>,
  componentRef: Ref<any>,
  type: string,
  ...args: any[]
) => {
  showRef.value = true
  nextTick(() => {
    if (componentRef.value && typeof componentRef.value.open === 'function') {
      componentRef.value.open(type, ...args)
    } else {
      message.error(`${type} 弹窗加载失败，请重试`)
    }
  })
}

const openForm = (type: string, id?: number) => {
  openComponentWithNextTick(showRequestOrderForm, formRef, type, id)
}

const openWorkOrderForm = (type: string, id?: number) => {
  openComponentWithNextTick(showWorkOrderForm, workOrderFormRef, type, id)
}

const openPurchaseForm = async (type: string, id?: number) => {
  if (id) {
    try {
      // 获取需求订单详情
      const requestOrderData = await RequestOrderApi.getRequestOrder(id)

      // 构造采购需求的预填充数据
      const prefillData = {
        sourceType: '2', // 来源类型：需求订单（固定值2）
        sourceNo: requestOrderData.requestNo, // 来源单号
        bizOrderId: requestOrderData.id, // 业务订单ID
        bizOrderNo: requestOrderData.requestNo, // 业务订单编号
        // bizOrderTyp: 暂时不填充业务订单类型
        requirementDate: new Date(), // 需求日期设为当前日期
        expectedDeliveryDate: requestOrderData.deliverDate
          ? new Date(requestOrderData.deliverDate)
          : new Date(), // 期望交货日期
        remark: requestOrderData.remark, // 备注
        note: requestOrderData.note // 摘要
      }

      // 打开采购需求表单并传递预填充数据
      showRequirementForm.value = true
      await nextTick()
      requirementFormRef.value.open(type, null, prefillData)
    } catch (error) {
      message.error('获取需求订单数据失败，请重试')
      // 如果获取数据失败，仍然打开表单但不预填充
      requirementFormRef.value.open(type, null)
    }
  } else {
    requirementFormRef.value.open(type, null)
  }
}

const openBomForm = (type: string, id?: number) => {
  openComponentWithNextTick(showBomForm, bomFormRef, type, id)
}

const openAnalysisForm = (type: string, ids?: Array<number>) => {
  openComponentWithNextTick(showAnalysisForm, analysisFormRef, type, ids)
}

/** 打开详情页面 */
const openDetailDialog = (id: number) => {
  // 找到对应的订单数据
  const orderData = list.value.find(item => item.id === id)
  const productName = orderData?.productName || '需求订单详情'

  push({
    name: 'RequestOrderDetail',
    params: { id },
    query: { title: productName }
  })
}



/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await RequestOrderApi.deleteRequestOrder(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await RequestOrderApi.exportRequestOrder(queryParams)
    download.excel(data, '生产订单.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 审核按钮操作 */
const handleApproval = async () => {
  if (!selectedRows.value || selectedRows.value.length === 0) {
    message.error('请选择要审核的请购单！')
    return
  }

  // 如果选中了多个请购单，提示用户
  if (selectedRows.value.length > 1) {
    message.warning('当前只支持单个请购单审核，请选择一个请购单进行审核')
    return
  }

  // 设置当前行为选中的第一个请购单
  const selectedRequestOrder = selectedRows.value[0]
  currentRow.value = selectedRequestOrder

  // 发起审批，传入业务数据
  approveInfoFormRef.value.open('approve', undefined, {
    bizId: selectedRequestOrder?.id,
    bizNo: selectedRequestOrder?.requestNo,
    bizType: 'mfg_request_order'
  })
}

/** 选择变化处理 */
const handleSelectionChange = (selection: RequestOrderVO[]) => {
  selectedRows.value = selection
}

// 基础数据加载函数
const unitMap = ref(new Map())

const getWarehouseMap = async () => {
  try {
    const data = await WarehouseApi.getWarehouseList({
      pageNo: 1,
      pageSize: 100
    })
    warehouseMap.value = new Map(data.map((item) => [item.id, item.name]))
    console.log('获取仓库映射成功:', warehouseMap.value)
  } catch (error) {
    console.error('获取仓库映射失败:', error)
  }
}

const getLocationMap = async () => {
  try {
    const data = await WarehouseLocationApi.getWarehouseLocationPage({
      pageNo: 1,
      pageSize: 100,
      warehouseId: undefined
    })
    locationMap.value = new Map(data.list.map((item) => [item.id, item.name]))
    console.log('获取库位映射成功:', locationMap.value)
  } catch (error) {
    console.error('获取库位映射失败:', error)
  }
}

const getUnitMap = async () => {
  try {
    const data = await UnitApi.getUnitPage({
      pageNo: 1,
      pageSize: 100
    })
    unitMap.value = new Map(data.list.map((item) => [item.id, item.name]))
    console.log('获取单位映射成功:', unitMap.value)
  } catch (error) {
    console.error('获取单位映射失败:', error)
  }
}

// 加载所有基础数据（只加载一次）
const loadBasicData = async () => {
  if (!isBasicDataLoaded.value) {
    await Promise.all([getWarehouseMap(), getLocationMap(), getUnitMap()])
    isBasicDataLoaded.value = true
    console.log('基础数据加载完成')
  }
}

// 转任务单
const handleTransferToTask = async (id: number) => {
  try {
    // 发起转任务单
    await RequestOrderApi.transferToTask(id)
    message.success(t('common.transferSuccess'))
    // 刷新列表
    await getList()
  } catch (error) {
    console.error('转任务单失败:', error)
  }
}

// 优先级
const handlePriority = async (id: number, priority: number) => {
  try {
    // 发起优先级
    if (priority === undefined) {
      priority = 1 // 默认优先级为1
    } else {
      priority = priority === 1 ? 0 : 1 // 切换优先级
    }
    await RequestOrderApi.setPriority({ id, priority })
    message.success(t('common.prioritySuccess'))
    // 刷新列表
    await getList()
  } catch (error) {
    console.error('优先级失败:', error)
  }
}

// 处理下拉菜单命令
const handleCommand = (command: string, row: RequestOrderVO) => {
  if (command === 'update') {
    openForm('update', row.id)
  } else if (command === 'delete') {
    handleDelete(row.id)
  } else if (command === 'transfer') {
    handleTransferToTask(row.id)
  } else if (command === 'priority') {
    handlePriority(row.id)
  }
}

const handleAnalysis = () => {
  // 获取选中的行数据
  const selectedRows = tableRef.value?.getSelectionRows()
  if (!selectedRows || selectedRows.length === 0) {
    message.warning('请至少选择一条数据')
    return
  }
  // 提取所有选中行的 id，并且校验订单状态是不是待bom确认
  if (selectedRows.some((row) => row.status == '0' || row.status == '1')) {
    message.warning('请选择bom已经确认的订单并且未全部转生产的订单')
    return
  }
  const selectedIds = selectedRows.map((row) => row.id)
  console.log('选中的 ID:', selectedIds)
  // 将 selectedIds 传递给弹窗组件
  openAnalysisForm('storageAnalysis', selectedIds)
}

/** 复制单号 */
const copyOrderNo = async (orderNo: string) => {
  try {
    await copy(orderNo)
    message.success('单号复制成功')
  } catch (error) {
    message.error('复制失败')
  }
}

/** 表格汇总方法 */
const getSummaries = (param: any) => {
  const { columns, data } = param
  const sums: string[] = []

  columns.forEach((column: any, index: number) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }

    // 需要汇总的数量字段
    const quantityFields = [
      'orderQuantity', 'orderSpecQuantity', 'fulfilledQuantity', 'readyQuantity'
    ]

    if (quantityFields.includes(column.property)) {
      // 数量字段汇总
      const values = data.map((item: any) => Number(item[column.property]) || 0)
      const total = values.reduce((prev: number, curr: number) => prev + curr, 0)
      sums[index] = total > 0 ? formatQuantity(total) : '0'
    } else {
      // 其他列不显示汇总信息
      sums[index] = ''
    }
  })

  return sums
}

/** 初始化 **/
onMounted(async () => {
  await loadBasicData() // 加载基础数据
  getList()
})
</script>

<style scoped>
/* 操作按钮容器对齐 */
.action-buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  margin: 0;
}

/* 确保下拉菜单按钮与其他按钮对齐 */
.more-btn {
  display: inline-flex;
  align-items: center;
}

.more-btn .dropdown-trigger {
  margin: 0 !important;
  padding: 0 !important;
  height: 32px !important;
  line-height: 32px !important;
}

/* 需求单号容器样式 */
.request-no-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
  padding: 4px 0;
}

/* 状态标签容器样式 */
.status-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

/* 状态标签样式 */
.status-tag :deep(.el-tag) {
  font-size: 9px !important;
  padding: 1px 4px !important;
  height: 16px !important;
  line-height: 14px !important;
  border-radius: 6px !important;
  font-weight: 500 !important;
  flex-shrink: 0;
}

.text-yellow-500 {
  color: #eab308; /* 黄色高亮 */
}

/* 复制按钮样式 */
.order-no-cell {
  display: flex;
  align-items: center;
  width: 100%;
  min-height: 20px;
  gap: 5px; /* 文字和图标之间保持一点点间隔 */
}

.order-no-content {
  flex: 0 1 auto; /* 改为自适应宽度，不占满剩余空间 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: calc(100% - 16px); /* 为图标预留空间 */
}

.copy-btn {
  padding: 2px !important;
  height: 20px !important;
  min-height: 20px !important;
  width: 20px !important;
  font-size: 12px;
  opacity: 0.6;
  transition: opacity 0.2s;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}



.copy-btn:hover {
  opacity: 1;
}
</style>
