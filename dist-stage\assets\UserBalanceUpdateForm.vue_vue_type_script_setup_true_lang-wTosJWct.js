import{a as A,d as E,F as _,G as r}from"./index-BeQABqnP.js";import{_ as G}from"./Dialog.vue_vue_type_style_index_0_lang-DpoeWsKb.js";import{g as H}from"./index-ge1eh6aq.js";import{g as P,u as R}from"./index-DUvbsVxr.js";import{h as D,aj as S,k as J,s as K,u as L,l as M,ak as N,f as O}from"./form-designer-DQFPUccF.js";import{k as Q,r as p,P as W,c as X,y,m as V,z as u,A as Y,u as n,H as l,E as v,h as Z}from"./form-create-B86qX0W_.js";const $=Q({name:"UpdateBalanceForm",__name:"UserBalanceUpdateForm",emits:["success"],setup(ee,{expose:k,emit:B}){const{t:w}=A(),f=E(),d=p(!1),c=p(!1),a=p({id:void 0,nickname:void 0,balance:"0",changeBalance:0,changeType:1}),x=W({changeBalance:[{required:!0,message:"\u53D8\u52A8\u4F59\u989D\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),m=p();k({open:async o=>{if(d.value=!0,F(),o){c.value=!0;try{const e=await H(o),i=await P({userId:e.id||0});a.value.id=e.id,a.value.nickname=e.nickname,a.value.balance=_(i.balance),a.value.changeType=1,a.value.changeBalance=0}finally{c.value=!1}}}});const T=B,U=async()=>{if(m&&await m.value.validate())if(a.value.changeBalance<=0)f.error("\u53D8\u52A8\u4F59\u989D\u4E0D\u80FD\u4E3A\u96F6");else if(r(b.value)<0)f.error("\u53D8\u52A8\u540E\u7684\u4F59\u989D\u4E0D\u80FD\u5C0F\u4E8E 0");else{c.value=!0;try{await R({userId:a.value.id,balance:r(a.value.changeBalance)*a.value.changeType}),f.success(w("common.updateSuccess")),d.value=!1,T("success")}finally{c.value=!1}}},F=()=>{var o;a.value={id:void 0,nickname:void 0,balance:"0",changeBalance:0,changeType:1},(o=m.value)==null||o.resetFields()},b=X(()=>_(r(a.value.balance)+r(a.value.changeBalance)*a.value.changeType));return(o,e)=>{const i=J,t=S,g=L,C=K,I=M,j=D,h=O,q=G,z=N;return V(),y(q,{modelValue:n(d),"onUpdate:modelValue":e[5]||(e[5]=s=>Z(d)?d.value=s:null),title:"\u4FEE\u6539\u7528\u6237\u4F59\u989D",width:"600"},{footer:u(()=>[l(h,{disabled:n(c),type:"primary",onClick:U},{default:u(()=>e[8]||(e[8]=[v("\u786E \u5B9A")])),_:1},8,["disabled"]),l(h,{onClick:e[4]||(e[4]=s=>d.value=!1)},{default:u(()=>e[9]||(e[9]=[v("\u53D6 \u6D88")])),_:1})]),default:u(()=>[Y((V(),y(j,{ref_key:"formRef",ref:m,model:n(a),rules:n(x),"label-width":"130px"},{default:u(()=>[l(t,{label:"\u7528\u6237\u7F16\u53F7",prop:"id"},{default:u(()=>[l(i,{modelValue:n(a).id,"onUpdate:modelValue":e[0]||(e[0]=s=>n(a).id=s),class:"!w-240px",disabled:""},null,8,["modelValue"])]),_:1}),l(t,{label:"\u7528\u6237\u6635\u79F0",prop:"nickname"},{default:u(()=>[l(i,{modelValue:n(a).nickname,"onUpdate:modelValue":e[1]||(e[1]=s=>n(a).nickname=s),class:"!w-240px",disabled:""},null,8,["modelValue"])]),_:1}),l(t,{label:"\u53D8\u52A8\u524D\u4F59\u989D(\u5143)",prop:"balance"},{default:u(()=>[l(i,{"model-value":n(a).balance,class:"!w-240px",disabled:""},null,8,["model-value"])]),_:1}),l(t,{label:"\u53D8\u52A8\u7C7B\u578B",prop:"changeType"},{default:u(()=>[l(C,{modelValue:n(a).changeType,"onUpdate:modelValue":e[2]||(e[2]=s=>n(a).changeType=s)},{default:u(()=>[l(g,{label:1},{default:u(()=>e[6]||(e[6]=[v("\u589E\u52A0")])),_:1}),l(g,{label:-1},{default:u(()=>e[7]||(e[7]=[v("\u51CF\u5C11")])),_:1})]),_:1},8,["modelValue"])]),_:1}),l(t,{label:"\u53D8\u52A8\u4F59\u989D(\u5143)",prop:"changeBalance"},{default:u(()=>[l(I,{modelValue:n(a).changeBalance,"onUpdate:modelValue":e[3]||(e[3]=s=>n(a).changeBalance=s),min:0,precision:2,step:.1,class:"!w-240px"},null,8,["modelValue"])]),_:1}),l(t,{label:"\u53D8\u52A8\u540E\u4F59\u989D(\u5143)"},{default:u(()=>[l(i,{"model-value":n(b),class:"!w-240px",disabled:""},null,8,["model-value"])]),_:1})]),_:1},8,["model","rules"])),[[z,n(c)]])]),_:1},8,["modelValue"])}}});export{$ as _};
