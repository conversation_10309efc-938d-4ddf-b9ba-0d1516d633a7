import{a as Y,d as Z,X as ee,ax as q,ay as ae}from"./index-BeQABqnP.js";import{_ as le}from"./Dialog.vue_vue_type_style_index_0_lang-DpoeWsKb.js";import{_ as de}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CxohUsTD.js";import{S as y}from"./index-1ZZhpKON.js";import{_ as te}from"./SaleOrderItemForm.vue_vue_type_script_setup_true_lang-Dzhx_hUE.js";import{C as oe}from"./index-DxvLTwW4.js";import{A as ue}from"./index-DWMZ_9Di.js";import{g as re}from"./index-UoUrkNdU.js";import{h as se,i as ie,j as me,aj as ce,k as ne,F as pe,x as fe,Q as ve,a0 as _e,$ as Ve,l as be,ak as Pe,f as Ue}from"./form-designer-DQFPUccF.js";import{k as Ie,r as i,P as we,c as ye,b as ke,y as f,m as c,z as t,A as he,u as d,H as e,l as k,G as h,$ as g,h as G,C as ge,E as H}from"./form-create-B86qX0W_.js";const Se=Ie({name:"SaleOrderForm",__name:"SaleOrderForm",emits:["success"],setup(xe,{expose:L,emit:R}){const{t:v}=Y(),S=Z(),n=i(!1),x=i(""),p=i(!1),_=i(""),o=i({id:void 0,customerId:void 0,accountId:void 0,saleUserId:void 0,orderTime:void 0,remark:void 0,fileUrl:"",discountPercent:0,discountPrice:0,totalPrice:0,depositPrice:0,items:[],no:void 0}),$=we({customerId:[{required:!0,message:"\u5BA2\u6237\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],orderTime:[{required:!0,message:"\u8BA2\u5355\u65F6\u95F4\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),V=ye(()=>_.value==="detail"),b=i(),T=i([]),P=i([]),C=i([]),U=i("item"),F=i();ke(()=>o.value,r=>{if(!r)return;const a=r.items.reduce((u,s)=>u+s.totalPrice,0),m=r.discountPercent!=null?ae(a,r.discountPercent/100):0;o.value.discountPrice=m,o.value.totalPrice=a-m},{deep:!0}),L({open:async(r,a)=>{if(n.value=!0,x.value=v("action."+r),_.value=r,E(),a){p.value=!0;try{o.value=await y.getSaleOrder(a)}finally{p.value=!1}}T.value=await oe.getCustomerSimpleList(),C.value=await re(),P.value=await ue.getAccountSimpleList();const m=P.value.find(u=>u.defaultStatus);m&&(o.value.accountId=m.id)}});const z=R,D=async()=>{await b.value.validate(),await F.value.validate(),p.value=!0;try{const r=o.value;_.value==="create"?(await y.createSaleOrder(r),S.success(v("common.createSuccess"))):(await y.updateSaleOrder(r),S.success(v("common.updateSuccess"))),n.value=!1,z("success")}finally{p.value=!1}},E=()=>{var r;o.value={id:void 0,customerId:void 0,accountId:void 0,saleUserId:void 0,orderTime:void 0,remark:void 0,fileUrl:void 0,discountPercent:0,discountPrice:0,totalPrice:0,depositPrice:0,items:[]},(r=b.value)==null||r.resetFields()};return(r,a)=>{const m=ne,u=ce,s=me,K=pe,I=ve,w=fe,Q=ee,O=ie,X=Ve,B=_e,J=de,j=be,M=se,A=Ue,N=le,W=Pe;return c(),f(N,{title:d(x),modelValue:d(n),"onUpdate:modelValue":a[13]||(a[13]=l=>G(n)?n.value=l:null),width:"1080"},{footer:t(()=>[d(V)?ge("",!0):(c(),f(A,{key:0,onClick:D,type:"primary",disabled:d(p)},{default:t(()=>a[14]||(a[14]=[H(" \u786E \u5B9A ")])),_:1},8,["disabled"])),e(A,{onClick:a[12]||(a[12]=l=>n.value=!1)},{default:t(()=>a[15]||(a[15]=[H("\u53D6 \u6D88")])),_:1})]),default:t(()=>[he((c(),f(M,{ref_key:"formRef",ref:b,model:d(o),rules:d($),"label-width":"100px",disabled:d(V)},{default:t(()=>[e(O,{gutter:20},{default:t(()=>[e(s,{span:8},{default:t(()=>[e(u,{label:"\u8BA2\u5355\u5355\u53F7",prop:"no"},{default:t(()=>[e(m,{disabled:"",modelValue:d(o).no,"onUpdate:modelValue":a[0]||(a[0]=l=>d(o).no=l),placeholder:"\u4FDD\u5B58\u65F6\u81EA\u52A8\u751F\u6210"},null,8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:t(()=>[e(u,{label:"\u8BA2\u5355\u65F6\u95F4",prop:"orderTime"},{default:t(()=>[e(K,{modelValue:d(o).orderTime,"onUpdate:modelValue":a[1]||(a[1]=l=>d(o).orderTime=l),type:"date","value-format":"x",placeholder:"\u9009\u62E9\u8BA2\u5355\u65F6\u95F4",class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:t(()=>[e(u,{label:"\u5BA2\u6237",prop:"customerId"},{default:t(()=>[e(w,{modelValue:d(o).customerId,"onUpdate:modelValue":a[2]||(a[2]=l=>d(o).customerId=l),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u5BA2\u6237",class:"!w-1/1"},{default:t(()=>[(c(!0),k(h,null,g(d(T),l=>(c(),f(I,{key:l.id,label:l.name,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:t(()=>[e(u,{label:"\u9500\u552E\u4EBA\u5458",prop:"saleUserId"},{default:t(()=>[e(w,{modelValue:d(o).saleUserId,"onUpdate:modelValue":a[3]||(a[3]=l=>d(o).saleUserId=l),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u9500\u552E\u4EBA\u5458",class:"!w-1/1"},{default:t(()=>[(c(!0),k(h,null,g(d(C),l=>(c(),f(I,{key:l.id,label:l.nickname,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(s,{span:16},{default:t(()=>[e(u,{label:"\u5907\u6CE8",prop:"remark"},{default:t(()=>[e(m,{type:"textarea",modelValue:d(o).remark,"onUpdate:modelValue":a[4]||(a[4]=l=>d(o).remark=l),rows:1,placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:t(()=>[e(u,{label:"\u9644\u4EF6",prop:"fileUrl"},{default:t(()=>[e(Q,{"is-show-tip":!1,modelValue:d(o).fileUrl,"onUpdate:modelValue":a[5]||(a[5]=l=>d(o).fileUrl=l),limit:1},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(J,null,{default:t(()=>[e(B,{modelValue:d(U),"onUpdate:modelValue":a[6]||(a[6]=l=>G(U)?U.value=l:null),class:"-mt-15px -mb-10px"},{default:t(()=>[e(X,{label:"\u8BA2\u5355\u4EA7\u54C1\u6E05\u5355",name:"item"},{default:t(()=>[e(te,{ref_key:"itemFormRef",ref:F,items:d(o).items,disabled:d(V)},null,8,["items","disabled"])]),_:1})]),_:1},8,["modelValue"])]),_:1}),e(O,{gutter:20},{default:t(()=>[e(s,{span:8},{default:t(()=>[e(u,{label:"\u4F18\u60E0\u7387\uFF08%\uFF09",prop:"discountPercent"},{default:t(()=>[e(j,{modelValue:d(o).discountPercent,"onUpdate:modelValue":a[7]||(a[7]=l=>d(o).discountPercent=l),"controls-position":"right",min:0,precision:2,placeholder:"\u8BF7\u8F93\u5165\u4F18\u60E0\u7387",class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:t(()=>[e(u,{label:"\u6536\u6B3E\u4F18\u60E0",prop:"discountPrice"},{default:t(()=>[e(m,{disabled:"",modelValue:d(o).discountPrice,"onUpdate:modelValue":a[8]||(a[8]=l=>d(o).discountPrice=l),formatter:d(q)},null,8,["modelValue","formatter"])]),_:1})]),_:1}),e(s,{span:8},{default:t(()=>[e(u,{label:"\u4F18\u60E0\u540E\u91D1\u989D"},{default:t(()=>[e(m,{disabled:"",modelValue:d(o).totalPrice,"onUpdate:modelValue":a[9]||(a[9]=l=>d(o).totalPrice=l),formatter:d(q)},null,8,["modelValue","formatter"])]),_:1})]),_:1}),e(s,{span:8},{default:t(()=>[e(u,{label:"\u7ED3\u7B97\u8D26\u6237",prop:"accountId"},{default:t(()=>[e(w,{modelValue:d(o).accountId,"onUpdate:modelValue":a[10]||(a[10]=l=>d(o).accountId=l),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u7ED3\u7B97\u8D26\u6237",class:"!w-1/1"},{default:t(()=>[(c(!0),k(h,null,g(d(P),l=>(c(),f(I,{key:l.id,label:l.name,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:t(()=>[e(u,{label:"\u6536\u53D6\u8BA2\u91D1",prop:"depositPrice"},{default:t(()=>[e(j,{modelValue:d(o).depositPrice,"onUpdate:modelValue":a[11]||(a[11]=l=>d(o).depositPrice=l),"controls-position":"right",min:0,precision:2,placeholder:"\u8BF7\u8F93\u5165\u6536\u53D6\u8BA2\u91D1",class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules","disabled"])),[[W,d(p)]])]),_:1},8,["title","modelValue"])}}});export{Se as _};
