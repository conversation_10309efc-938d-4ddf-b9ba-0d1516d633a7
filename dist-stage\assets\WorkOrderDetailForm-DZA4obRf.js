import{_ as na,c as ua}from"./index-BeQABqnP.js";import{W as oa}from"./index-B3LrXjfm.js";import{B as da}from"./index-CH8pxbb6.js";import{WarehouseApi as ra}from"./index-CASHthoJ.js";import{U as sa}from"./index-BDU5cx5r.js";import{h as pa}from"./tree-COGD3qag.js";import{f as V}from"./formatter-BLTmz7GT.js";import{f as ca,p as ya,g as z,c as A,C as ma,B as va,a as Qa}from"./bomCalculation-DYv5_fDl.js";import{h as fa,_ as ba,aj as ha,a1 as Ia,x as Na,Q as Sa,l as wa,k as ka,Z as ga,ak as xa,f as _a,i as Va}from"./form-designer-DQFPUccF.js";import{k as Ua,r as k,P as qa,b as H,e as Oa,n as C,l as P,m as g,G as B,A as Ta,H as u,y as E,u as y,z as d,v as I,F,$ as K,E as $a}from"./form-create-B86qX0W_.js";const za={class:"material-info"},Aa={class:"material-name"},Ca={class:"value"},Pa={class:"material-code"},Ba={class:"value"},Ea={class:"material-spec"},Fa={class:"value"},ja=ua(Ua({__name:"WorkOrderDetailForm",props:{bizOrderId:{},bomId:{},slotQuantity:{},orderQuantity:{},orderSpecQuantity:{},isTransferMode:{type:Boolean}},setup(Z,{expose:J}){const m=Z,U=k(!1),r=k([]),S=qa({}),j=k();H(()=>({orderId:m.bizOrderId,bomId:m.bomId}),async({orderId:e,bomId:t})=>{if(r.value=[],e)try{U.value=!0;const a=await oa.getWorkOrderDetailListByBizOrderId(e);a&&Array.isArray(a)&&a.length>0?G(a):r.value=[],D(),await C(),$()}finally{U.value=!1}else if(t){const a=await Y(t,"");r.value=a.map(l=>({...l,bizOrderId:m.bizOrderId,unit:l.materialUnit,slotQuantity:l.quantity||0,quantity:x({slotQuantity:l.quantity||0})})),C(()=>{$()})}},{immediate:!0});const X=()=>{const e={id:void 0,num:r.value.length>0?Math.max(...r.value.map(t=>Number(t.num)||0))+1:1,bizOrderId:void 0,bizOrderNo:void 0,warehouseId:void 0,locationId:void 0,materialId:void 0,materialName:void 0,materialCode:void 0,spec:void 0,unit:void 0,unitPrice:void 0,amount:void 0,quantity:void 0,plannedQuantity:void 0,fulfilledQuantity:void 0,standardPlannedQuantity:void 0,standardFulfilledQuantity:void 0,standardUnit:void 0,plannedSpecQuantity:void 0,fulfilledSpecQuantity:void 0,slotQuantity:0,slotSpecQuantity:0,lockQuantity:void 0,lockTransitQuantity:void 0,readyQuantity:void 0,stockQuantity:void 0,shortageQuantity:void 0,purchaseQuantity:void 0,transitQuantity:void 0,lockStockQuantity:void 0,readyStatus:void 0,lossRate:void 0,lossQuantity:void 0,taxPrice:void 0,taxAmount:void 0,invoiceQuantity:void 0,invoiceAmount:void 0,standardInvoiceQuantity:void 0,note:void 0,batchNo:void 0,remark:void 0,costObjectId:void 0,costObjectName:void 0,accountingVoucherNumber:void 0,kdId:void 0,kdOrderId:void 0,createTime:void 0,updateTime:void 0,creator:void 0,updater:void 0,deleted:void 0};e.bizOrderId=m.bizOrderId,r.value.push(e)},D=()=>{r.value.forEach((e,t)=>{e.num=t+1})},L=()=>{const e=m.orderQuantity||0,t=m.slotQuantity&&m.slotQuantity>0?m.slotQuantity:va.DEFAULT_SLOT_QUANTITY;return Qa(e,t)},x=e=>{const t=typeof e=="object"?e.slotQuantity||0:e||0,a=L();return ca(t*a,ma.QUANTITY_PRECISION)},q=ya,O=e=>{if(!e)return"\u4E2A";if(!h.value||h.value.length===0)return R(e);const t=h.value.find(l=>l.id===e);return(t?t.name:"")||R(e)},R=e=>{if(h.value&&h.value.length>0){const t=h.value.find(a=>a.id===e);if(t&&t.name)return t.name}return"\u4E2A"},T=e=>{const t=q(e.spec||""),a=e.slotQuantity||0;let l="";if(h.value&&h.value.length>0&&e.unit)l=O(e.unit);else{if(e.unit)return a||1;l="\u4E2A"}const s=z(l);return!t.value||t.value<=0?a||1:A(a,t,s,l).displayText},_=e=>{const t=L();if(e.slotSpecQuantity!==void 0&&e.slotSpecQuantity!==null&&e.slotSpecQuantity!==0||(e.slotSpecQuantity=T(e)),typeof e.slotSpecQuantity=="string"){const a=q(e.spec||""),l=O(e.unit||0),s=z(l);return A((e.slotQuantity||0)*t,a,s,l).displayText}{const a=q(e.spec||""),l=O(e.unit||0),s=z(l);return A((e.slotQuantity||0)*t,a,s,l).displayText}},W=()=>{r.value&&Array.isArray(r.value)&&r.value.forEach(e=>{e&&(e.slotSpecQuantity=T(e),e.quantity=x(e),e.plannedSpecQuantity=_(e))})},$=()=>{W()};H(()=>[m.slotQuantity,m.orderQuantity,m.orderSpecQuantity],()=>{r.value&&Array.isArray(r.value)&&r.value.forEach(e=>{e&&(e.quantity=x(e),e.plannedSpecQuantity=_(e))})},{immediate:!0});const aa=e=>{const{columns:t,data:a}=e,l=[];return t.forEach((s,p)=>{if(p===0)return void(l[p]="\u5408\u8BA1");const w=s.property;if(w==="slotQuantity"){const v=a.map(i=>Number(i.slotQuantity)||0);if(v.every(i=>Number.isNaN(i)))l[p]="";else{const i=v.reduce((Q,N)=>{const f=Number(N);return Number.isNaN(f)?Q:Q+f},0);l[p]=V(i)}}else if(w==="slotSpecQuantity"){const v=a.map(i=>Number(i.slotSpecQuantity)||0);if(v.every(i=>Number.isNaN(i)))l[p]="";else{const i=v.reduce((Q,N)=>{const f=Number(N);return Number.isNaN(f)?Q:Q+f},0);l[p]=V(i)}}else if(w==="quantity"){const v=a.map(i=>Number(i.quantity)||0);if(v.every(i=>Number.isNaN(i)))l[p]="";else{const i=v.reduce((Q,N)=>{const f=Number(N);return Number.isNaN(f)?Q:Q+f},0);l[p]=V(i)}}else if(w==="plannedQuantity"){const v=a.map(i=>Number(i.plannedQuantity)||0);if(v.every(i=>Number.isNaN(i)))l[p]="";else{const i=v.reduce((Q,N)=>{const f=Number(N);return Number.isNaN(f)?Q:Q+f},0);l[p]=V(i)}}else l[p]=""}),l},M=k([]),ea=k([]),h=k([]);Oa(async()=>{await(async()=>{const e=await ra.getWarehouseList({});M.value=pa(e,"id","parentId")})(),await(async()=>{const e=await sa.getUnitPage({pageNo:1,pageSize:100});h.value=e.list})(),await C(),$()});const Y=async(e,t)=>(await da.getBomMaterialListByBomId(e,t)).map(a=>({...a,unit:a.materialUnit||a.unit,bizOrderId:m.bizOrderId,num:a.num||1,plannedQuantity:a.quantity||0,fulfilledQuantity:0,unitPrice:0,amount:0,remark:a.remark||""})),G=e=>{if(!e||e.length===0)return;r.value=[];const t=e.map((a,l)=>({num:l+1,materialId:a.materialId,materialCode:a.materialCode,materialName:a.materialName,spec:a.spec||a.materialSpec,unit:a.unit,quantity:a.pendingQuantity||a.quantity||a.plannedQuantity,plannedQuantity:a.plannedQuantity||a.pendingQuantity||a.quantity,fulfilledQuantity:a.fulfilledQuantity||0,standardUnit:a.standardUnit||a.standardUnitId||a.unit,standardPlannedQuantity:a.standardPlannedQuantity||a.plannedQuantity||a.pendingQuantity||a.quantity,standardFulfilledQuantity:a.standardFulfilledQuantity||0,plannedSpecQuantity:a.plannedSpecQuantity||a.slotSpecQuantity||0,fulfilledSpecQuantity:a.fulfilledSpecQuantity||0,slotQuantity:a.slotQuantity||0,slotSpecQuantity:a.slotSpecQuantity||0,readyQuantity:a.readyQuantity||0,stockQuantity:a.stockQuantity||0,shortageQuantity:a.shortageQuantity||0,purchaseQuantity:a.purchaseQuantity||0,transitQuantity:a.transitQuantity||0,lockStockQuantity:a.lockStockQuantity||0,lockTransitQuantity:a.lockTransitQuantity||0,lockQuantity:a.lockQuantity||0,readyStatus:a.readyStatus||0,lossRate:a.lossRate||0,lossQuantity:a.lossQuantity||0,unitPrice:a.unitPrice||0,amount:a.amount||0,remark:a.remark||a.requirement||"",note:a.note||"",batchNo:a.batchNo||"",warehouseId:a.warehouseId,locationId:a.locationId,bizOrderId:m.bizOrderId,version:a.version||1,materialType:a.materialType}));r.value=t};return J({validate:()=>j.value.validate(),getData:()=>r.value,getMaterialsByBomId:Y,setDetails:G,recalculateAll:W}),(e,t)=>{const a=ba,l=Ia,s=ha,p=Sa,w=Na,v=wa,i=ka,Q=na,N=ga,f=fa,ta=_a,la=Va,ia=xa;return g(),P(B,null,[Ta((g(),E(f,{ref_key:"formRef",ref:j,model:y(r),rules:y(S),"label-width":"0px","inline-message":!0},{default:d(()=>[u(N,{data:y(r),class:"-mt-10px",border:"","max-height":400,style:{width:"100%"},"show-summary":"","summary-method":aa},{default:d(()=>[u(a,{label:"\u7269\u6599\u4FE1\u606F","min-width":"200"},{default:d(({row:n})=>[I("div",za,[I("div",Aa,[t[0]||(t[0]=I("span",{class:"label"},"\u540D\u79F0\uFF1A",-1)),I("span",Ca,F(n.materialName||"-"),1)]),I("div",Pa,[t[1]||(t[1]=I("span",{class:"label"},"\u7F16\u53F7\uFF1A",-1)),I("span",Ba,F(n.materialCode||"-"),1)]),I("div",Ea,[t[2]||(t[2]=I("span",{class:"label"},"\u89C4\u683C\uFF1A",-1)),I("span",Fa,F(n.spec||"-"),1)])])]),_:1}),u(a,{label:"\u4ED3\u5E93","min-width":"150"},{default:d(({row:n,$index:c})=>[u(s,{prop:`${c}.warehouseId`,rules:y(S).warehouseId,class:"mb-0px!"},{default:d(()=>[u(l,{modelValue:n.warehouseId,"onUpdate:modelValue":o=>n.warehouseId=o,data:y(M),props:{value:"id",label:"name",children:"children"},placeholder:"\u8BF7\u9009\u62E9\u4ED3\u5E93",clearable:"",filterable:"",class:"!w-240px","node-key":"id","render-after-expand":!1},null,8,["modelValue","onUpdate:modelValue","data"])]),_:2},1032,["prop","rules"])]),_:1}),u(a,{label:"\u5E93\u4F4D","min-width":"150"},{default:d(({row:n,$index:c})=>[u(s,{prop:`${c}.locationId`,rules:y(S).locationId,class:"mb-0px!"},{default:d(()=>[u(w,{modelValue:n.locationId,"onUpdate:modelValue":o=>n.locationId=o,placeholder:"\u8BF7\u9009\u62E9\u5E93\u4F4D",class:"!w-240px"},{default:d(()=>[(g(!0),P(B,null,K(y(ea),o=>(g(),E(p,{key:o.id,label:o.name,value:o.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])]),_:1}),u(a,{label:"\u5355\u4F4D","min-width":"150"},{default:d(({row:n,$index:c})=>[u(s,{prop:`${c}.unit`,rules:y(S).unit,class:"mb-0px!"},{default:d(()=>[u(w,{modelValue:n.unit,"onUpdate:modelValue":o=>n.unit=o,placeholder:"\u8BF7\u9009\u62E9\u5355\u4F4D",class:"!w-240px",disabled:""},{default:d(()=>[(g(!0),P(B,null,K(y(h),o=>(g(),E(p,{key:o.id,label:o.name,value:o.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])]),_:1}),u(a,{label:"\u6BCF\u69FD\u6570\u91CF","min-width":"150",prop:"slotQuantity"},{default:d(({row:n,$index:c})=>[u(s,{prop:`${c}.slotQuantity`,rules:y(S).slotQuantity,class:"mb-0px!"},{default:d(()=>[u(v,{modelValue:n.slotQuantity,"onUpdate:modelValue":o=>n.slotQuantity=o,placeholder:"\u8BF7\u8F93\u5165\u6BCF\u69FD\u6570\u91CF",precision:4,step:1e-4,min:0,"controls-position":"right",class:"w-full",onChange:o=>(b=>{b.quantity=x(b),(b.slotSpecQuantity===void 0||b.slotSpecQuantity===null||b.slotSpecQuantity===0)&&(b.slotSpecQuantity=T(b)),b.plannedSpecQuantity=_(b)})(n)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:2},1032,["prop","rules"])]),_:1}),u(a,{label:"\u6570\u91CF","min-width":"150",prop:"quantity"},{default:d(({row:n,$index:c})=>[u(s,{prop:`${c}.quantity`,rules:y(S).quantity,class:"mb-0px!"},{default:d(()=>[u(i,{modelValue:n.quantity,"onUpdate:modelValue":o=>n.quantity=o,placeholder:"\u8BF7\u8F93\u5165\u6570\u91CF"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])]),_:1}),u(a,{label:"\u89C4\u683C\u6570\u91CF","min-width":"150",prop:"plannedSpecQuantity"},{default:d(({row:n,$index:c})=>[u(s,{prop:`${c}.plannedSpecQuantity`,rules:y(S).plannedSpecQuantity,class:"mb-0px!"},{default:d(()=>[u(i,{modelValue:n.plannedSpecQuantity,"onUpdate:modelValue":o=>n.plannedSpecQuantity=o,placeholder:"\u8BF7\u8F93\u5165\u89C4\u683C\u6570\u91CF"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])]),_:1}),u(a,{label:"\u8BF4\u660E","min-width":"150"},{default:d(({row:n,$index:c})=>[u(s,{prop:`${c}.note`,rules:y(S).note,class:"mb-0px!"},{default:d(()=>[u(i,{modelValue:n.note,"onUpdate:modelValue":o=>n.note=o,placeholder:"\u8BF7\u8F93\u5165\u8BF4\u660E"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])]),_:1}),u(a,{align:"center",fixed:"right",label:"\u64CD\u4F5C",width:"60"},{default:d(({$index:n})=>[u(Q,{icon:"ep:delete",color:"#f56c6c",style:{},onClick:c=>{return o=n,r.value.splice(o,1),void D();var o}},null,8,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1},8,["model","rules"])),[[ia,y(U)]]),u(la,{justify:"center",class:"mt-3"},{default:d(()=>[u(ta,{onClick:X,round:""},{default:d(()=>t[3]||(t[3]=[$a(" + \u6DFB\u52A0\u4EFB\u52A1\u5355\u660E\u7EC6 ")])),_:1})]),_:1})],64)}}}),[["__scopeId","data-v-a204bf3c"]]);export{ja as default};
