import{d as C,aE as E,_ as A,c as B}from"./index-BeQABqnP.js";import{_ as H}from"./main.vue_vue_type_script_setup_true_lang-BOOeL97b.js";import{W as M}from"./main-DQAeP9Mx.js";import{u as P,U as T}from"./useUpload-CEoH0OEO.js";import{i as W,k as S,j as q,f as D,v as F,z as G}from"./form-designer-DQFPUccF.js";import{k as J,c as K,r as v,P as L,l as N,m as V,H as t,z as i,u as o,y as O,C as Q,E as y,h as R}from"./form-create-B86qX0W_.js";import"./index.vue_vue_type_script_setup_true_lang-BjqH9dXd.js";import"./main-UvnCRKF5.js";import"./main-BSQmhXUw.js";import"./index-DqrovjuT.js";import"./index-BC9MuBj8.js";import"./formatTime-CN67D7Gb.js";const X=B(J({__name:"TabVideo",props:{modelValue:{}},emits:["update:modelValue"],setup(h,{emit:b}){const g=C(),I={Authorization:"Bearer "+E()},k=h,z=b,l=K({get:()=>k.modelValue,set:e=>z("update:modelValue",e)}),d=v(!1),m=v([]),r=L({accountId:l.value.accountId,type:"video",title:"",introduction:""}),U=e=>P(T.Video,10)(e),x=e=>{if(e.code!==0)return g.error("\u4E0A\u4F20\u51FA\u9519\uFF1A"+e.msg),!1;m.value=[],r.title="",r.introduction="",p(e.data)},p=e=>{d.value=!1,l.value.mediaId=e.mediaId,l.value.url=e.url,l.value.name=e.name,e.title&&(l.value.title=e.title||""),e.introduction&&(l.value.description=e.introduction||"")};return(e,a)=>{const c=S,s=W,f=A,_=D,j=F,n=q,w=G;return V(),N("div",null,[t(s,null,{default:i(()=>[t(c,{modelValue:o(l).title,"onUpdate:modelValue":a[0]||(a[0]=u=>o(l).title=u),class:"input-margin-bottom",placeholder:"\u8BF7\u8F93\u5165\u6807\u9898"},null,8,["modelValue"]),t(c,{class:"input-margin-bottom",modelValue:o(l).description,"onUpdate:modelValue":a[1]||(a[1]=u=>o(l).description=u),placeholder:"\u8BF7\u8F93\u5165\u63CF\u8FF0"},null,8,["modelValue"]),t(s,{class:"ope-row",justify:"center"},{default:i(()=>[o(l).url?(V(),O(o(H),{key:0,url:o(l).url},null,8,["url"])):Q("",!0)]),_:1}),t(n,null,{default:i(()=>[t(s,{style:{"text-align":"center"},align:"middle"},{default:i(()=>[t(n,{span:12},{default:i(()=>[t(_,{type:"success",onClick:a[2]||(a[2]=u=>d.value=!0)},{default:i(()=>[a[4]||(a[4]=y(" \u7D20\u6750\u5E93\u9009\u62E9 ")),t(f,{icon:"ep:circle-check"})]),_:1}),t(j,{title:"\u9009\u62E9\u89C6\u9891",modelValue:o(d),"onUpdate:modelValue":a[3]||(a[3]=u=>R(d)?d.value=u:null),width:"90%","append-to-body":"","destroy-on-close":""},{default:i(()=>[t(o(M),{type:"video","account-id":o(l).accountId,onSelectMaterial:p},null,8,["account-id"])]),_:1},8,["modelValue"])]),_:1}),t(n,{span:12},{default:i(()=>[t(w,{action:"https://optest.hbfarmx.com/admin-api/mp/material/upload-temporary",headers:I,multiple:"",limit:1,"file-list":o(m),data:o(r),"before-upload":U,"on-success":x},{default:i(()=>[t(_,{type:"primary"},{default:i(()=>[a[5]||(a[5]=y("\u65B0\u5EFA\u89C6\u9891 ")),t(f,{icon:"ep:upload"})]),_:1})]),_:1},8,["file-list","data"])]),_:1})]),_:1})]),_:1})]),_:1})])}}}),[["__scopeId","data-v-e3227e83"]]);export{X as default};
