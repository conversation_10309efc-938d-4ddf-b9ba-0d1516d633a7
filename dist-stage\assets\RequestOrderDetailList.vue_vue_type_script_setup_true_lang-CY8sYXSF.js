import{a as F,d as G,D as _}from"./index-BeQABqnP.js";import{_ as x}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CxohUsTD.js";import{_ as H}from"./DictTag.vue_vue_type_script_lang-B0tTMPJ6.js";import{d as A}from"./formatTime-CN67D7Gb.js";import{q as h,f as Y}from"./formatter-BLTmz7GT.js";import{R as W}from"./index-Ufxn2eGP.js";import{B as Z}from"./index-CH8pxbb6.js";import{W as j}from"./index-B3LrXjfm.js";import{WarehouseApi as J}from"./index-CASHthoJ.js";import{W as V}from"./index-DnvZjr4Y.js";import{U as X}from"./index-BDU5cx5r.js";import{ak as $,Z as ee,_ as ae,a6 as te}from"./form-designer-DQFPUccF.js";import{k as re,r as v,c as S,b as B,e as le,y as w,m as n,z as i,A as ie,u as r,l as c,C as f,G as O,H as t,E as g,F as p,v as ne,n as oe}from"./form-create-B86qX0W_.js";const ue={key:0},de={key:0},pe={key:1},se={key:1},me={key:1},ye=re({__name:"RequestOrderDetailList",props:{bizOrderId:{},bomId:{},detailType:{},warehouseMap:{},locationMap:{},unitMap:{}},setup(D){const{t:ce}=F(),L=G(),l=D,T=v(!1),d=v([]),N=v(new Map),q=v(new Map),R=v(new Map),C=S(()=>l.warehouseMap||N.value),K=S(()=>l.locationMap||q.value),M=S(()=>l.unitMap||R.value),U=v(!1),m=S(()=>l.detailType||"requestOrder"),z=async()=>{T.value=!0;try{if(!l.bizOrderId&&l.detailType!=="bom")return void(d.value=[]);switch(l.detailType){case"requestOrder":default:l.bizOrderId?d.value=await W.getRequestOrderDetailListByBizOrderId(l.bizOrderId):d.value=[];break;case"bom":l.bomId?(d.value=await Z.getBomMaterialListByBomId(l.bomId,""),d.value=d.value.map(o=>({...o,unit:o.materialUnit||o.unit,plannedQuantity:o.quantity||0,fulfilledQuantity:0,readyQuantity:0,lockedQuantity:0}))):d.value=[];break;case"workOrder":if(l.bizOrderId)try{const o=await W.getRequestOrder(l.bizOrderId),u=await j.getWorkOrderPage({orderNo:o.orderNo||""});u&&Array.isArray(u.list)?d.value=u.list:u&&Array.isArray(u)?d.value=u:d.value=[]}catch{d.value=[]}else d.value=[]}await oe()}catch{L.error("\u52A0\u8F7D\u6570\u636E\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5"),d.value=[]}finally{T.value=!1}};B(()=>({bizOrderId:l.bizOrderId,bomId:l.bomId,detailType:l.detailType}),async()=>{(l.bizOrderId||l.bomId)&&(await E(),await z())},{immediate:!1});const E=async()=>{if(!U.value){const o=[];l.warehouseMap||o.push((async()=>{const u=await J.getWarehouseList({pageNo:1,pageSize:100});N.value=new Map(u.map(e=>[e.id,e.name]))})()),l.locationMap||o.push((async()=>{const u=await V.getWarehouseLocationPage({pageNo:1,pageSize:100,warehouseId:void 0});q.value=new Map(u.list.map(e=>[e.id,e.name]))})()),l.unitMap||o.push((async()=>{const u=await X.getUnitPage({pageNo:1,pageSize:100});R.value=new Map(u.list.map(e=>[e.id,e.name]))})()),o.length>0&&await Promise.all(o),U.value=!0}},P=o=>{const{columns:u,data:e}=o,s=[];return u.forEach((b,k)=>{if(k===0)return void(s[k]="\u5408\u8BA1");let I=[];if(I=m.value==="bom"?["quantity"]:m.value==="workOrder"?["orderQuantity","scheduleQuantity"]:["plannedQuantity","fulfilledQuantity","stockQuantity","readyQuantity","lockedQuantity","purchaseQuantity"],I.includes(b.property)){const Q=e.map(a=>Number(a[b.property])||0).reduce((a,y)=>a+y,0);s[k]=Q>0?Y(Q):"0"}else s[k]=""}),s};return B(()=>[l.bizOrderId,l.bomId,l.detailType],async()=>{(l.bizOrderId||l.bomId)&&await z()},{immediate:!1}),le(async()=>{await E(),(l.bizOrderId||l.bomId)&&await z()}),(o,u)=>{const e=ae,s=te,b=H,k=ee,I=x,Q=$;return n(),w(I,null,{default:i(()=>[ie((n(),w(k,{data:r(d),stripe:!0,border:!0,"show-overflow-tooltip":!0,"show-summary":"","summary-method":P},{default:i(()=>[r(m)!=="workOrder"?(n(),c(O,{key:0},[t(e,{label:"\u5E8F\u53F7",align:"center",prop:"num",width:"60"}),t(e,{label:"\u4ED3\u5E93",align:"left",prop:"warehouseId",width:"110"},{default:i(a=>[a.row.warehouseId?(n(),w(s,{key:0},{default:i(()=>{var y;return[g(p(((y=r(C))==null?void 0:y.get(a.row.warehouseId))||"-"),1)]}),_:2},1024)):f("",!0)]),_:1}),t(e,{label:"\u5E93\u4F4D",align:"left",prop:"locationId",width:"60"},{default:i(a=>{var y;return[ne("span",null,p(((y=r(K))==null?void 0:y.get(a.row.locationId))||"-"),1)]}),_:1}),t(e,{label:"\u7269\u6599\u540D\u79F0",align:"left",prop:"materialName",width:"150"}),t(e,{label:"\u7269\u6599\u7F16\u53F7",align:"center",prop:"materialCode",width:"120"}),t(e,{label:"\u89C4\u683C",align:"center",prop:"spec",width:"80"}),t(e,{label:"\u5355\u4F4D",align:"center",prop:"unit",width:"80"},{default:i(a=>{var y;return[a.row.unit?(n(),c("span",ue,p(((y=r(M))==null?void 0:y.get(Number(a.row.unit)))||"-"),1)):f("",!0)]}),_:1})],64)):f("",!0),r(m)!=="workOrder"?(n(),w(e,{key:1,label:"\u9700\u6C42\u6570\u91CF",align:"center",width:"100",prop:r(m)==="bom"?"quantity":"plannedQuantity"},{default:i(a=>[r(m)==="bom"?(n(),c("span",de,p(r(h)(null,null,a.row.quantity||0,null)),1)):(n(),c("span",pe,p(r(h)(null,null,a.row.plannedQuantity||0,null)),1))]),_:1},8,["prop"])):f("",!0),r(m)==="requestOrder"?(n(),c(O,{key:2},[t(e,{label:"\u5DF2\u8F6C\u6570\u91CF",align:"center",prop:"fulfilledQuantity",width:"100",formatter:r(h)},null,8,["formatter"]),t(e,{label:"\u4ED3\u5E93\u6570\u91CF",align:"center",prop:"stockQuantity",width:"100",formatter:r(h)},null,8,["formatter"]),t(e,{label:"\u53EF\u7528\u6570\u91CF",align:"center",prop:"readyQuantity",width:"100",formatter:r(h)},{default:i(a=>[a.row.readyStatus===1?(n(),w(s,{key:0,type:"success"},{default:i(()=>[g(p(a.row.readyQuantity),1)]),_:2},1024)):(n(),w(s,{key:1},{default:i(()=>[g(p(a.row.readyQuantity),1)]),_:2},1024))]),_:1},8,["formatter"]),t(e,{label:"\u5728\u9014\u6570\u91CF",align:"center",prop:"transitQuantity",width:"100",formatter:r(h)},{default:i(a=>[a.row.transitQuantity>0?(n(),w(s,{key:0,type:"success"},{default:i(()=>[g(p(a.row.transitQuantity),1)]),_:2},1024)):(n(),c("span",se,"-"))]),_:1},8,["formatter"]),t(e,{label:"\u5728\u9014\u53EF\u9501",align:"center",prop:"lockTransitQuantity",width:"100",formatter:r(h)},{default:i(a=>[a.row.lockTransitQuantity>0?(n(),w(s,{key:0,type:"success"},{default:i(()=>[g(p(a.row.lockTransitQuantity),1)]),_:2},1024)):(n(),c("span",me,"-"))]),_:1},8,["formatter"]),t(e,{label:"\u51C6\u5907\u72B6\u6001",align:"center",prop:"readyStatus",width:"100"},{default:i(a=>[t(b,{type:r(_).MFG_MATERIAL_READY_STATUS,value:a.row.readyStatus},null,8,["type","value"])]),_:1}),t(e,{label:"\u5DF2\u8F6C\u91C7\u8D2D\u6570\u91CF",align:"center",prop:"purchaseQuantity",width:"110"})],64)):f("",!0),r(m)==="workOrder"?(n(),c(O,{key:3},[t(e,{label:"\u751F\u4EA7\u5355\u53F7",align:"center",prop:"workNo","min-width":"160"}),t(e,{label:"\u4EA7\u54C1\u540D\u79F0",align:"center",prop:"productName","min-width":"150"}),t(e,{label:"\u89C4\u683C",align:"center",prop:"spec","min-width":"120"}),t(e,{label:"\u8BA2\u5355\u6570\u91CF",align:"center",prop:"orderQuantity","min-width":"120"},{default:i(a=>[t(s,null,{default:i(()=>[g(p(a.row.orderQuantity||0)+" "+p(r(M).get(Number(a.row.orderUnit))),1)]),_:2},1024)]),_:1}),t(e,{label:"\u8BA1\u5212\u6570\u91CF",align:"center",prop:"scheduleQuantity","min-width":"120"},{default:i(a=>[t(s,null,{default:i(()=>[g(p(a.row.scheduleQuantity||0)+" "+p(r(M).get(Number(a.row.orderUnit))),1)]),_:2},1024)]),_:1}),t(e,{label:"\u72B6\u6001",align:"center",prop:"status","min-width":"100"},{default:i(a=>[t(b,{type:r(_).WORK_ORDER_STATUS,value:a.row.status},null,8,["type","value"])]),_:1}),t(e,{label:"\u8BA1\u5212\u5F00\u59CB",align:"center",prop:"scheduleStartTime",formatter:r(A),"min-width":"160"},null,8,["formatter"]),t(e,{label:"\u8BA1\u5212\u7ED3\u675F",align:"center",prop:"scheduleEndTime",formatter:r(A),"min-width":"160"},null,8,["formatter"]),t(e,{label:"\u4EA4\u671F",align:"center",prop:"deliverDate",formatter:r(A),"min-width":"160"},null,8,["formatter"]),t(e,{label:"\u9886\u6599\u72B6\u6001",align:"center",prop:"pickingStatus","min-width":"100"},{default:i(a=>[t(b,{type:r(_).COMMON_TASK_STATUS,value:a.row.pickingStatus},null,8,["type","value"])]),_:1}),t(e,{label:"\u5165\u5E93\u72B6\u6001",align:"center",prop:"inStockStatus","min-width":"100"},{default:i(a=>[t(b,{type:r(_).COMMON_TASK_STATUS,value:a.row.inStockStatus},null,8,["type","value"])]),_:1}),t(e,{label:"\u62A5\u5DE5\u72B6\u6001",align:"center",prop:"reportStatus","min-width":"100"},{default:i(a=>[t(b,{type:r(_).COMMON_TASK_STATUS,value:a.row.reportStatus},null,8,["type","value"])]),_:1}),t(e,{label:"\u5907\u6CE8",align:"center",prop:"remark","min-width":"150"})],64)):f("",!0),r(m)==="bom"?(n(),w(e,{key:4,label:"\u635F\u8017\u7387",align:"center",prop:"lossRate",width:"80"})):f("",!0),r(m)!=="workOrder"?(n(),c(O,{key:5},[t(e,{label:"\u5907\u6CE8",align:"center",prop:"remark",width:"100"}),t(e,{label:"\u8BF4\u660E",align:"center",prop:"note"}),t(e,{label:"\u6279\u53F7",align:"center",prop:"batchNo"})],64)):f("",!0),r(m)==="workOrder"?(n(),c(O,{key:6},[],64)):f("",!0)]),_:1},8,["data"])),[[Q,r(T)]])]),_:1})}}});export{ye as _};
