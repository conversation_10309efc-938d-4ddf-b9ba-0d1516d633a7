<template>
  <!-- 统一内容卡片 -->
  <ContentWrap>
    <!-- Tab 切换 -->
    <el-tabs v-model="subTabsName">
      <el-tab-pane label="待出库" name="pendingDelivery" />

      <el-tab-pane label="已出库" name="delivered" />
    </el-tabs>

    <!-- 已出库表格 -->
    <div v-if="subTabsName === 'delivered'">
      <!-- 搜索表单卡片 -->
      <el-card shadow="hover" class="mb-4">
        <!-- 搜索工作栏 -->
        <el-form
          class="-mb-15px"
          :model="queryParams"
          ref="queryFormRef"
          :inline="true"
          label-width="auto"
        >
          <!-- 基础搜索项 - 始终显示 -->
          <el-form-item label="单号" prop="orderNo">
            <el-input
              v-model="queryParams.orderNo"
              placeholder="请输入单号"
              clearable
              @keyup.enter="handleQuery"
              class="!w-240px"
            />
          </el-form-item>
          <el-form-item label="来源类型" prop="sourceType">
            <el-select
              v-model="queryParams.sourceType"
              placeholder="请选择来源类型"
              clearable
              class="!w-240px"
            >
              <el-option
              v-for="item in material_source"
              :key="item.value"
              :label="item.label"
              :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="客户名称" prop="objectName">
            <el-input
              v-model="queryParams.objectName"
              placeholder="请输入客户名称"
              clearable
              @keyup.enter="handleQuery"
              class="!w-240px"
            />
          </el-form-item>

          <!-- 高级搜索项 - 可展开收起 -->
          <template v-if="isExpanded">
<!--        <el-form-item label="业务类型" prop="bizType">-->
<!--          <el-select-->
<!--            v-model="queryParams.bizType"-->
<!--            placeholder="请选择业务类型"-->
<!--            clearable-->
<!--            class="!w-240px"-->
<!--          >-->
<!--            <el-option-->
<!--              v-for="item in inventory_transaction_type"-->
<!--              :key="item.value"-->
<!--              :label="item.label"-->
<!--              :value="item.value"-->
<!--            />-->
<!--          </el-select>-->
<!--        </el-form-item>-->
            <el-form-item label="来源单编号" prop="sourceNo">
              <el-input
                v-model="queryParams.sourceNo"
                placeholder="请输入来源单编号"
                clearable
                @keyup.enter="handleQuery"
                class="!w-240px"
              />
            </el-form-item>
            <el-form-item label="交易对象订单号" prop="objectOrderNo">
              <el-input
                v-model="queryParams.objectOrderNo"
                placeholder="请输入交易对象订单号"
                clearable
                @keyup.enter="handleQuery"
                class="!w-240px"
              />
            </el-form-item>
            <el-form-item label="交易日期" prop="date">
              <el-date-picker
                v-model="queryParams.date"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
                class="!w-240px"
              />
            </el-form-item>
            <!-- <el-form-item label="摘要" prop="note">
              <el-input
                v-model="queryParams.note"
                placeholder="请输入摘要"
                clearable
                @keyup.enter="handleQuery"
                class="!w-240px"
              />
            </el-form-item>
            <el-form-item label="备注" prop="remark">
              <el-input
                v-model="queryParams.remark"
                placeholder="请输入备注"
                clearable
                @keyup.enter="handleQuery"
                class="!w-240px"
              />
            </el-form-item> -->
            <el-form-item label="创建时间" prop="createTime">
              <el-date-picker
                v-model="queryParams.createTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
                class="!w-240px"
              />
            </el-form-item>
            <el-form-item label="审批状态" prop="approveStatus">
              <el-select
                v-model="queryParams.approveStatus"
                placeholder="请选择审批状态"
                clearable
                class="!w-240px"
              >
                <el-option
                v-for="item in approve_status"
                :key="item.value"
                :label="item.label"
                :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="审批单号" prop="approveNo">
              <el-input
                v-model="queryParams.approveNo"
                placeholder="请输入审批单号"
                clearable
                @keyup.enter="handleQuery"
                class="!w-240px"
              />
            </el-form-item>
            <el-form-item label="审批人" prop="approverName">
              <el-input
                v-model="queryParams.approverName"
                placeholder="请输入审批人"
                clearable
                @keyup.enter="handleQuery"
                class="!w-240px"
              />
            </el-form-item>
            <el-form-item label="审批时间" prop="approveDate">
              <el-date-picker
                v-model="queryParams.approveDate"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
                class="!w-240px"
              />
            </el-form-item>
          </template>

          <!-- 操作按钮行 -->
          <el-form-item>
            <el-button @click="handleQuery" class="ml-4"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
            <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
            <el-button
              type="primary"
              plain
              @click="openForm('create')"
              v-hasPermi="['inventory:delivery-receipt:create']"
            >
              <Icon icon="ep:plus" class="mr-5px" /> 新增
            </el-button>
            <el-button
              type="success"
              plain
              @click="handleExport"
              :loading="exportLoading"
              v-hasPermi="['inventory:delivery-receipt:export']"
            >
              <Icon icon="ep:download" class="mr-5px" /> 导出
            </el-button>
            <el-button
              type="primary"
              plain
              @click="handleApproval"
              :loading="exportLoading"
              v-hasPermi="['inventory:delivery-receipt:approve']"
            >
              <Icon icon="ep:check" class="mr-5px" /> 审核
            </el-button>
            <el-button
              type="text"
              @click="toggleExpanded"
              class="ml-2"
            >
              {{ isExpanded ? '收起' : '展开' }}
              <Icon :icon="isExpanded ? 'ep:arrow-up' : 'ep:arrow-down'" class="ml-1" />
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 表格区域 -->
      <el-table
        v-loading="loading"
        :data="flattenedList"
        :stripe="true"
        border
        :show-overflow-tooltip="true"
        highlight-current-row
        show-summary
        :summary-method="summaryMethod"
        @current-change="handleCurrentChange"
        @selection-change="handleSelectionChange"
        :span-method="objectSpanMethod"
        height="600"
        style="width: 100%"
      >
        <el-table-column type="selection" width="60" fixed="left" />
        <!-- 最重要的主单据信息 -->
        <el-table-column label="单号" align="left" prop="orderNo" width="180px" fixed="left">
          <template #default="scope">
            <div class="order-no-container">
              <div class="order-no-cell">
                <div class="order-no-content">
                  <el-button
                    link
                    type="primary"
                    @click="handleDetail(scope.row.id)"
                    class="order-no-link"
                  >
                    {{ scope.row.orderNo }}
                  </el-button>
                </div>
                <el-button
                  link
                  type="info"
                  @click="copyOrderNo(scope.row.orderNo)"
                  class="copy-btn copy-btn-fixed"
                  size="small"
                >
                  <Icon icon="ep:copy-document" :size="12"/>
                </el-button>
              </div>
              <dict-tag
                v-if="scope.row.approveStatus"
                :type="DICT_TYPE.APPROVE_STATUS"
                :value="scope.row.approveStatus"
                class="status-tag"
              />
            </div>
          </template>
        </el-table-column>
<!--          <el-table-column label="业务类型" align="left" prop="bizType" width="100px" fixed="left">-->
<!--            <template #default="scope">-->
<!--              {{ getDictLabel('INVENTORY_TRANSACTION_TYPE', scope.row.bizType) }}-->
<!--            </template>-->
<!--          </el-table-column>-->

        <!-- 核心明细信息 - 最重要的业务数据 -->
        <!-- <el-table-column label="序号" align="center" prop="detail.num" width="60px" /> -->
        <el-table-column label="物料名称" align="left" prop="detail.materialName" width="180px" fixed="left"/>
        <el-table-column label="物料编号" align="left" prop="detail.materialCode" width="120px" />
        <el-table-column label="实发数量" align="right" prop="detail.fulfilledQuantity" width="100px">
          <template #default="scope">
            {{ formatQuantity(scope.row.detail.fulfilledQuantity) }}{{ getUnitName(scope.row.detail?.unit) || scope.row.detail?.unit || '' }}
          </template>
        </el-table-column>
        <el-table-column label="应发数量" align="right" prop="detail.plannedQuantity" width="110px">
          <template #default="scope">
            {{ formatQuantity(scope.row.detail.plannedQuantity) }}{{ getUnitName(scope.row.detail?.unit) || scope.row.detail?.unit || '' }}
          </template>
        </el-table-column>
        <!-- <el-table-column label="单位" align="center" width="80px">
          <template #default="scope">
          </template>
        </el-table-column> -->
        <el-table-column label="单价" align="right" prop="detail.unitPrice" width="100px">
          <template #default="scope">
            {{ formatAmount(scope.row.detail.unitPrice) }}
          </template>
        </el-table-column>
        <el-table-column label="金额" align="right" prop="detail.amount" width="120px">
          <template #default="scope">
            {{ formatAmount(scope.row.detail.amount) }}
          </template>
        </el-table-column>
        <el-table-column label="批号" align="left" prop="detail.batchNo" width="100px"/>



<!--          &lt;!&ndash; 重要的主单据业务信息 &ndash;&gt;-->
<!--          <el-table-column label="来源类型" align="center" prop="sourceType" width="100px">-->
<!--            <template #default="scope">-->
<!--              {{ getDictLabel('MATERIAL_SOURCE', scope.row.sourceType) }}-->
<!--            </template>-->
<!--          </el-table-column>-->
<!--          <el-table-column label="来源单编号" align="left" prop="sourceNo" width="120px"/>-->
        <el-table-column label="客户名称" align="left" prop="objectName" width="150px"/>
        <el-table-column
          label="交易日期"
          align="center"
          prop="date"
          :formatter="dateFormatter2"
          width="100px"
        />

        <!-- 税务和财务信息 -->
        <el-table-column label="含税单价" align="right" prop="detail.taxPrice" width="100px">
          <template #default="scope">
            {{ formatAmount(scope.row.detail.taxPrice) }}
          </template>
        </el-table-column>
        <el-table-column label="含税金额" align="right" prop="detail.taxAmount" width="110px">
          <template #default="scope">
            {{ formatAmount(scope.row.detail.taxAmount) }}
          </template>
        </el-table-column>

        <!-- 基本单位信息 -->
        <!-- <el-table-column label="基本单位" align="center" width="100px">
          <template #default="scope">
            {{ getUnitName(scope.row.detail?.standardUnit) || scope.row.detail?.standardUnit || '' }}
          </template>
        </el-table-column> -->
        <!-- <el-table-column label="基本单位实发数量" align="right" prop="detail.standardFulfilledQuantity" width="140px">
          <template #default="scope">
            {{ formatQuantity(scope.row.detail.standardFulfilledQuantity) }}
          </template>
        </el-table-column> -->
        <!-- <el-table-column label="基本单位应发数量" align="right" prop="detail.standardPlannedQuantity" width="140px">
          <template #default="scope">
            {{ formatQuantity(scope.row.detail.standardPlannedQuantity) }}
          </template>
        </el-table-column> -->

        <!-- 开票信息 -->
        <!-- <el-table-column label="开票数量" align="right" prop="detail.invoiceQuantity" width="100px">
          <template #default="scope">
            {{ formatQuantity(scope.row.detail.invoiceQuantity) }}
          </template>
        </el-table-column>
        <el-table-column label="开票金额" align="right" prop="detail.invoiceAmount" width="100px">
          <template #default="scope">
            {{ formatAmount(scope.row.detail.invoiceAmount) }}
          </template>
        </el-table-column> -->

        <!-- 日期信息 -->
        <el-table-column
          label="生产日期"
          align="center"
          prop="detail.effictiveDate"
          :formatter="dateFormatter2"
          width="100px"
        />
        <el-table-column
          label="失效日期"
          align="center"
          prop="detail.expiryDate"
          :formatter="dateFormatter2"
          width="100px"
        />

        <!-- 其他主单据信息 -->
        <el-table-column label="交易对象订单号" align="left" prop="objectOrderNo" width="140px">
          <template #default="scope">
            <div class="order-no-cell" v-if="scope.row.objectOrderNo">
              <div class="order-no-content">
                <span>{{ scope.row.objectOrderNo }}</span>
              </div>
              <el-button
                link
                type="info"
                @click="copyOrderNo(scope.row.objectOrderNo)"
                class="copy-btn copy-btn-fixed"
                size="small"
              >
                <Icon icon="ep:copy-document" :size="12"/>
              </el-button>
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="摘要" align="left" prop="note" width="120px"/>
        <el-table-column label="备注" align="left" prop="remark" width="120px"/>
        <el-table-column
          label="创建时间"
          align="center"
          prop="createTime"
          :formatter="dateFormatter"
          width="180px"
        />

        <!-- 详细的明细信息 -->
        <el-table-column label="开票基本数量" align="right" prop="detail.standardInvoiceQuantity" width="120px">
          <template #default="scope">
            {{ formatQuantity(scope.row.detail.standardInvoiceQuantity) }}
          </template>
        </el-table-column>
        <el-table-column label="明细备注" align="left" prop="detail.remark" width="120px"/>
        <el-table-column label="说明" align="left" prop="detail.note" width="120px"/>

        <!-- 系统和标识信息 -->
        <el-table-column label="源单单号" align="left" prop="detail.sourceNo" width="120px">
          <template #default="scope">
            <div class="order-no-cell" v-if="scope.row.detail.sourceNo">
              <div class="order-no-content">
                <span>{{ scope.row.detail.sourceNo }}</span>
              </div>
              <el-button
                link
                type="info"
                @click="copyOrderNo(scope.row.detail.sourceNo)"
                class="copy-btn copy-btn-fixed"
                size="small"
              >
                <Icon icon="ep:copy-document" :size="12"/>
              </el-button>
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <!-- <el-table-column label="成本对象编码" align="left" prop="detail.costObjectId" width="120px"/>
        <el-table-column label="成本对象名称" align="left" prop="detail.costObjectName" width="120px"/>
        <el-table-column label="记账凭证号" align="left" prop="detail.accountingVoucherNumber" width="120px"/>
        <el-table-column
          label="明细创建时间"
          align="center"
          prop="detail.createTime"
          :formatter="dateFormatter"
          width="180px"
        /> -->
        <el-table-column label="操作" align="center" min-width="150px" fixed="right">
          <template #default="scope">

            <el-button
              link
              type="primary"
              @click="openForm('update', scope.row.id)"
              v-hasPermi="['inventory:delivery-receipt:update']"
              v-if="scope.row.approveStatus !== '3'"
            >
              编辑
            </el-button>
            <el-button
              link
              type="danger"
              @click="handleDelete(scope.row.id)"
              v-hasPermi="['inventory:delivery-receipt:delete']"
              v-if="scope.row.approveStatus !== '3'"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <Pagination
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 待出库表格 -->
    <div v-if="subTabsName === 'pendingDelivery'">
      <Pending @open-delivery-form="handleOpenDeliveryForm" />
    </div>
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <DeliveryReceiptForm ref="formRef" @success="getList" />
  <!-- 审核弹窗 -->
  <ApproveInfoForm ref="approveInfoFormRef" @success="getList" :biz-id="currentRow?.id" biz-type="delivery_receipt" :biz-no="currentRow?.orderNo"/>
</template>

<script setup lang="ts">
import { dateFormatter, dateFormatter2 } from '@/utils/formatTime'
import download from '@/utils/download'
import { DeliveryReceiptApi, DeliveryReceiptVO } from '@/api/scm/inventory/deliveryreceipt'
import { getRemoteUnit } from '@/utils/commonBiz'
import DeliveryReceiptForm from './DeliveryReceiptForm.vue'
import ApproveInfoForm from '../../base/approveinfo/ApproveInfoForm.vue'
import { DICT_TYPE,getStrDictOptions } from '@/utils/dict'
import { amountTableFormatter, quantityTableFormatter, formatAmount, formatQuantity } from '@/utils/formatter'
import { useRouter } from 'vue-router'
import Pending from '@/views/scm/inventory/deliveryreceipt/pending.vue'
import { useClipboard } from '@vueuse/core'
/** 销售出库 列表 */
defineOptions({ name: 'DeliveryReceipt' })

const message = useMessage() // 消息弹窗gei
const { t } = useI18n() // 国际化
const router = useRouter() // 路由
const { copy } = useClipboard() // 复制功能

/** 复制订单单号 */
const copyOrderNo = async (orderNo: string) => {
  try {
    await copy(orderNo)
    message.success('单号复制成功')
  } catch (error) {
    message.error('复制失败')
  }
}
const subTabsName = ref('pendingDelivery')
const loading = ref(true) // 列表的加载中
const list = ref<(DeliveryReceiptVO & { deliveryReceiptDetails?: any[], details?: any[] })[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const spanArr = ref<number[]>([]) // 行合并数组
const currentRow = ref() // 当前选中行
const unitMap = ref<Map<number, string>>(new Map()) // 单位ID到名称的映射
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  orderNo: undefined,
  bizType: undefined,
  sourceType: undefined,
  sourceId: undefined,
  sourceNo: undefined,
  objectId: undefined,
  objectName: undefined,
  objectOrderNo: undefined,
  date: [],
  warehouseId: undefined,
  accountId: undefined,
  note: undefined,
  remark: undefined,
  createTime: [],
  approveStatus: undefined,
  approveNo: undefined,
  approverId: undefined,
  approverName: undefined,
  approveDate: [],
  deptId: undefined,
  empId: undefined,
  managerId: undefined,
  manger1Id: undefined,
  accountantId: undefined,
  checkerId: undefined,
  detail:true
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const isExpanded = ref(false) // 表单展开状态
const selectedRows = ref<(DeliveryReceiptVO & { deliveryReceiptDetails?: any[], details?: any[] })[]>([]) // 选中的行
const approveInfoFormRef = ref() // 审核弹窗引用
const approve_status = getStrDictOptions(DICT_TYPE.APPROVE_STATUS)
const material_source = getStrDictOptions(DICT_TYPE.MATERIAL_SOURCE)
const inventory_transaction_type = getStrDictOptions(DICT_TYPE.INVENTORY_TRANSACTION_TYPE)

/** 扁平化数据 - 将主单据和明细数据合并 */
const flattenedList = computed(() => {
  const result: any[] = []
  spanArr.value = [] // 每次重新计算时清空旧数据

  list.value.forEach(order => {
    const details = order.deliveryReceiptDetails?.length ? order.deliveryReceiptDetails : [{}] // 确保无明细时也有占位行
    const detailCount = details.length

    details.forEach((detail: any, index: number) => {
      result.push({ ...order, detail })
      // 主信息列只在第一个明细行合并，合并行数=明细数量
      spanArr.value.push(index === 0 ? detailCount : 0)
    })
  })

  return result
})

/** 行合并方法 */
const objectSpanMethod = ({ row, column, rowIndex }: any) => {
  // 需要合并的主信息列 - 移除审批编号和审批人字段
  const mergeFields = ['orderNo', 'bizType', 'sourceType', 'sourceNo', 'objectName', 'date', 'approveStatus', 'objectOrderNo', 'note', 'remark', 'createTime']

  // 检查是否是操作列（通过列标签判断）
  const isOperationColumn = column.label === '操作'

  // 检查是否是选择列（通过列类型判断）
  const isSelectionColumn = column.type === 'selection'

  if (mergeFields.includes(column.property) || isOperationColumn || isSelectionColumn) {
    const span = spanArr.value[rowIndex]
    if (span > 0) {
      return {
        rowspan: span,
        colspan: 1
      }
    } else {
      return {
        rowspan: 0,
        colspan: 0
      }
    }
  }
}

/** 当前行变化 */
const handleCurrentChange = (row: any) => {
  if (row) {
    currentRow.value = row
  }
}

/** 选择变化处理 */
const handleSelectionChange = (selection: (DeliveryReceiptVO & { deliveryReceiptDetails?: any[], details?: any[] })[]) => {
  selectedRows.value = selection
}

/** 切换表单展开状态 */
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
}

/** 加载单位数据 */
const loadUnits = async () => {
  try {
    // 批量获取所有单位信息
    const units = await getRemoteUnit()

    if (!units || units.length === 0) {
      return
    }

    // 建立单位映射
    units.forEach((unit: any) => {
      if (unit && unit.id && unit.name) {
        unitMap.value.set(unit.id, unit.name)
      }
    })
  } catch (error) {
    console.error('加载单位数据失败:', error)
  }
}

/** 获取单位名称 */
const getUnitName = (unitId: number | string) => {
  if (!unitId) return ''
  const id = typeof unitId === 'string' ? parseInt(unitId) : unitId
  const unitName = unitMap.value.get(id)
  return unitName || unitId.toString()
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await DeliveryReceiptApi.getDeliveryReceiptPage(queryParams)
    list.value = data.list
    total.value = data.total

    // 直接使用列表中的 details 字段作为明细数据
    list.value.forEach(order => {
      // 将 details 字段赋值给 deliveryReceiptDetails，保持原有的属性名
      order.deliveryReceiptDetails = order.details || []
    })
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 跳转到详情页面 */
const handleDetail = (id: number) => {
  router.push(`/scm/inventory/deliveryreceipt/detail/${id}`)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await DeliveryReceiptApi.deleteDeliveryReceipt(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await DeliveryReceiptApi.exportDeliveryReceipt(queryParams)
    download.excel(data, '销售出库.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 表格汇总方法 */
const summaryMethod = ({ columns, data }: { columns: any[], data: any[] }) => {
  const sums: any[] = []

  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }

    // 需要汇总的数量字段
    const quantityFields = ['detail.fulfilledQuantity', 'detail.plannedQuantity',
                           'detail.standardFulfilledQuantity', 'detail.standardPlannedQuantity',
                           'detail.invoiceQuantity', 'detail.standardInvoiceQuantity']

    // 需要汇总的金额字段
    const amountFields = ['detail.unitPrice', 'detail.amount', 'detail.taxPrice',
                         'detail.taxAmount', 'detail.invoiceAmount']

    if (quantityFields.includes(column.property)) {
      // 数量字段汇总
      const values = data.map(item => {
        const value = column.property.split('.').reduce((obj, key) => obj?.[key], item)
        return Number(value) || 0
      })
      const total = values.reduce((prev, curr) => prev + curr, 0)
      sums[index] = formatQuantity(total)
    } else if (amountFields.includes(column.property)) {
      // 金额字段汇总
      const values = data.map(item => {
        const value = column.property.split('.').reduce((obj, key) => obj?.[key], item)
        return Number(value) || 0
      })
      const total = values.reduce((prev, curr) => prev + curr, 0)
      sums[index] = formatAmount(total)
    } else {
      // 其他字段不汇总
      sums[index] = ''
    }
  })

  return sums
}

/** 审核按钮操作 */
const handleApproval = async () => {
  console.log('handleApproval', selectedRows.value)
  if(!selectedRows.value || selectedRows.value.length === 0) {
    message.error('请选择要审核的发货出库单！')
    return
  }

  // 如果选中了多个发货出库单，提示用户
  if(selectedRows.value.length > 1) {
    message.warning('当前只支持单个发货出库单审核，请选择一个发货出库单进行审核')
    return
  }

  // 设置当前行为选中的第一个发货出库单
  const selectedOrder = selectedRows.value[0]
  currentRow.value = selectedOrder

  // 发起审批，传入业务数据
  approveInfoFormRef.value.open('approve', undefined, {
    bizId: selectedOrder.id,
    bizNo: selectedOrder.orderNo,
    bizType: 'delivery_receipt'
  })
}

/** 处理待出库表格的出库操作 */
const handleOpenDeliveryForm = (eventData: any) => {
  // 打开出库单新增表单，传递来源数据
  console.log(eventData)
  formRef.value.open('create', undefined, eventData.sourceData)
}

/** 初始化 **/
onMounted(async () => {
  // 先加载单位数据
  await loadUnits()
  // 再加载列表数据
  getList()
})
</script>

<style scoped>
.order-no-link {
  font-weight: 500;
  padding: 0;
  height: auto;
  line-height: 1.4;
}

.order-no-link:hover {
  text-decoration: underline;
}

/* 单号容器样式 */
.order-no-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
  padding: 4px 0;
}

/* 订单号行布局 */
.order-no-cell {
  display: flex;
  align-items: center;
  width: 100%;
  min-height: 20px;
  gap: 5px; /* 文字和图标之间保持一点点间隔 */
}

.order-no-content {
  flex: 0 1 auto; /* 改为自适应宽度，不占满剩余空间 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: calc(100% - 16px); /* 为图标预留空间 */
}

/* 复制按钮样式 */
.copy-btn {
  padding: 2px !important;
  height: 20px !important;
  min-height: 20px !important;
  width: 20px !important;
  font-size: 12px;
  opacity: 0.6;
  transition: opacity 0.2s;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}



.copy-btn:hover {
  opacity: 1;
}

/* 状态标签样式 */
.status-tag :deep(.el-tag) {
  font-size: 9px !important;
  padding: 1px 4px !important;
  height: 16px !important;
  line-height: 14px !important;
  border-radius: 6px !important;
  font-weight: 500 !important;
  flex-shrink: 0;
}
</style>
