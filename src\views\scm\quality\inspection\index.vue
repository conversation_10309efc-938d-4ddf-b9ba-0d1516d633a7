<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="auto"
    >
      <el-form-item label="质检单号" prop="inspectionCode">
        <el-input
          v-model="queryParams.inspectionCode"
          placeholder="请输入质检单号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="来源类型" prop="sourceType">
        <el-select
          v-model="queryParams.sourceType"
          placeholder="请选择来源类型"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.INSPECT_SOURCE_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="来源单号" prop="sourceCode">
        <el-input
          v-model="queryParams.sourceCode"
          placeholder="请输入来源单号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <!-- 可展开收起的表单项 -->
      <template v-if="isExpanded">
        <el-form-item label="质检人员" prop="inspector">
          <el-input
            v-model="queryParams.inspector"
            placeholder="请输入质检人员"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="销售单号" prop="salesOrderCode">
        <el-input
          v-model="queryParams.salesOrderCode"
          placeholder="请输入销售单号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
        <el-form-item label="质检日期" prop="inspectionDate">
          <el-date-picker
            v-model="queryParams.inspectionDate"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select
            v-model="queryParams.status"
            placeholder="请选择状态"
            clearable
            class="!w-240px"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.INSPECT_STATUS)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="质检结果" prop="result">
          <el-select
            v-model="queryParams.result"
            placeholder="请选择质检结果"
            clearable
            class="!w-240px"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.INSPECT_RESULT)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间" prop="createTime">
          <el-date-picker
            v-model="queryParams.createTime"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-240px"
          />
        </el-form-item>
      </template>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['quality:inspection:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['quality:inspection:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
        <el-button
          type="text"
          @click="toggleExpand"
          class="ml-2"
        >
          {{ isExpanded ? '收起' : '展开' }}
          <Icon :icon="isExpanded ? 'ep:arrow-up' : 'ep:arrow-down'" class="ml-1" />
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <!-- 子表的列表 -->
      <el-table-column type="expand">
        <template #default="scope">
          <el-tabs model-value="inspectionItem">
            <el-tab-pane label="质检明细" name="inspectionItem">
              <InspectionItemList :inspection-id="scope.row.id" />
            </el-tab-pane>
          </el-tabs>
        </template>
      </el-table-column>
      <!-- <el-table-column label="ID" align="center" prop="id" /> -->
      <el-table-column label="质检单号" align="center" prop="inspectionCode" width="130"/>
      <el-table-column label="来源类型" align="center" prop="sourceType" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.INSPECT_SOURCE_TYPE" :value="scope.row.sourceType" />
        </template>
      </el-table-column>
      <el-table-column label="来源单号" align="center" prop="sourceCode" width="150">
        <template #default="scope">
          <div class="order-no-cell" v-if="scope.row.sourceCode">
            <div class="order-no-content">
              <span>{{ scope.row.sourceCode }}</span>
            </div>
            <el-button
              link
              type="info"
              @click="copyOrderNo(scope.row.sourceCode)"
              class="copy-btn copy-btn-fixed"
              size="small"
            >
              <Icon icon="ep:copy-document" :size="12"/>
            </el-button>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="销售单号" align="center" prop="salesOrderCode" />
      <el-table-column label="质检人员" align="center" prop="inspector" />
      <el-table-column
        label="质检日期"
        align="center"
        prop="inspectionDate"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.INSPECT_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="质检结果" align="center" prop="result">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.INSPECT_RESULT" :value="scope.row.result" />
        </template>
      </el-table-column>
      <el-table-column label="质检备注" align="center" prop="remark" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" min-width="120px" fixed="right">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['quality:inspection:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['quality:inspection:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <InspectionForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import {getIntDictOptions, DICT_TYPE, getStrDictOptions} from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { InspectionApi, InspectionVO } from '@/api/scm/quality/inspection'
import InspectionForm from './InspectionForm.vue'
import InspectionItemList from './components/InspectionItemList.vue'
import { useClipboard } from '@vueuse/core'

/** 质检单 列表 */
defineOptions({ name: 'Inspection' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const { copy } = useClipboard() // 复制功能

const loading = ref(true) // 列表的加载中
const list = ref<InspectionVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  inspectionCode: undefined,
  sourceType: undefined,
  sourceId: undefined,
  sourceCode: undefined,
  salesOrderId: undefined,
  salesOrderCode: undefined,
  inspectorId: undefined,
  inspector: undefined,
  inspectionDate: [],
  status: undefined,
  result: undefined,
  remark: undefined,
  createTime: [],
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const isExpanded = ref(false) // 搜索表单展开状态

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await InspectionApi.getInspectionPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 切换展开收起状态 */
const toggleExpand = () => {
  isExpanded.value = !isExpanded.value
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await InspectionApi.deleteInspection(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await InspectionApi.exportInspection(queryParams)
    download.excel(data, '质检单.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 复制单号 */
const copyOrderNo = async (orderNo: string) => {
  try {
    await copy(orderNo)
    message.success('单号复制成功')
  } catch (error) {
    message.error('复制失败')
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>

<style scoped>
/* 复制按钮样式 */
.order-no-cell {
  display: flex;
  align-items: center;
  width: 100%;
  min-height: 20px;
  gap: 5px; /* 文字和图标之间保持一点点间隔 */
}

.order-no-content {
  flex: 0 1 auto; /* 改为自适应宽度，不占满剩余空间 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: calc(100% - 16px); /* 为图标预留空间 */
}

.copy-btn {
  padding: 2px !important;
  height: 20px !important;
  min-height: 20px !important;
  width: 20px !important;
  min-width: 20px !important;
  opacity: 0.7;
  transition: opacity 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.copy-btn:hover {
  opacity: 1;
}
</style>
