import{aM as C,ax as q,aL as R,ay as Z}from"./index-BeQABqnP.js";import{P as J}from"./index-COu9YF6c.js";import{W as K}from"./index-GJzpvaVt.js";import{S as O}from"./index-BGb7xQiJ.js";import{ak as T,h as X,Z as Y,_ as ee,aj as ae,x as le,Q as oe,k as te,l as de,f as ue,i as re}from"./form-designer-DQFPUccF.js";import{k as ie,r as h,P as se,b as B,e as ne,l as k,m as c,G as U,A as me,y as v,C as pe,u as s,z as l,H as e,$ as E,E as L}from"./form-create-B86qX0W_.js";const ce=ie({__name:"StockCheckItemForm",props:{items:{},disabled:{type:Boolean}},setup(j,{expose:G}){const W=j,z=h(!1),m=h([]),V=se({inId:[{required:!0,message:"\u76D8\u70B9\u7F16\u53F7\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],warehouseId:[{required:!0,message:"\u4ED3\u5E93\u540D\u5B57\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],productId:[{required:!0,message:"\u4EA7\u54C1\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],count:[{required:!0,message:"\u4EA7\u54C1\u6570\u91CF\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),I=h([]),g=h([]),x=h([]),P=h(void 0);B(()=>W.items,async t=>{m.value=t},{immediate:!0}),B(()=>m.value,t=>{t&&t.length!==0&&t.forEach(d=>{d.stockCount!=null&&d.actualCount!=null?d.count=d.actualCount-d.stockCount:d.count=void 0,d.totalPrice=Z(d.productPrice,d.count)})},{deep:!0});const A=t=>{const{columns:d,data:i}=t,p=[];return d.forEach((f,r)=>{if(r!==0)if(["count","totalPrice"].includes(f.property)){const n=R(i.map(w=>Number(w[f.property])));p[r]=f.property==="count"?C(n):q(n)}else p[r]="";else p[r]="\u5408\u8BA1"}),p},y=()=>{var d;const t={id:void 0,warehouseId:(d=P.value)==null?void 0:d.id,productId:void 0,productUnitName:void 0,productBarCode:void 0,productPrice:void 0,stockCount:void 0,actualCount:void 0,count:void 0,totalPrice:void 0,remark:void 0};m.value.push(t)},$=async t=>{if(!t.productId||!t.warehouseId)return;const d=await O.getStock2(t.productId,t.warehouseId);t.stockCount=d?d.count:0,t.actualCount=t.stockCount};return G({validate:()=>I.value.validate()}),ne(async()=>{g.value=await J.getProductSimpleList(),x.value=await K.getWarehouseSimpleList(),P.value=x.value.find(t=>t.defaultStatus),m.value.length===0&&y()}),(t,d)=>{const i=ee,p=oe,f=le,r=ae,n=te,w=de,N=ue,F=Y,H=X,M=re,Q=T;return c(),k(U,null,[me((c(),v(H,{ref_key:"formRef",ref:I,model:s(m),rules:s(V),"label-width":"0px","inline-message":!0,disabled:t.disabled},{default:l(()=>[e(F,{data:s(m),"show-summary":"","summary-method":A,class:"-mt-10px"},{default:l(()=>[e(i,{label:"\u5E8F\u53F7",type:"index",align:"center",width:"60"}),e(i,{label:"\u4ED3\u5E93\u540D\u5B57","min-width":"125"},{default:l(({row:a,$index:u})=>[e(r,{prop:`${u}.warehouseId`,rules:s(V).warehouseId,class:"mb-0px!"},{default:l(()=>[e(f,{modelValue:a.warehouseId,"onUpdate:modelValue":o=>a.warehouseId=o,clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u4ED3\u5E93\u540D\u5B57",onChange:o=>((S,b)=>{$(b)})(0,a)},{default:l(()=>[(c(!0),k(U,null,E(s(x),o=>(c(),v(p,{key:o.id,label:o.name,value:o.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),_:2},1032,["prop","rules"])]),_:1}),e(i,{label:"\u4EA7\u54C1\u540D\u79F0","min-width":"180"},{default:l(({row:a,$index:u})=>[e(r,{prop:`${u}.productId`,rules:s(V).productId,class:"mb-0px!"},{default:l(()=>[e(f,{modelValue:a.productId,"onUpdate:modelValue":o=>a.productId=o,clearable:"",filterable:"",onChange:o=>((S,b)=>{const _=g.value.find(D=>D.id===S);_&&(b.productUnitName=_.unitName,b.productBarCode=_.barCode,b.productPrice=_.minPrice),$(b)})(o,a),placeholder:"\u8BF7\u9009\u62E9\u4EA7\u54C1"},{default:l(()=>[(c(!0),k(U,null,E(s(g),o=>(c(),v(p,{key:o.id,label:o.name,value:o.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),_:2},1032,["prop","rules"])]),_:1}),e(i,{label:"\u8D26\u9762\u5E93\u5B58","min-width":"100"},{default:l(({row:a})=>[e(r,{class:"mb-0px!"},{default:l(()=>[e(n,{disabled:"",modelValue:a.stockCount,"onUpdate:modelValue":u=>a.stockCount=u,formatter:s(C)},null,8,["modelValue","onUpdate:modelValue","formatter"])]),_:2},1024)]),_:1}),e(i,{label:"\u6761\u7801","min-width":"150"},{default:l(({row:a})=>[e(r,{class:"mb-0px!"},{default:l(()=>[e(n,{disabled:"",modelValue:a.productBarCode,"onUpdate:modelValue":u=>a.productBarCode=u},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:1}),e(i,{label:"\u5355\u4F4D","min-width":"80"},{default:l(({row:a})=>[e(r,{class:"mb-0px!"},{default:l(()=>[e(n,{disabled:"",modelValue:a.productUnitName,"onUpdate:modelValue":u=>a.productUnitName=u},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:1}),e(i,{label:"\u5B9E\u9645\u5E93\u5B58",fixed:"right","min-width":"140"},{default:l(({row:a,$index:u})=>[e(r,{prop:`${u}.actualCount`,rules:s(V).actualCount,class:"mb-0px!"},{default:l(()=>[e(w,{modelValue:a.actualCount,"onUpdate:modelValue":o=>a.actualCount=o,"controls-position":"right",precision:3,class:"!w-100%"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])]),_:1}),e(i,{label:"\u76C8\u4E8F\u6570\u91CF",prop:"count",fixed:"right","min-width":"110"},{default:l(({row:a,$index:u})=>[e(r,{prop:`${u}.count`,rules:s(V).count,class:"mb-0px!"},{default:l(()=>[e(n,{disabled:"",modelValue:a.count,"onUpdate:modelValue":o=>a.count=o,formatter:s(C),class:"!w-100%"},null,8,["modelValue","onUpdate:modelValue","formatter"])]),_:2},1032,["prop","rules"])]),_:1}),e(i,{label:"\u4EA7\u54C1\u5355\u4EF7",fixed:"right","min-width":"120"},{default:l(({row:a,$index:u})=>[e(r,{prop:`${u}.productPrice`,class:"mb-0px!"},{default:l(()=>[e(w,{modelValue:a.productPrice,"onUpdate:modelValue":o=>a.productPrice=o,"controls-position":"right",min:.01,precision:2,class:"!w-100%"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"])]),_:1}),e(i,{label:"\u5408\u8BA1\u91D1\u989D",prop:"totalPrice",fixed:"right","min-width":"100"},{default:l(({row:a,$index:u})=>[e(r,{prop:`${u}.totalPrice`,class:"mb-0px!"},{default:l(()=>[e(n,{disabled:"",modelValue:a.totalPrice,"onUpdate:modelValue":o=>a.totalPrice=o,formatter:s(q)},null,8,["modelValue","onUpdate:modelValue","formatter"])]),_:2},1032,["prop"])]),_:1}),e(i,{label:"\u5907\u6CE8","min-width":"150"},{default:l(({row:a,$index:u})=>[e(r,{prop:`${u}.remark`,class:"mb-0px!"},{default:l(()=>[e(n,{modelValue:a.remark,"onUpdate:modelValue":o=>a.remark=o,placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"])]),_:1}),e(i,{align:"center",fixed:"right",label:"\u64CD\u4F5C",width:"60"},{default:l(({$index:a})=>[e(N,{onClick:u=>{return o=a,void m.value.splice(o,1);var o},link:""},{default:l(()=>d[0]||(d[0]=[L("\u2014")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1},8,["model","rules","disabled"])),[[Q,s(z)]]),t.disabled?pe("",!0):(c(),v(M,{key:0,justify:"center",class:"mt-3"},{default:l(()=>[e(N,{onClick:y,round:""},{default:l(()=>d[1]||(d[1]=[L("+ \u6DFB\u52A0\u76D8\u70B9\u4EA7\u54C1")])),_:1})]),_:1}))],64)}}});export{ce as _};
