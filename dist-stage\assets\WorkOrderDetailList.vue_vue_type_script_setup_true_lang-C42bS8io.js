import{a as Q,d as E,I as b}from"./index-BeQABqnP.js";import{_ as W}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CxohUsTD.js";import{d as h}from"./formatTime-CN67D7Gb.js";import{W as c}from"./index-B3LrXjfm.js";import{P as L}from"./index-HrozUqYh.js";import{R as P}from"./index-mfJlVb1-.js";import{f as R}from"./formatter-BLTmz7GT.js";import{g as C,d as M}from"./commonBiz-Dnw63na0.js";import{ak as j,Z as F,_ as U}from"./form-designer-DQFPUccF.js";import{k as V,r as w,b as Y,e as H,y as O,m as v,z as s,A as T,u as n,H as t,E as u,F as y}from"./form-create-B86qX0W_.js";const Z=V({__name:"WorkOrderDetailList",props:{bizOrderId:{},detailType:{}},setup(_){const{t:$}=Q();E();const i=_,f=w(!1),l=w([]),k=w(new Map),I=w(new Map),x=async()=>{try{const e=await C();if(!e||e.length===0)return;e.forEach(a=>{a&&a.id&&a.name&&k.value.set(a.id,a.name)})}catch{}},N=async()=>{try{const e=await M();if(!e)return;Object.keys(e).forEach(a=>{const r=e[a];r&&r.id&&r.name&&I.value.set(r.id,r.name)})}catch{}},D=e=>{if(!e)return"";const a=typeof e=="string"?parseInt(e):e;return k.value.get(a)||e.toString()},S=e=>{if(!e)return"";const a=typeof e=="string"?parseInt(e):e;return I.value.get(a)||e.toString()},A=async()=>{f.value=!0;try{switch(i.detailType){case"workOrderDetail":try{const e=await c.getWorkOrder(i.bizOrderId);if(e&&e.workNo){const a=await L.getPickingReceiptPage({sourceNo:e.workNo,pageNo:1,pageSize:100});a&&Array.isArray(a.list)?l.value=a.list:a&&Array.isArray(a)?l.value=a:l.value=[]}else l.value=[]}catch{try{const a=await c.getWorkOrderDetailListByBizOrderId(i.bizOrderId);l.value=Array.isArray(a)?a:[]}catch{l.value=[]}}break;case"feedOrderDetail":try{const e=await c.getWorkOrderDetailListByBizOrderId(i.bizOrderId);l.value=Array.isArray(e)?e:[]}catch{l.value=[]}break;case"reportOrderDetail":try{const e=await P.getReportOrderPage({workOrderId:i.bizOrderId,pageNo:1,pageSize:100});e&&Array.isArray(e.list)?l.value=e.list:e&&Array.isArray(e)?l.value=e:l.value=[]}catch{try{const a=await c.getWorkOrderDetailListByBizOrderId(i.bizOrderId);l.value=Array.isArray(a)?a:[]}catch{l.value=[]}}break;case"inspectOrderDetail":try{const e=await c.getWorkOrderDetailListByBizOrderId(i.bizOrderId);l.value=Array.isArray(e)?e:[]}catch{l.value=[]}break;case"stockInOrderDetail":try{const e=await c.getWorkOrderDetailListByBizOrderId(i.bizOrderId);l.value=Array.isArray(e)?e:[]}catch{l.value=[]}break;default:try{const e=await c.getWorkOrderDetailListByBizOrderId(i.bizOrderId);l.value=Array.isArray(e)?e:[]}catch{l.value=[]}}}finally{f.value=!1}};Y(()=>({bizOrderId:i.bizOrderId,detailType:i.detailType}),async()=>{i.bizOrderId&&await A()},{immediate:!1});const z=e=>{const{columns:a,data:r}=e,p=[];return a.forEach((m,d)=>{if(d===0)return void(p[d]="\u5408\u8BA1");if(i.detailType==="workOrderDetail")return void(p[d]=d===1?`\u5171 ${r.length} \u6761\u8BB0\u5F55`:"");if(["plannedQuantity","fulfilledQuantity","standardPlannedQuantity","standardFulfilledQuantity","plannedSpecQuantity","fulfilledSpecQuantity"].includes(m.property)){const o=r.map(g=>Number(g[m.property])||0).reduce((g,B)=>g+B,0);p[d]=o>0?R(o):"0"}else p[d]=""}),p};return H(async()=>{await Promise.all([x(),N()]),A()}),(e,a)=>{const r=U,p=F,m=W,d=j;return v(),O(m,null,{default:s(()=>[i.detailType==="workOrderDetail"?T((v(),O(p,{key:0,data:n(l),stripe:!0,"show-overflow-tooltip":!0,"show-summary":"","summary-method":z,border:"",height:"600"},{default:s(()=>[t(r,{label:"\u5355\u53F7",align:"left",prop:"orderNo",width:"120px",fixed:"left"}),t(r,{label:"\u4E1A\u52A1\u7C7B\u578B",align:"left",prop:"bizType",width:"100px"},{default:s(o=>[u(y(n(b)("INVENTORY_TRANSACTION_TYPE",o.row.bizType)),1)]),_:1}),t(r,{label:"\u6765\u6E90\u7C7B\u578B",align:"center",prop:"sourceType",width:"100px"},{default:s(o=>[u(y(n(b)("MATERIAL_SOURCE",o.row.sourceType)),1)]),_:1}),t(r,{label:"\u6765\u6E90\u5355\u7F16\u53F7",align:"left",prop:"sourceNo",width:"120px"}),t(r,{label:"\u4EA4\u6613\u65E5\u671F",align:"center",prop:"date",formatter:n(h),width:"100px"},null,8,["formatter"]),t(r,{label:"\u5BA1\u6279\u72B6\u6001",align:"center",prop:"approveStatus",width:"100px"},{default:s(o=>[u(y(n(b)("APPROVE_STATUS",o.row.approveStatus)),1)]),_:1}),t(r,{label:"\u5BA1\u6279\u5355\u53F7",align:"left",prop:"approveNo",width:"120px"}),t(r,{label:"\u5BA1\u6279\u4EBA",align:"left",prop:"approverName",width:"100px"}),t(r,{label:"\u5BA1\u6279\u65F6\u95F4",align:"center",prop:"approveDate",formatter:n(h),width:"180px"},null,8,["formatter"]),t(r,{label:"\u6458\u8981",align:"left",prop:"note",width:"120px"}),t(r,{label:"\u5907\u6CE8",align:"left",prop:"remark",width:"120px"}),t(r,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",formatter:n(h),width:"180px"},null,8,["formatter"])]),_:1},8,["data"])),[[d,n(f)]]):T((v(),O(p,{key:1,data:n(l),stripe:!0,"show-overflow-tooltip":!0,"show-summary":"","summary-method":z},{default:s(()=>[t(r,{label:"\u5E8F\u53F7",align:"center",prop:"num",width:"70"}),t(r,{label:"\u4ED3\u5E93",align:"center",width:"120px"},{default:s(o=>[u(y(S(o.row.warehouseId)||o.row.warehouseId||""),1)]),_:1}),t(r,{label:"\u7269\u6599\u540D\u79F0",align:"left",prop:"materialName"}),t(r,{label:"\u7269\u6599\u7F16\u53F7",align:"center",prop:"materialCode"}),t(r,{label:"\u89C4\u683C",align:"center",prop:"spec"}),t(r,{label:"\u5355\u4F4D",align:"center",width:"80px"},{default:s(o=>[u(y(D(o.row.unit)||o.row.unit||""),1)]),_:1}),t(r,{label:"\u5907\u6CE8",align:"center",prop:"remark"}),t(r,{label:"\u8BA1\u5212\u6570\u91CF",align:"center",prop:"plannedQuantity"}),t(r,{label:"\u5C65\u7EA6\u6570\u91CF",align:"center",prop:"fulfilledQuantity"}),t(r,{label:"\u89C4\u683C\u6570\u91CF",align:"center",prop:"plannedSpecQuantity"}),t(r,{label:"\u5C65\u7EA6\u89C4\u683C\u6570\u91CF",align:"center",prop:"fulfilledSpecQuantity"}),t(r,{label:"\u8BF4\u660E",align:"center",prop:"note"}),t(r,{label:"\u6279\u53F7",align:"center",prop:"batchNo"})]),_:1},8,["data"])),[[d,n(f)]])]),_:1})}}});export{Z as _};
