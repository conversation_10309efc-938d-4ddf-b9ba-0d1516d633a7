import{aC as n,M as i}from"./index-BeQABqnP.js";import{I as c}from"./index-DSlr-2zm.js";import{aj as V,k as f,l as h}from"./form-designer-DQFPUccF.js";import{k as b,e as g,l as _,m as w,G as U,H as l,z as s,u as o}from"./form-create-B86qX0W_.js";const M=b({name:"RedisStreamMQConfigForm",__name:"RedisStreamMQConfigForm",props:{modelValue:{}},emits:["update:modelValue"],setup(m,{emit:u}){const e=n(m,"modelValue",u);return g(()=>{i(e.value)&&(e.value={type:c.REDIS_STREAM,host:"",port:6379,password:"",database:0,topic:""})}),(R,a)=>{const d=f,p=V,r=h;return w(),_(U,null,[l(p,{label:"\u4E3B\u673A\u5730\u5740",prop:"config.host"},{default:s(()=>[l(d,{modelValue:o(e).host,"onUpdate:modelValue":a[0]||(a[0]=t=>o(e).host=t),placeholder:"\u8BF7\u8F93\u5165\u4E3B\u673A\u5730\u5740\uFF0C\u5982\uFF1Alocalhost"},null,8,["modelValue"])]),_:1}),l(p,{label:"\u7AEF\u53E3",prop:"config.port"},{default:s(()=>[l(r,{modelValue:o(e).port,"onUpdate:modelValue":a[1]||(a[1]=t=>o(e).port=t),max:65535,min:1,"controls-position":"right",placeholder:"\u8BF7\u8F93\u5165\u7AEF\u53E3"},null,8,["modelValue"])]),_:1}),l(p,{label:"\u5BC6\u7801",prop:"config.password"},{default:s(()=>[l(d,{modelValue:o(e).password,"onUpdate:modelValue":a[2]||(a[2]=t=>o(e).password=t),placeholder:"\u8BF7\u8F93\u5165\u5BC6\u7801","show-password":"",type:"password"},null,8,["modelValue"])]),_:1}),l(p,{label:"\u6570\u636E\u5E93",prop:"config.database"},{default:s(()=>[l(r,{modelValue:o(e).database,"onUpdate:modelValue":a[3]||(a[3]=t=>o(e).database=t),max:15,min:0,"controls-position":"right",placeholder:"\u8BF7\u8F93\u5165\u6570\u636E\u5E93\u7D22\u5F15"},null,8,["modelValue"])]),_:1}),l(p,{label:"\u4E3B\u9898",prop:"config.topic"},{default:s(()=>[l(d,{modelValue:o(e).topic,"onUpdate:modelValue":a[4]||(a[4]=t=>o(e).topic=t),placeholder:"\u8BF7\u8F93\u5165\u4E3B\u9898"},null,8,["modelValue"])]),_:1})],64)}}});export{M as _};
