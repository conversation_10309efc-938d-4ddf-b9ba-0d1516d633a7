<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="任务单" prop="workCode">
        <el-input
          v-model="queryParams.workCode"
          placeholder="请输入任务单编号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="任务类型" prop="type">
        <el-select
          v-model="queryParams.type"
          placeholder="请选择任务类型"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.MFG_WORK_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="报工编号" prop="reportCode">
        <el-input
          v-model="queryParams.reportCode"
          placeholder="请输入报工编号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>

      <div v-if="expandSearchForm">
        <el-form-item label="开始时间" prop="startTime">
          <el-date-picker
            v-model="queryParams.startTime"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="人数" prop="costHeadcount">
          <el-input
            v-model="queryParams.costHeadcount"
            placeholder="请输入人数"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="生产线" prop="line">
          <el-select
            v-model="queryParams.line"
            placeholder="请选择生产线"
            clearable
            class="!w-240px"
          >
            <el-option
              v-for="dict in getStrDictOptions(DICT_TYPE.MANUFACTURE_LINE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="数量" prop="quantity">
          <el-input
            v-model="queryParams.quantity"
            placeholder="请输入数量"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="件数" prop="piece">
          <el-input
            v-model="queryParams.piece"
            placeholder="请输入件数"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="批号" prop="batchNo">
          <el-input
            v-model="queryParams.batchNo"
            placeholder="请输入批号"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="温度" prop="temperature">
          <el-input
            v-model="queryParams.temperature"
            placeholder="请输入温度"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="湿度" prop="humidity">
          <el-input
            v-model="queryParams.humidity"
            placeholder="请输入湿度"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
      </div>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['mfg:report-order:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['mfg:report-order:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
        <el-button @click="toggleSearchForm" text type="primary" link>
          {{ expandSearchForm ? '收起' : '展开' }}
          <Icon :icon="expandSearchForm ? 'ep:arrow-up' : 'ep:arrow-down'" />
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table
      v-loading="loading"
      :data="list"
      :stripe="true"
      :show-overflow-tooltip="true"
      :summary-method="getSummaries"
      show-summary
    >
      <el-table-column label="生产编号" align="center" prop="workNo" width="150">
        <template #default="scope">
          <div class="order-no-cell" v-if="scope.row.workNo">
            <div class="order-no-content">
              <span>{{ scope.row.workNo }}</span>
            </div>
            <el-button
              link
              type="info"
              @click="copyOrderNo(scope.row.workNo)"
              class="copy-btn copy-btn-fixed"
              size="small"
            >
              <Icon icon="ep:copy-document" :size="12"/>
            </el-button>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="任务类型" align="center" prop="type">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.MFG_WORK_TYPE" :value="scope.row.type" />
        </template>
      </el-table-column>
      <el-table-column label="报工编号" align="center" prop="reportCode" width="180">
        <template #default="scope">
          <div class="order-no-cell" v-if="scope.row.reportCode">
            <div class="order-no-content">
              <span>{{ scope.row.reportCode }}</span>
            </div>
            <el-button
              link
              type="info"
              @click="copyOrderNo(scope.row.reportCode)"
              class="copy-btn copy-btn-fixed"
              size="small"
            >
              <Icon icon="ep:copy-document" :size="12"/>
            </el-button>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column
        label="开始时间"
        align="center"
        prop="startTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column
        label="结束时间"
        align="center"
        prop="endTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="用时" align="center" prop="costTime" :formatter="durationTableFormatter" width="150"/>
      <el-table-column label="人数" align="center" prop="costHeadcount" :formatter="quantityTableFormatter" />
      <el-table-column label="生产线" align="center" prop="line" width="110">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.MANUFACTURE_LINE" :value="scope.row.line" />
        </template>
      </el-table-column>
      <el-table-column label="数量" align="center" prop="quantity" :formatter="quantityTableFormatter" width="100"/>
      <el-table-column label="件数" align="center" prop="piece" :formatter="quantityTableFormatter" />
      <el-table-column label="批号" align="center" prop="batchNo" width="120"/>
      <el-table-column label="温度" align="center" prop="temperature" />
      <el-table-column label="湿度" align="center" prop="humidity" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" width="120px" fixed="right">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['mfg:report-order:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['mfg:report-order:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <ReportOrderForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { ReportOrderApi, ReportOrderVO } from '@/api/scm/mfg/reportorder'
import ReportOrderForm from './ReportOrderForm.vue'
import { quantityTableFormatter, formatQuantity, durationTableFormatter, formatMinutesToDuration } from '@/utils/formatter'
import { useClipboard } from '@vueuse/core'

/** 报工 列表 */
defineOptions({ name: 'ReportOrder' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const { copy } = useClipboard() // 复制功能

const loading = ref(true) // 列表的加载中
const list = ref<ReportOrderVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const expandSearchForm = ref(false) // 搜索表单展开状态
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  workId: undefined,
  workNo: undefined,
  workCode: undefined,
  type: undefined,
  reportCode: undefined,
  startTime: [],
  endTime: [],
  costTime: [],
  costHeadcount: undefined,
  line: undefined,
  quantity: undefined,
  piece: undefined,
  batchNo: undefined,
  temperature: undefined,
  humidity: undefined,
  remark: undefined,
  createTime: [],
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await ReportOrderApi.getReportOrderPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 切换查询条件展开/收起状态 */
const toggleSearchForm = () => {
  expandSearchForm.value = !expandSearchForm.value
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await ReportOrderApi.deleteReportOrder(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await ReportOrderApi.exportReportOrder(queryParams)
    download.excel(data, '报工.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 复制单号 */
const copyOrderNo = async (orderNo: string) => {
  try {
    await copy(orderNo)
    message.success('单号复制成功')
  } catch (error) {
    message.error('复制失败')
  }
}

/** 表格汇总方法 */
const getSummaries = (param: any) => {
  const { columns, data } = param
  const sums: string[] = []

  columns.forEach((column: any, index: number) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }

    // 需要汇总的数量字段
    const summaryFields = ['costHeadcount', 'quantity', 'piece']
    // 需要汇总的时间字段
    const timeFields = ['costTime']

    if (summaryFields.includes(column.property)) {
      const values = data.map((item: ReportOrderVO) => Number(item[column.property as keyof ReportOrderVO]))
      if (!values.every((value: number) => Number.isNaN(value))) {
        const sum = values.reduce((prev: number, curr: number) => {
          const value = Number(curr)
          if (!Number.isNaN(value)) {
            return prev + value
          } else {
            return prev
          }
        }, 0)
        sums[index] = formatQuantity(sum)
      } else {
        sums[index] = ''
      }
    } else if (timeFields.includes(column.property)) {
      // 处理时间字段的汇总
      const values = data.map((item: ReportOrderVO) => Number(item[column.property as keyof ReportOrderVO]))
      if (!values.every((value: number) => Number.isNaN(value))) {
        const sum = values.reduce((prev: number, curr: number) => {
          const value = Number(curr)
          if (!Number.isNaN(value)) {
            return prev + value
          } else {
            return prev
          }
        }, 0)
        sums[index] = formatMinutesToDuration(sum)
      } else {
        sums[index] = ''
      }
    } else {
      sums[index] = ''
    }
  })

  return sums
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>

<style scoped>
.order-no-cell {
  display: flex;
  align-items: center;
  width: 100%;
  min-height: 20px;
  gap: 5px; /* 文字和图标之间保持一点点间隔 */
}

.order-no-content {
  flex: 0 1 auto; /* 改为自适应宽度，不占满剩余空间 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: calc(100% - 16px); /* 为图标预留空间 */
}

/* 复制按钮样式 */
.copy-btn {
  padding: 2px !important;
  height: 20px !important;
  min-height: 20px !important;
  width: 20px !important;
  font-size: 12px;
  opacity: 0.6;
  transition: opacity 0.2s;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}



.copy-btn:hover {
  opacity: 1;
}
</style>
