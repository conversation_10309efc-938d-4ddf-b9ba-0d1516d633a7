import{_ as f}from"./CountTo.vue_vue_type_script_setup_true_lang-BntEaWTh.js";import{p as a,_ as d}from"./index-BeQABqnP.js";import{o as x,I as s}from"./form-designer-DQFPUccF.js";import{k as u,l as g,m as o,v as t,y as v,C as b,F as p,z as y,H as l,B as _,u as r,E as w}from"./form-create-B86qX0W_.js";const S={class:"flex flex-col gap-2 bg-[var(--el-bg-color-overlay)] p-6"},T={class:"flex items-center justify-between text-gray-500"},k={class:"mb-4 text-3xl"},V={class:"flex flex-row gap-1 text-sm"},h=u({name:"TradeStatisticValue",__name:"TradeStatisticValue",props:{tooltip:a.string.def(""),title:a.string.def(""),prefix:a.string.def(""),value:a.number.def(0),decimals:a.number.def(0),percent:a.oneOfType([Number,String]).def(0)},setup:e=>(j,n)=>{const i=d,c=x,m=f;return o(),g("div",S,[t("div",T,[t("span",null,p(e.title),1),e.tooltip?(o(),v(c,{key:0,content:e.tooltip,placement:"top-start"},{default:y(()=>[l(i,{icon:"ep:warning"})]),_:1},8,["content"])):b("",!0)]),t("div",k,[l(m,{prefix:e.prefix,"end-val":e.value,decimals:e.decimals},null,8,["prefix","end-val","decimals"])]),t("div",V,[n[0]||(n[0]=t("span",{class:"text-gray-500"},"\u73AF\u6BD4",-1)),t("span",{class:_(r(s)(e.percent)>0?"text-red-500":"text-green-500")},[w(p(Math.abs(r(s)(e.percent)))+"% ",1),l(i,{icon:r(s)(e.percent)>0?"ep:caret-top":"ep:caret-bottom",class:"!text-sm"},null,8,["icon"])],2)])])}});export{h as _};
