import{a as U,d as j}from"./index-BeQABqnP.js";import{_ as q}from"./Dialog.vue_vue_type_style_index_0_lang-DpoeWsKb.js";import{g as z,c as A,u as E}from"./index-QFhiabVl.js";import{h as H,aj as M,k as R,ak as B,f as D}from"./form-designer-DQFPUccF.js";import{k as G,r as o,y as _,m as g,z as r,A as J,u as s,H as d,E as k,h as K}from"./form-create-B86qX0W_.js";const L=G({name:"MpTagForm",__name:"TagForm",emits:["success"],setup(N,{expose:b,emit:w}){const{t:i}=U(),f=j(),u=o(!1),p=o(""),t=o(!1),v=o(""),l=o({accountId:-1,name:""}),V={name:[{required:!0,message:"\u8BF7\u8F93\u5165\u6807\u7B7E\u540D\u79F0",trigger:"blur"}]},m=o(null),h=w;b({open:async(a,e,c)=>{if(u.value=!0,p.value=i("action."+a),v.value=a,F(),l.value.accountId=e,c){t.value=!0;try{l.value=await z(c)}finally{t.value=!1}}}});const x=async()=>{var a;if(m&&await((a=m.value)==null?void 0:a.validate())){t.value=!0;try{const e=l.value;v.value==="create"?(await A(e),f.success(i("common.createSuccess"))):(await E(e),f.success(i("common.updateSuccess"))),u.value=!1,h("success")}finally{t.value=!1}}},F=()=>{var a;l.value={accountId:-1,name:""},(a=m.value)==null||a.resetFields()};return(a,e)=>{const c=R,I=M,C=H,y=D,S=q,T=B;return g(),_(S,{modelValue:s(u),"onUpdate:modelValue":e[2]||(e[2]=n=>K(u)?u.value=n:null),title:s(p)},{footer:r(()=>[d(y,{disabled:s(t),type:"primary",onClick:x},{default:r(()=>e[3]||(e[3]=[k("\u786E \u5B9A")])),_:1},8,["disabled"]),d(y,{onClick:e[1]||(e[1]=n=>u.value=!1)},{default:r(()=>e[4]||(e[4]=[k("\u53D6 \u6D88")])),_:1})]),default:r(()=>[J((g(),_(C,{ref_key:"formRef",ref:m,model:s(l),rules:V,"label-width":"80px"},{default:r(()=>[d(I,{label:"\u6807\u7B7E\u540D\u79F0",prop:"name"},{default:r(()=>[d(c,{modelValue:s(l).name,"onUpdate:modelValue":e[0]||(e[0]=n=>s(l).name=n),placeholder:"\u8BF7\u8F93\u5165\u6807\u7B7E\u540D\u79F0"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])),[[T,s(t)]])]),_:1},8,["modelValue","title"])}}});export{L as _};
