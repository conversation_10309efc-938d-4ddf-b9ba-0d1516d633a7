<template>
  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true" border size="small" show-summary :summary-method="summaryMethod">
      <!-- <el-table-column label="ID" align="center" prop="id" /> -->
<!--      <el-table-column label="交易编号" align="center" prop="bizId" width="100" />-->
      <el-table-column label="业务单号" align="center" prop="bizNo" width="150" />
      <!-- <el-table-column label="交易明细ID" align="center" prop="bizDetailId" width="120px"/> -->
      <el-table-column label="交易类型" align="center" prop="transactionType" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.SCM_BIZ_TYPE" :value="scope.row.transactionType" />
        </template>
      </el-table-column>
      <el-table-column label="交易方向" align="center" prop="transactionDirection" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.INVENTORY_TRANSACTION_DIRECTION" :value="scope.row.transactionDirection" />
        </template>
      </el-table-column>
      <!-- <el-table-column label="物料ID" align="center" prop="materialId" /> -->
<!--      <el-table-column label="物料编码" align="center" prop="materialCode" width="120" />
      <el-table-column label="物料名称" align="left" prop="materialName" width="200" />
      <el-table-column label="物料类型" align="center" prop="materialType" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.MATERIAL_TYPE" :value="scope.row.materialType" />
        </template>
      </el-table-column>-->
      <el-table-column
        label="移动日期"
        align="center"
        prop="moveDate"
        :formatter="dateFormatter"
        width="140"
      />
      <!-- <el-table-column label="来源单号ID" align="center" prop="sourceId" width="120px"/> -->
      <el-table-column label="库存批号" align="center" prop="inventoryBatchNo" width="120" />
      <el-table-column label="数量" align="center" prop="quantity" width="100">
        <template #default="scope">
          {{ formatQuantity(scope.row.quantity) }}
        </template>
      </el-table-column>
      <el-table-column label="单位" align="center" width="80">
        <template #default="scope">
          {{ getUnitName(scope.row.quantityUnit) }}
        </template>
      </el-table-column>
      <!--      <el-table-column label="基本单位数量" align="center" prop="auxiliaryQuantity" width="120">-->
      <!--        <template #default="scope">-->
      <!--          {{ formatQuantity(scope.row.auxiliaryQuantity) }}-->
      <!--        </template>-->
      <!--      </el-table-column>-->
      <!--      <el-table-column label="基本单位" align="center" width="100">-->
      <!--        <template #default="scope">-->
      <!--          {{ getUnitName(scope.row.auxiliaryUnit) }}-->
      <!--        </template>-->
      <!--      </el-table-column>-->
      <el-table-column label="出入库前数量" align="center" prop="beforeQuantity" width="120">
        <template #default="scope">
          {{ formatQuantity(scope.row.beforeQuantity) }}
        </template>
      </el-table-column>
      <el-table-column label="出入库后数量" align="center" prop="afterQuantity" width="120">
        <template #default="scope">
          {{ formatQuantity(scope.row.afterQuantity) }}
        </template>
      </el-table-column>
      <!--      <el-table-column label="成本对象编码" align="center" prop="costObjectCode" width="120"/>-->
      <!--      <el-table-column label="成本对象名称" align="center" prop="costObjectName" width="120"/>-->
      <!--      <el-table-column label="记账凭证号" align="center" prop="accountingVoucherNumber" width="120"/>-->
      <el-table-column label="摘要" align="center" prop="summary" width="150" />
      <el-table-column label="移动类型" align="center" prop="moveType" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.INVENTORY_MOVE_TYPE" :value="scope.row.moveType" />
        </template>
      </el-table-column>
      <!-- <el-table-column label="移动源仓库ID" align="center" prop="fromWarehouseId" width="120px"/> -->
      <el-table-column label="移动源仓库" align="center" prop="fromWarehouseName" width="120"/>
      <!-- <el-table-column label="移动源仓位ID" align="center" prop="fromLocationId" width="120px"/> -->
      <el-table-column label="移动源仓位" align="center" prop="fromLocationName" width="120"/>
      <!-- <el-table-column label="移动到仓库ID" align="center" prop="toWarehouseId" width="120px"/> -->
      <el-table-column label="移动到仓库" align="center" prop="toWarehouseName" width="120"/>
      <!-- <el-table-column label="移动到仓位ID" align="center" prop="toLocationId" width="120px"/> -->
      <el-table-column label="移动到仓位" align="center" prop="toLocationName" width="120"/>
      <el-table-column label="来源单号" align="center" prop="sourceNo" width="150" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="160"
      />
    </el-table>

    <!-- 无数据提示 -->
    <div v-if="!loading && list.length === 0" class="p-4 text-center text-gray-500">
      暂无历史交易记录
    </div>
  </ContentWrap>
</template>
<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import { StockInfoApi } from '@/api/scm/inventory/stockinfo'
import { DICT_TYPE } from '@/utils/dict'
import { getRemoteUnit } from '@/utils/commonBiz'
import { formatQuantity } from '@/utils/formatter'

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const props = defineProps<{
  inventoryId?: number // 库存ID（主表的关联字段）
}>()
const loading = ref(false) // 列表的加载中
const list = ref<any[]>([]) // 列表的数据
const unitMap = ref<Map<number, string>>(new Map()) // 单位ID到名称的映射

/** 加载单位数据 */
const loadUnits = async () => {
  try {
    // 批量获取所有单位信息
    const units = await getRemoteUnit()

    if (!units || units.length === 0) {
      return
    }

    // 建立单位映射
    units.forEach((unit: any) => {
      if (unit && unit.id && unit.name) {
        unitMap.value.set(unit.id, unit.name)
      }
    })
  } catch (error) {
    console.error('加载单位数据失败:', error)
  }
}

/** 获取单位名称 */
const getUnitName = (unitId: number | string) => {
  if (!unitId && unitId !== 0) return ''
  
  // 处理数字类型
  if (typeof unitId === 'number') {
    const unitName = unitMap.value.get(unitId)
    if (unitName) return unitName
  }
  
  // 处理字符串类型
  if (typeof unitId === 'string') {
    // 先尝试按字符串查找
    for (const [id, name] of unitMap.value) {
      if (id.toString() === unitId) {
        return name
      }
    }
    
    // 再尝试转换为数字查找
    const numId = parseInt(unitId)
    if (!isNaN(numId)) {
      const unitName = unitMap.value.get(numId)
      if (unitName) return unitName
    }
  }
  
  return unitId.toString()
}

/** 查询列表 */
const getList = async () => {
  if (!props.inventoryId) {
    list.value = []
    return
  }
  
  loading.value = true
  try {
    // 确保单位数据已加载
    if (unitMap.value.size === 0) {
      await loadUnits()
    }
    
    const data = await StockInfoApi.getTransactionListByInventoryId(props.inventoryId)
    list.value = data || []
  } catch (error) {
    console.error('获取历史交易记录失败:', error)
    list.value = []
    message.error('获取历史交易记录失败')
  } finally {
    loading.value = false
  }
}

/** 表格汇总方法 */
const summaryMethod = ({ columns, data }: { columns: any[], data: any[] }) => {
  const sums: any[] = []

  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }

    // 需要汇总的数量字段
    const quantityFields = ['quantity', 'auxiliaryQuantity', 'beforeQuantity', 'afterQuantity']

    if (quantityFields.includes(column.property)) {
      // 数量字段汇总
      const values = data.map(item => Number(item[column.property]) || 0)
      const total = values.reduce((prev, curr) => prev + curr, 0)
      sums[index] = formatQuantity(total)
    } else {
      // 其他字段不汇总
      sums[index] = ''
    }
  })

  return sums
}

/** 监听props变化 */
watch(() => props.inventoryId, () => {
  if (props.inventoryId) {
    getList()
  } else {
    list.value = []
  }
}, { immediate: true })

/** 初始化 **/
onMounted(async () => {
  // 先加载单位数据
  await loadUnits()
  // 如果有inventoryId，则加载数据
  if (props.inventoryId) {
    getList()
  }
})
</script>
