import{a as d}from"./tagsView-BRFKV6mF.js";import{u}from"./index-BeQABqnP.js";import{c as f,u as l,n as h}from"./form-create-B86qX0W_.js";const n=()=>{const r=d(),{replace:i,currentRoute:c}=u(),a=f(()=>r.getSelectedTag);return{closeAll:e=>{r.delAllViews(),e==null||e()},closeLeft:e=>{r.delLeftViews(l(a)),e==null||e()},closeRight:e=>{r.delRightViews(l(a)),e==null||e()},closeOther:e=>{r.delOthersViews(l(a)),e==null||e()},closeCurrent:(e,s)=>{var t;(t=e==null?void 0:e.meta)!=null&&t.affix||(r.delView(e||l(c)),s==null||s())},refreshPage:async(e,s)=>{r.delCachedView();const{path:t,query:o}=e||l(c);await h(),i({path:"/redirect"+t,query:o}),s==null||s()},setTitle:(e,s)=>{r.setTitle(e,s)}}};export{n as u};
