<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="auto"
    >
      <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择状态"
          clearable
          multiple
          collapse-tags
          collapse-tags-tooltip
          class="!w-240px"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.WORK_ORDER_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="产品名称" prop="productName">
        <el-input
          v-model="queryParams.productName"
          placeholder="请输入产品名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="产品编号" prop="productCode">
        <el-input
          v-model="queryParams.productCode"
          placeholder="请输入产品编号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />        
      </el-form-item>
      <div v-if="expandSearchForm">
        <el-form-item label="客户名称" prop="customerName">
        <el-input
          v-model="queryParams.customerName"
          placeholder="请输入客户名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
        <el-form-item label="来源类型" prop="orderType">
        <el-select
          v-model="queryParams.orderType"
          placeholder="请选择来源类型"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.MFG_ORDER_SOURCE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
        <el-form-item label="来源单号" prop="orderNo">
          <el-input
            v-model="queryParams.orderNo"
            placeholder="请输入来源订单编号"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="产品类型" prop="productSubType">
          <el-select
            v-model="queryParams.productSubType"
            placeholder="请选择产品类型"
            clearable
            class="!w-240px"
          >
            <el-option
              v-for="dict in getStrDictOptions(DICT_TYPE.MATERIAL_TYPE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="Bom" prop="bomCode">
          <el-input
            v-model="queryParams.bomCode"
            placeholder="请输入bom编码"
            clearable
            @keyup.enter="handleQuery"
            class="!w-220px"
          />
        </el-form-item>
        <el-form-item label="完成进度" prop="progress">
          <el-input
            v-model="queryParams.progress"
            placeholder="请输入完成进度"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="下单时间" prop="orderDate">
          <el-date-picker
            v-model="queryParams.orderDate"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="计划日期" prop="scheduleStartDate">
          <el-date-picker
            v-model="queryParams.scheduleStartDate"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="审批单号" prop="approveNo">
          <el-input
            v-model="queryParams.approveNo"
            placeholder="请输入审批单号"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="审批状态" prop="approveStatus">
          <el-select
            v-model="queryParams.approveStatus"
            placeholder="请选择审批状态"
            clearable
            class="!w-240px"
          >
            <el-option
              v-for="dict in getStrDictOptions(DICT_TYPE.APPROVE_STATUS)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="领料状态" prop="pickingStatus">
          <el-select
            v-model="queryParams.pickingStatus"
            placeholder="请选择领料状态"
            clearable
            multiple
            collapse-tags
            collapse-tags-tooltip
            class="!w-240px"
          >
            <el-option
              v-for="dict in getStrDictOptions(DICT_TYPE.COMMON_TASK_STATUS)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="入库状态" prop="inStockStatus">
          <el-select
            v-model="queryParams.inStockStatus"
            placeholder="请选择入库状态"
            clearable
            multiple
            collapse-tags
            collapse-tags-tooltip
            class="!w-240px"
          >
            <el-option
              v-for="dict in getStrDictOptions(DICT_TYPE.COMMON_TASK_STATUS)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="报工状态" prop="reportStatus">
          <el-select
            v-model="queryParams.reportStatus"
            placeholder="请选择报工状态"
            clearable
            multiple
            collapse-tags
            collapse-tags-tooltip
            class="!w-240px"
          >
            <el-option
              v-for="dict in getStrDictOptions(DICT_TYPE.COMMON_TASK_STATUS)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="质检状态" prop="qualityStatus">
          <el-select
            v-model="queryParams.qualityStatus"
            placeholder="请选择质检状态"
            clearable
            multiple
            collapse-tags
            collapse-tags-tooltip
            class="!w-240px"
          >
            <el-option
              v-for="dict in getStrDictOptions(DICT_TYPE.COMMON_TASK_STATUS)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
      </div>
  
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['mfg:work-order:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['mfg:work-order:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
        <el-button
          type="primary"
          plain
          @click="handleApproval"
          :loading="exportLoading"
          v-hasPermi="['mfg:work-order:approve']"
        >
          <Icon icon="ep:check" class="mr-5px" /> 审核
        </el-button>
        <el-button @click="toggleSearchForm" text type="primary" link>
          {{ expandSearchForm ? '收起' : '展开' }}
          <Icon :icon="expandSearchForm ? 'ep:arrow-up' : 'ep:arrow-down'" />
        </el-button>

      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <!-- 任务分类Tab -->
    <el-tabs v-model="activeTaskTab" @tab-change="handleTaskTabChange" class="mb-4">
      <el-tab-pane label="全部" name="all" />
      <el-tab-pane label="昨日" name="yesterday" />
      <el-tab-pane label="今日" name="today" />
      <el-tab-pane label="明日" name="tomorrow" />
      <el-tab-pane label="未完成" name="unfinished" />
    </el-tabs>

    <el-table
      v-loading="loading"
      :data="list"
      :stripe="true"
      :show-overflow-tooltip="true"
      border
      @selection-change="handleSelectionChange"
      show-summary
      :summary-method="getSummaries"
    >
      <el-table-column type="selection" width="60" fixed="left"/>
      <!-- 子表的列表 -->
      <el-table-column type="expand" fixed="left">
        <template #default="scope">
          <el-tabs v-model="activeTabName" @tab-change="handleTabChange">
            <el-tab-pane label="投料单" name="feedOrderDetail">
              <WorkOrderDetailList
                v-if="activeTabName === 'feedOrderDetail'"
                :biz-order-id="scope.row.id"
                detail-type="feedOrderDetail"
              />
            </el-tab-pane>
            <el-tab-pane label="领料单" name="workOrderDetail">
              <PickingReceiptTable
                v-if="activeTabName === 'workOrderDetail'"
                :work-order-id="scope.row.id"
              />
            </el-tab-pane>
            <el-tab-pane label="报工单" name="reportOrderDetail">
              <ReportOrderTable
                v-if="activeTabName === 'reportOrderDetail'"
                :work-order-id="scope.row.id"
              />
            </el-tab-pane>
            <el-tab-pane label="质检单" name="inspectOrderDetail">
              <InspectionTable
                v-if="activeTabName === 'inspectOrderDetail'"
                :work-order-id="scope.row.id"
              />
            </el-tab-pane>
            <el-tab-pane label="入库单" name="stockInOrderDetail">
              <ProductReceiptTable
                v-if="activeTabName === 'stockInOrderDetail'"
                :work-order-id="scope.row.id"
              />
            </el-tab-pane>
          </el-tabs>
        </template>
      </el-table-column>
      <el-table-column label="生产单号" align="left" width="200" fixed="left">
        <template #default="scope">
          <div class="work-no-container">
            <div class="order-no-cell" v-if="scope.row.workNo">
              <div class="order-no-content">
                <span
                  class="work-no-link"
                  @click="openWorkOrderDetail(scope.row.id)"
                  :title="'点击查看工作订单详情'"
                >
                  {{ scope.row.workNo || '-' }}
                </span>
              </div>
              <el-button
                link
                type="info"
                @click="copyOrderNo(scope.row.workNo)"
                class="copy-btn copy-btn-fixed"
                size="small"
              >
                <Icon icon="ep:copy-document" :size="12"/>
              </el-button>
            </div>
            <span v-else>-</span>
            <dict-tag
              v-if="scope.row.approveStatus"
              :type="DICT_TYPE.APPROVE_STATUS"
              :value="scope.row.approveStatus"
              class="status-tag"
            />
          </div>
        </template>
      </el-table-column>
      <el-table-column label="产品编号" align="center" prop="productCode" width="120"/>
      <el-table-column label="产品名称" align="center" prop="productName" width="120"/>
      <!-- <el-table-column label="产品类型" align="center" prop="productSubType" /> -->
      <!-- <el-table-column label="产品单位" align="center" prop="productUnit" /> -->
      <el-table-column label="规格" align="center" prop="spec" />

      <!-- <el-table-column label="客户名称" align="center" prop="customerName" width="180"/> -->
       <el-table-column label="订单数量" align="center" prop="orderQuantity" width="110px">
        <template #default="scope">
           <el-tag>{{ scope.row.orderQuantity || 0 }} {{ unitMap.get(Number(scope.row.orderUnit)) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="计划数量" align="center" prop="scheduleQuantity" width="110">
        <template  #default="scope">
          <el-tag>
            {{ scope.row.scheduleQuantity || 0 }} {{ unitMap.get(Number(scope.row.orderUnit)) }}
          </el-tag>
        </template> 
      </el-table-column>
      <el-table-column label="计划件数" align="center" prop="schedulePiece" width="100"/>
      <el-table-column
        label="开始时间"
        align="center"
        prop="scheduleStartTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column
        label="结束时间"
        align="center"
        prop="scheduleEndTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="来源类型" align="center" prop="orderType" width="120">
         <template #default="scope">
          <dict-tag :type="DICT_TYPE.MFG_ORDER_SOURCE" :value="scope.row.orderType" />
        </template>
      </el-table-column>
      <el-table-column label="销售单号" align="center" prop="orderNo" width="120">
        <template #default="scope">
          <div class="order-no-cell" v-if="scope.row.orderNo">
            <div class="order-no-content">
              <span>{{ scope.row.orderNo }}</span>
            </div>
            <el-button
              link
              type="info"
              @click="copyOrderNo(scope.row.orderNo)"
              class="copy-btn copy-btn-fixed"
              size="small"
            >
              <Icon icon="ep:copy-document" :size="12"/>
            </el-button>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="bom" align="center" prop="bomCode" width="150">
        <template #default="scope">
          <el-tag v-if="scope.row.bomCode">{{ scope.row.bomCode || '无' }} {{ scope.row.bomVersion }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.WORK_ORDER_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>

      <el-table-column label="领料状态" align="center" prop="pickingStatus" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.COMMON_TASK_STATUS" :value="scope.row.pickingStatus" />
        </template>
      </el-table-column>
      <el-table-column label="入库状态" align="center" prop="inStockStatus" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.COMMON_TASK_STATUS" :value="scope.row.inStockStatus" />
        </template>
      </el-table-column>
      <el-table-column label="报工状态" align="center" prop="reportStatus" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.COMMON_TASK_STATUS" :value="scope.row.reportStatus" />
        </template>
      </el-table-column>
      <el-table-column label="质检状态" align="center" prop="qualityStatus" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.COMMON_TASK_STATUS" :value="scope.row.qualityStatus" />
        </template>
      </el-table-column>
      <el-table-column
        label="下单时间"
        align="center"
        prop="orderDate"
        :formatter="dateFormatter2"
        width="100px"
      />
      <el-table-column
        label="交期"
        align="center"
        prop="deliverDate"
        :formatter="dateFormatter"
        width="180px"
      />
      
      <el-table-column label="计划用时" align="center" prop="scheduleCostTime" width="120">
        <template #default="scope">
          {{ scope.row.scheduleCostTime ? formatMinutesToTime(scope.row.scheduleCostTime) : '' }}
        </template>
      </el-table-column>
      <el-table-column label="计划产线" align="center" prop="scheduleLine" width="110">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.MANUFACTURE_LINE" :value="scope.row.scheduleLine" />
        </template>
      </el-table-column>
      <el-table-column label="计划用人" align="center" prop="scheduleHeadcount" width="100">
        <template #default="scope">
          {{ scope.row.scheduleHeadcount ? `${scope.row.scheduleHeadcount}人` : '' }}
        </template>
      </el-table-column>
      <el-table-column label="生产要求" align="center" prop="requirement" width="100"/>
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="完成进度" align="center" prop="progress" width="150">
        <template #default="scope">
          <div class="progress-container">
            <el-progress
              :percentage="parseFloat(scope.row.progress) || 0"
              :stroke-width="8"
              :show-text="true"
              :format="(percentage) => `${percentage}%`"
              :color="getProgressColor(parseFloat(scope.row.progress) || 0)"
            />
          </div>
        </template>
      </el-table-column>
      <el-table-column label="实际生产线" align="center" prop="actualLine" width="110">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.MANUFACTURE_LINE" :value="scope.row.actualLine" />
        </template>
      </el-table-column>
      <el-table-column label="实际生产数量" align="center" prop="actualQuantity" width="120"/>
      <el-table-column
        label="实际开始时间"
        align="center"
        prop="actualStartTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column
        label="实际结束时间"
        align="center"
        prop="actualEndTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="实际耗时" align="center" prop="actualCostTime" width="150">
        <template #default="scope">
          {{ scope.row.actualCostTime ? formatMinutesToTime(scope.row.actualCostTime) : '' }}
        </template>
      </el-table-column>
      <el-table-column label="实际用人" align="center" prop="actualHeadcount" width="100">
        <template #default="scope">
          {{ scope.row.actualHeadcount ? `${scope.row.actualHeadcount}人` : '' }}
        </template>
      </el-table-column>
      <el-table-column label="实际生产件数" align="center" prop="actualPiece" width="120"/>
      <el-table-column label="批号" align="center" prop="actualBatchNo" />
      <el-table-column label="生产备注" align="center" prop="actualRemark" width="100"/>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <!-- <el-table-column label="审批单号" align="center" prop="approveNo" width="120"/>
      <el-table-column
        label="审批时间"
        align="center"
        prop="approvalTime"
        :formatter="dateFormatter"
        width="180px"
      /> -->
      <el-table-column label="操作" align="center" min-width="220px" fixed="right">
        <template #default="scope">
          <!-- 投料确认：不受审核状态限制 -->
          <el-button
            link
            type="success"
            @click="openForm('confirmTask', scope.row.id)"
            v-hasPermi="['mfg:work-order:confirm']"
            v-if="scope.row.status == 0"
          >
            投料确认
          </el-button>
          <el-button
            link
            type="success"
            @click="openForm('confirmTask', scope.row.id)"
            v-hasPermi="['mfg:work-order:change']"
            v-if="scope.row.status == 1"
          >
            投料变更
          </el-button>
          <!-- 领料：需要审核通过 -->
          <el-button
            v-if="scope.row.approveStatus === 3"
            link
            type="primary"
            @click="openPickingForm('create', scope.row)"
            v-hasPermi="['mfg:work-order:picking']"
          >
            领料
          </el-button>
          <!-- 报工：需要审核通过 -->
          <el-button
            link
            type="primary"
            v-if="scope.row.approveStatus === 3"
            @click="openReportOrderForm('create', scope.row)"
            v-hasPermi="['mfg:work-order:report']"
          >
            报工
          </el-button>
          <el-dropdown  @command="handleCommand($event, scope.row)" class="more-btn">
            <span class="el-dropdown-link">
              更多
              <el-icon class="el-icon--right">
                <arrow-down />
              </el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <!-- 质检：需要审核通过 -->
                <el-dropdown-item
                  v-if="scope.row.approveStatus === 3"
                  command="inspection"
                  v-hasPermi="['quality:inspection:create']"
                >
                  质检
                </el-dropdown-item>
                <!-- 入库：需要审核通过 -->
                <el-dropdown-item
                  v-if="scope.row.approveStatus === 3"
                  command="stockIn"
                  v-hasPermi="['mfg:work-order:update']"
                >
                  入库
                </el-dropdown-item>

                <!-- 编辑：投料确认后不能编辑 -->
                <el-dropdown-item
                  v-if="(!scope.row.feedStatus || scope.row.feedStatus === 0) && scope.row.approveStatus < 3"
                  command="update"
                  v-hasPermi="['mfg:work-order:update']"
                >
                  编辑
                </el-dropdown-item>
                <!-- 删除：投料确认后不能删除 -->
                <el-dropdown-item
                  v-if="(!scope.row.feedStatus || scope.row.feedStatus === 0) && scope.row.approveStatus < 3"
                  command="delete"
                  v-hasPermi="['mfg:work-order:delete']"
                >
                  删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <WorkOrderForm ref="formRef" @success="getList" />
  <!-- 领料form -->
  <PickingReceiptForm ref="pickingFormRef" @success="getList" />
  <!-- 报工-->
   <ReportOrderForm ref="reportOrderFormRef" @success="getList" />
  <!-- 质检表单 -->
  <InspectionForm ref="inspectionFormRef" @success="getList" />
  <!-- 产品入库表单 -->
  <ProductReceiptForm ref="productReceiptFormRef" @success="getList" />
  <!-- 审核表单 -->
  <ApproveInfoForm ref="approveInfoFormRef" @success="getList" :biz-id="currentRow?.id" biz-type="work_order" :biz-no="currentRow?.workNo"/>

</template>

<script setup lang="ts">
import { getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { dateFormatter, dateFormatter2 } from '@/utils/formatTime'
import { formatQuantity } from '@/utils/formatter'
import download from '@/utils/download'
import { WorkOrderApi, WorkOrderVO } from '@/api/scm/mfg/workorder'
import WorkOrderForm from './WorkOrderForm.vue'
import WorkOrderDetailList from './components/WorkOrderDetailList.vue'
import PickingReceiptTable from './components/PickingReceiptTable.vue'
import ReportOrderTable from './components/ReportOrderTable.vue'
import InspectionTable from './components/InspectionTable.vue'
import ProductReceiptTable from './components/ProductReceiptTable.vue'
import { UnitApi } from '@/api/scm/base/unit'
import PickingReceiptForm from '../../inventory/pickingreceipt/PickingReceiptForm.vue'
import ReportOrderForm from '../reportorder/ReportOrderForm.vue'
import InspectionForm from '../../quality/inspection/InspectionForm.vue'
import ProductReceiptForm from '../../inventory/productreceipt/ProductReceiptForm.vue'
import ApproveInfoForm from '../../base/approveinfo/ApproveInfoForm.vue'
import { useClipboard } from '@vueuse/core'

import { View } from '@element-plus/icons-vue'
/** 生产任务 列表 */
defineOptions({ name: 'WorkOrder' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const { push } = useRouter() // 路由
const { copy } = useClipboard() // 复制功能

const loading = ref(true) // 列表的加载中
const list = ref<WorkOrderVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const expandSearchForm = ref(false) // 搜索表单展开状态
const activeTabName = ref('feedOrderDetail') // 当前激活的tab
const activeTaskTab = ref('all') // 当前激活的任务分类tab
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  planId: undefined,
  customerId: undefined,
  customerName: undefined,
  orderType: undefined,
  orderId: undefined,
  orderNo: undefined,
  productId: undefined,
  productCode: undefined,
  productName: undefined,
  productSubType: undefined,
  productUnit: undefined,
  spec: undefined,
  status: [],
  bomId: undefined,
  bomCode: undefined,
  bomVersion: undefined,
  progress: undefined,
  orderDate: [],
  orderQuantity: undefined,
  orderUnit: undefined,
  deliverDate: [],
  scheduleStartDate: [],
  scheduleStartTime: [],
  scheduleEndDate: [],
  scheduleEndTime: [],
  scheduleQuantity: undefined,
  schedulePiece: undefined,
  scheduleCostTime: [],
  scheduleLine: undefined,
  scheduleHeadcount: undefined,
  requirement: undefined,
  remark: undefined,
  actualLine: undefined,
  actualQuantity: undefined,
  actualStartTime: [],
  actualEndTime: [],
  actualCostTime: [],
  actualHeadcount: undefined,
  actualPiece: undefined,
  actualBatchNo: undefined,
  actualRemark: undefined,
  shareImageUrl: undefined,
  createTime: [],
  approveNo: undefined,
  approveStatus: undefined,
  pickingStatus: [],
  inStockStatus: [],
  reportStatus: [],
  qualityStatus: [],
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const selectedRows = ref<WorkOrderVO[]>([]) // 选中的行
const currentRow = ref<WorkOrderVO>({} as WorkOrderVO) // 选中行
const approveInfoFormRef = ref()

// 获取今天、昨天、明天的日期字符串
const getTodayDate = () => {
  const today = new Date()
  return today.toISOString().split('T')[0]
}

const getYesterdayDate = () => {
  const yesterday = new Date()
  yesterday.setDate(yesterday.getDate() - 1)
  return yesterday.toISOString().split('T')[0]
}

const getTomorrowDate = () => {
  const tomorrow = new Date()
  tomorrow.setDate(tomorrow.getDate() + 1)
  return tomorrow.toISOString().split('T')[0]
}

// 根据当前tab设置查询参数
const setQueryParamsByTab = () => {
  const today = getTodayDate()
  const yesterday = getYesterdayDate()
  const tomorrow = getTomorrowDate()

  // 重置相关查询参数
  queryParams.scheduleStartDate = []
  queryParams.status = []

  switch (activeTaskTab.value) {
    case 'yesterday':
      queryParams.scheduleStartDate = [`${yesterday} 00:00:00`, `${yesterday} 23:59:59`]
      break
    case 'today':
      queryParams.scheduleStartDate = [`${today} 00:00:00`, `${today} 23:59:59`]
      break
    case 'tomorrow':
      queryParams.scheduleStartDate = [`${tomorrow} 00:00:00`, `${tomorrow} 23:59:59`]
      break
    case 'unfinished':
      // 未完成状态：0, 1, 2, 3
      queryParams.status = ['0', '1', '2', '3']
      break
    case 'all':
    default:
      // 不设置任何过滤条件，显示所有数据
      break
  }
}

// 检查是否有用户主动输入的搜索条件
const hasSearchConditions = () => {
  // 检查基本搜索条件
  const hasBasicConditions = !!(
    queryParams.customerName ||
    queryParams.productName ||
    queryParams.productCode ||
    queryParams.orderType ||
    queryParams.orderNo ||
    queryParams.productSubType ||
    queryParams.bomCode ||
    queryParams.progress ||
    queryParams.approveNo ||
    queryParams.approveStatus ||
    (queryParams.orderDate && queryParams.orderDate.length > 0) ||
    (queryParams.pickingStatus && queryParams.pickingStatus.length > 0) ||
    (queryParams.inStockStatus && queryParams.inStockStatus.length > 0) ||
    (queryParams.reportStatus && queryParams.reportStatus.length > 0) ||
    (queryParams.qualityStatus && queryParams.qualityStatus.length > 0)
  )

  // 检查用户是否手动设置了状态（在搜索表单中设置的状态）
  const hasManualStatus = queryParams.status && queryParams.status.length > 0

  // 检查用户是否手动设置了计划日期（在搜索表单中设置的日期）
  const hasManualScheduleDate = queryParams.scheduleStartDate && queryParams.scheduleStartDate.length > 0

  return hasBasicConditions || hasManualStatus || hasManualScheduleDate
}

// 清除tab相关的过滤条件
const clearTabFilters = () => {
  // 只清除由tab设置的过滤条件，保留用户手动设置的搜索条件
  // 注意：这里不清除scheduleStartDate和status，因为用户可能手动设置了这些条件
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await WorkOrderApi.getWorkOrderPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  // 检查是否有搜索条件，如果有则切换到全部tab
  if (hasSearchConditions()) {
    activeTaskTab.value = 'all'
    // 清除tab相关的过滤条件，只保留用户输入的搜索条件
    clearTabFilters()
  } else {
    // 如果没有搜索条件，应用当前tab的过滤条件
    setQueryParamsByTab()
  }
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  // 重置后应用当前tab的过滤条件
  queryParams.pageNo = 1
  setQueryParamsByTab()
  getList()
}

/** 添加/修改操作 */
const formRef = ref()
/** 领料form */
const pickingFormRef = ref()
/** 报工form */
const reportOrderFormRef = ref()
/** 质检form */
const inspectionFormRef = ref()
/** 产品入库form */
const productReceiptFormRef = ref()

const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

const openPickingForm = (type: string, row: WorkOrderVO) => {
  pickingFormRef.value.open(type, null, row)
}

const openReportOrderForm = (type: string, row: WorkOrderVO) => {
  reportOrderFormRef.value.open(type, null, row.id)
}

const openInspectionForm = async (type: string, row: WorkOrderVO) => {
  try {
    // 处理工单数据，确保包含单位名称
    const processedRow = { ...row }

    // 如果有productUnit字段但没有unitName，则获取单位名称
    if (processedRow.productUnit && !processedRow.unitName) {
      try {
        const unitInfo = await UnitApi.getUnit(processedRow.productUnit)
        if (unitInfo) {
          processedRow.unitName = unitInfo.name
          processedRow.unit = processedRow.productUnit // 确保unit字段存在
        }
      } catch (error) {
        console.error('获取产品单位信息失败:', error)
      }
    }

    // 如果有orderUnit字段，也尝试获取单位名称（作为备用）
    if (!processedRow.unitName && processedRow.orderUnit) {
      try {
        // orderUnit可能是字符串类型的单位ID
        const unitId = typeof processedRow.orderUnit === 'string' ? parseInt(processedRow.orderUnit) : processedRow.orderUnit
        if (!isNaN(unitId)) {
          const unitInfo = await UnitApi.getUnit(unitId)
          if (unitInfo) {
            processedRow.unitName = unitInfo.name
            processedRow.unit = unitId
          }
        }
      } catch (error) {
        console.error('获取订单单位信息失败:', error)
      }
    }

    inspectionFormRef.value.open(type, null, processedRow)
  } catch (error) {
    console.error('打开质检表单失败:', error)
    // 如果处理失败，仍然尝试打开表单
    inspectionFormRef.value.open(type, null, row)
  }
}

const openProductReceiptForm = (type: string, row: WorkOrderVO) => {
  productReceiptFormRef.value.open(type, null, row)
}

/** tab切换处理 */
const handleTabChange = (tabName: string) => {
  console.log('切换到tab:', tabName)
  activeTabName.value = tabName
}

/** 任务分类tab切换处理 */
const handleTaskTabChange = (tabName: string) => {
  console.log('切换到任务分类tab:', tabName)
  activeTaskTab.value = tabName
  // 重置分页到第一页
  queryParams.pageNo = 1
  // 根据tab设置查询参数
  setQueryParamsByTab()
  // 重新获取数据
  getList()
}
/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await WorkOrderApi.deleteWorkOrder(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await WorkOrderApi.exportWorkOrder(queryParams)
    download.excel(data, '生产任务.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 选择变化处理 */
const handleSelectionChange = (selection: WorkOrderVO[]) => {
  selectedRows.value = selection
}

/** 审核按钮操作 */
const handleApproval = async () => {
  console.log('handleApproval', selectedRows.value)
  if(!selectedRows.value || selectedRows.value.length === 0) {
    message.error('请选择要审核的工单！')
    return
  }

  // 如果选中了多个工单，提示用户
  if(selectedRows.value.length > 1) {
    message.warning('当前只支持单个工单审核，请选择一个工单进行审核')
    return
  }

  // 设置当前行为选中的第一个工单
  const selectedWorkOrder = selectedRows.value[0]
  currentRow.value = selectedWorkOrder

  // 发起审批，传入业务数据
  approveInfoFormRef.value.open('approve', undefined, {
    bizId: selectedWorkOrder.id,
    bizNo: selectedWorkOrder.workNo,
    bizType: 'mfg_work_order'
  })
}

// 获取单位列表
const unitList = ref([]) // 单位列表
const unitMap = ref<Map<number, string>>(new Map()) // 单位映射
const getUnitList = async () => {
  try {
    const data = await UnitApi.getUnitPage({
      pageNo: 1,
      pageSize: 100,
    })
    // 将单位列表转换为映射
    unitMap.value = new Map(data.list.map((item) => [item.id, item.name]))
    console.log('单位映射:', unitMap.value)
    unitList.value = data.list
  } catch (error) {
    console.error('获取单位列表失败:', error)
  }
}


// 处理下拉菜单命令
const handleCommand = (command: string, row: WorkOrderVO) => {
  if (command === 'update') {
    openForm('update', row.id)
  } else if (command === 'delete') {
    handleDelete(row.id)
  } else if (command === 'inspection') {
    openInspectionForm('create', row)
  } else if (command === 'stockIn') {
    openProductReceiptForm('create', row)
  }
}

/** 打开工单详情页面 */
const openWorkOrderDetail = (workOrderId: number) => {
  push({
    name: 'WorkOrderDetail',
    params: { id: workOrderId.toString() }
  })
}



/** 切换查询条件展开/收起状态 */
const toggleSearchForm = () => {
  expandSearchForm.value = !expandSearchForm.value
}

/** 复制单号 */
const copyOrderNo = async (orderNo: string) => {
  try {
    await copy(orderNo)
    message.success('单号复制成功')
  } catch (error) {
    message.error('复制失败')
  }
}

/** 表格汇总方法 */
const getSummaries = (param: any) => {
  const { columns, data } = param
  const sums: string[] = []

  columns.forEach((column: any, index: number) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }

    // 需要汇总的数量字段
    const quantityFields = [
      'orderQuantity', 'scheduleQuantity', 'schedulePiece',
      'actualQuantity', 'actualPiece'
    ]

    // 需要汇总的人数字段
    const headcountFields = [
      'scheduleHeadcount', 'actualHeadcount'
    ]

    // 需要汇总的时间字段（分钟）
    const timeFields = [
      'scheduleCostTime', 'actualCostTime'
    ]

    if (quantityFields.includes(column.property)) {
      // 数量字段汇总
      const values = data.map((item: any) => Number(item[column.property]) || 0)
      const total = values.reduce((prev: number, curr: number) => prev + curr, 0)
      sums[index] = total > 0 ? formatQuantity(total) : '0'
    } else if (headcountFields.includes(column.property)) {
      // 人数字段汇总
      const values = data.map((item: any) => Number(item[column.property]) || 0)
      const total = values.reduce((prev: number, curr: number) => prev + curr, 0)
      sums[index] = total > 0 ? `${total}人` : '0人'
    } else if (timeFields.includes(column.property)) {
      // 时间字段汇总（分钟转换为小时分钟格式）
      const values = data.map((item: any) => Number(item[column.property]) || 0)
      const total = values.reduce((prev: number, curr: number) => prev + curr, 0)
      sums[index] = formatMinutesToTime(total)
    } else {
      // 其他列不显示汇总信息
      sums[index] = ''
    }
  })

  return sums
}

/** 获取进度条颜色 */
const getProgressColor = (percentage: number) => {
  if (percentage >= 100) {
    return '#67c23a' // 绿色 - 已完成
  } else if (percentage >= 80) {
    return '#409eff' // 蓝色 - 接近完成
  } else if (percentage >= 50) {
    return '#e6a23c' // 橙色 - 进行中
  } else if (percentage > 0) {
    return '#f56c6c' // 红色 - 刚开始
  } else {
    return '#dcdfe6' // 灰色 - 未开始
  }
}

/** 将分钟数转换为"xx小时xx分钟"格式 */
const formatMinutesToTime = (minutes: number) => {
  if (!minutes || minutes <= 0) {
    return '0分钟'
  }

  const hours = Math.floor(minutes / 60)
  const remainingMinutes = minutes % 60

  if (hours > 0 && remainingMinutes > 0) {
    return `${hours}小时${remainingMinutes}分钟`
  } else if (hours > 0) {
    return `${hours}小时`
  } else {
    return `${remainingMinutes}分钟`
  }
}

/** 初始化 **/
onMounted(() => {
  // 根据默认tab设置查询参数
  setQueryParamsByTab()
  getList()
  getUnitList()
})
</script>
<style scoped>
.more-btn {
  display: inline-flex;
  align-items: center;
  vertical-align: top;
  margin-top: 4px;
}

.more-btn .el-dropdown-link {
  display: flex;
  padding: 0 5px;
  font-size: 14px;
  font-weight: 500;
  color: var(--el-color-primary);
  vertical-align: middle !important;
  cursor: pointer;
  align-items: center;
  justify-content: center;
}

.text-yellow-500 {
  color: #eab308; /* 黄色高亮 */
}

/* Tab样式优化 */
.el-tabs__item {
  font-weight: 500;
}

.mb-4 {
  margin-bottom: 16px;
}

/* 生产单号容器样式 */
.work-no-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
  padding: 4px 0;
}

/* 生产单号链接样式 */
.work-no-link {
  color: #409eff;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    color: #66b1ff;
    text-decoration: underline;
  }

  &:active {
    color: #3a8ee6;
  }
}

/* 状态标签样式 */
.status-tag :deep(.el-tag) {
  font-size: 9px !important;
  padding: 1px 4px !important;
  height: 16px !important;
  line-height: 14px !important;
  border-radius: 6px !important;
  font-weight: 500 !important;
  flex-shrink: 0;
}

/* 进度条容器样式 */
.progress-container {
  padding: 0 8px;
}

.progress-container :deep(.el-progress) {
  line-height: 1;
}

.progress-container :deep(.el-progress-bar) {
  padding-right: 0;
  margin-right: 0;
}

.progress-container :deep(.el-progress__text) {
  font-size: 12px;
  font-weight: 500;
  margin-left: 8px;
}

/* 复制按钮样式 */
.order-no-cell {
  display: flex;
  align-items: center;
  width: 100%;
  min-height: 20px;
  gap: 5px; /* 文字和图标之间保持一点点间隔 */
}

.order-no-content {
  flex: 0 1 auto; /* 改为自适应宽度，不占满剩余空间 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: calc(100% - 16px); /* 为图标预留空间 */
}

.copy-btn {
  padding: 2px !important;
  height: 20px !important;
  min-height: 20px !important;
  width: 20px !important;
  font-size: 12px;
  opacity: 0.6;
  transition: opacity 0.2s;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}



.copy-btn:hover {
  opacity: 1;
}
</style>
