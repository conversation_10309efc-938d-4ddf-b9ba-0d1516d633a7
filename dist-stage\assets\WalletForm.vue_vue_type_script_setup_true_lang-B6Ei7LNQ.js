import{_ as f}from"./Dialog.vue_vue_type_style_index_0_lang-DpoeWsKb.js";import"./index-BeQABqnP.js";import{_}from"./WalletTransactionList.vue_vue_type_script_setup_true_lang-DgnH02M5.js";import{f as c}from"./form-designer-DQFPUccF.js";import{k as v,r as l,y as w,m as V,z as t,H as n,u as o,E as h,h as k}from"./form-create-B86qX0W_.js";const x=v({__name:"WalletForm",setup(y,{expose:i}){const e=l(!1),r=l("");l(!1);const u=l(0);return i({open:async m=>{e.value=!0,r.value="\u94B1\u5305\u4F59\u989D\u660E\u7EC6",u.value=m}}),(m,a)=>{const p=c,d=f;return V(),w(d,{title:o(r),modelValue:o(e),"onUpdate:modelValue":a[1]||(a[1]=s=>k(e)?e.value=s:null),width:"800"},{footer:t(()=>[n(p,{onClick:a[0]||(a[0]=s=>e.value=!1)},{default:t(()=>a[2]||(a[2]=[h("\u53D6 \u6D88")])),_:1})]),default:t(()=>[n(_,{"wallet-id":o(u)},null,8,["wallet-id"])]),_:1},8,["title","modelValue"])}}});export{x as _};
