import{aC as T,M as _,c as S}from"./index-BeQABqnP.js";import i from"./ThingModelInputOutputParam-CGyBtxFk.js";import{f as m,h as p}from"./config-BXOiFKPF.js";import{aj as g,s as b,u as y}from"./form-designer-DQFPUccF.js";import{k as C,b as N,l as P,m as U,G as Y,H as l,z as t,u as e,E as n,F as c}from"./form-create-B86qX0W_.js";import"./Dialog.vue_vue_type_style_index_0_lang-DpoeWsKb.js";import"./ThingModelProperty-BG6FrGUo.js";import"./ThingModelEnumDataSpecs-B1l6ptpN.js";import"./ThingModelNumberDataSpecs-CDIi9w7g.js";const h=S(C({name:"ThingModelService",__name:"ThingModelService",props:{modelValue:{},isStructDataSpecs:{type:Boolean}},emits:["update:modelValue"],setup(f,{emit:v}){const a=T(f,"modelValue",v);return N(()=>a.value.callType,s=>_(s)&&(a.value.callType=m.ASYNC.value),{immediate:!0}),(s,o)=>{const d=y,V=b,r=g;return U(),P(Y,null,[l(r,{rules:[{required:!0,message:"\u8BF7\u9009\u62E9\u8C03\u7528\u65B9\u5F0F",trigger:"change"}],label:"\u8C03\u7528\u65B9\u5F0F",prop:"service.callType"},{default:t(()=>[l(V,{modelValue:e(a).callType,"onUpdate:modelValue":o[0]||(o[0]=u=>e(a).callType=u)},{default:t(()=>[l(d,{value:e(m).ASYNC.value},{default:t(()=>[n(c(e(m).ASYNC.label),1)]),_:1},8,["value"]),l(d,{value:e(m).SYNC.value},{default:t(()=>[n(c(e(m).SYNC.label),1)]),_:1},8,["value"])]),_:1},8,["modelValue"])]),_:1}),l(r,{label:"\u8F93\u5165\u53C2\u6570"},{default:t(()=>[l(i,{modelValue:e(a).inputParams,"onUpdate:modelValue":o[1]||(o[1]=u=>e(a).inputParams=u),direction:e(p).INPUT},null,8,["modelValue","direction"])]),_:1}),l(r,{label:"\u8F93\u51FA\u53C2\u6570"},{default:t(()=>[l(i,{modelValue:e(a).outputParams,"onUpdate:modelValue":o[2]||(o[2]=u=>e(a).outputParams=u),direction:e(p).OUTPUT},null,8,["modelValue","direction"])]),_:1})],64)}}}),[["__scopeId","data-v-87151372"]]);export{h as default};
