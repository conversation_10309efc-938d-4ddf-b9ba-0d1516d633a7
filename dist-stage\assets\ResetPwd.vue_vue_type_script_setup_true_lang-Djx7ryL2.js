import{a as b,d as _}from"./index-BeQABqnP.js";import{_ as V}from"./XButton-BWhMH4NC.js";import{I as i}from"./InputPassword-Cl5M8vRV.js";import{a as y}from"./profile-DcD6FaJA.js";import{aj as v,h as R}from"./form-designer-DQFPUccF.js";import{k as h,r as k,P as p,y as q,m as x,z as w,H as a,u as e}from"./form-create-B86qX0W_.js";const M=h({name:"ResetPwd",__name:"ResetPwd",setup(U){const{t:r}=b(),u=_(),m=k(),o=p({oldPassword:"",newPassword:"",confirmPassword:""}),f=p({oldPassword:[{required:!0,message:r("profile.password.oldPwdMsg"),trigger:"blur"},{min:6,max:20,message:r("profile.password.pwdRules"),trigger:"blur"}],newPassword:[{required:!0,message:r("profile.password.newPwdMsg"),trigger:"blur"},{min:6,max:20,message:r("profile.password.pwdRules"),trigger:"blur"}],confirmPassword:[{required:!0,message:r("profile.password.cfPwdMsg"),trigger:"blur"},{required:!0,validator:(P,s,l)=>{o.newPassword!==s?l(new Error(r("profile.password.diffPwd"))):l()},trigger:"blur"}]});return(P,s)=>{const l=v,n=V,g=R;return x(),q(g,{ref_key:"formRef",ref:m,model:e(o),rules:e(f),"label-width":200},{default:w(()=>[a(l,{label:e(r)("profile.password.oldPassword"),prop:"oldPassword"},{default:w(()=>[a(e(i),{modelValue:e(o).oldPassword,"onUpdate:modelValue":s[0]||(s[0]=d=>e(o).oldPassword=d)},null,8,["modelValue"])]),_:1},8,["label"]),a(l,{label:e(r)("profile.password.newPassword"),prop:"newPassword"},{default:w(()=>[a(e(i),{modelValue:e(o).newPassword,"onUpdate:modelValue":s[1]||(s[1]=d=>e(o).newPassword=d),strength:""},null,8,["modelValue"])]),_:1},8,["label"]),a(l,{label:e(r)("profile.password.confirmPassword"),prop:"confirmPassword"},{default:w(()=>[a(e(i),{modelValue:e(o).confirmPassword,"onUpdate:modelValue":s[2]||(s[2]=d=>e(o).confirmPassword=d),strength:""},null,8,["modelValue"])]),_:1},8,["label"]),a(l,null,{default:w(()=>[a(n,{title:e(r)("common.save"),type:"primary",onClick:s[3]||(s[3]=d=>{var t;(t=e(m))&&t.validate(async c=>{c&&(await y(o.oldPassword,o.newPassword),u.success(r("common.updateSuccess")))})})},null,8,["title"]),a(n,{title:e(r)("common.reset"),type:"danger",onClick:s[4]||(s[4]=d=>{var t;(t=e(m))&&t.resetFields()})},null,8,["title"])]),_:1})]),_:1},8,["model","rules"])}}});export{M as _};
