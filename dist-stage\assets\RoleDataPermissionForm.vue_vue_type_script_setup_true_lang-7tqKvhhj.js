import{a as z,d as G,h as H,D as K}from"./index-BeQABqnP.js";import{_ as Q}from"./Dialog.vue_vue_type_style_index_0_lang-DpoeWsKb.js";import{d as Y,h as Z}from"./tree-COGD3qag.js";import{q as C}from"./constants-C3gLHYOK.js";import{g as $}from"./index-CxjbpoB6.js";import{b as B}from"./index-9g_ZCIuI.js";import{h as J,aj as L,a6 as W,x as X,Q as ee,ak as ae,ad as le,y as te,D as oe,f as de}from"./form-designer-DQFPUccF.js";import{k as se,r as u,P as ue,y as h,m as f,z as d,A as ne,C as ce,u as a,H as s,E as c,F as D,l as ie,G as me,$ as pe,h as y,n as re}from"./form-create-B86qX0W_.js";const ve=se({name:"SystemRoleDataPermissionForm",__name:"RoleDataPermissionForm",emits:["success"],setup(fe,{expose:w,emit:U}){const{t:P}=z(),T=G(),i=u(!1),S=u(!1),o=ue({id:void 0,name:"",code:"",dataScope:void 0,dataScopeDeptIds:[]}),V=u(),k=u([]),m=u(!0),p=u(),r=u(!1),v=u(!0);w({open:async l=>{var e;i.value=!0,R(),k.value=Z(await $()),o.id=l.id,o.name=l.name,o.code=l.code,o.dataScope=l.dataScope,await re(),(e=l.dataScopeDeptIds)==null||e.forEach(n=>{p.value.setChecked(n,!0,!1)})}});const E=U,I=async()=>{S.value=!0;try{const l={roleId:o.id,dataScope:o.dataScope,dataScopeDeptIds:o.dataScope!==C.DEPT_CUSTOM?[]:p.value.getCheckedKeys(!1)};await B(l),T.success(P("common.updateSuccess")),i.value=!1,E("success")}finally{S.value=!1}},R=()=>{var l,e;r.value=!1,m.value=!0,v.value=!0,o.value={id:void 0,name:"",code:"",dataScope:void 0,dataScopeDeptIds:[]},(l=p.value)==null||l.setCheckedNodes([]),(e=V.value)==null||e.resetFields()},g=()=>{var e;const l=(e=p.value)==null?void 0:e.store.nodesMap;for(let n in l)l[n].expanded!==m.value&&(l[n].expanded=m.value)};return(l,e)=>{const n=W,_=L,A=ee,F=X,M=J,x=oe,O=te,N=le,b=de,j=Q,q=ae;return f(),h(j,{modelValue:a(i),"onUpdate:modelValue":e[6]||(e[6]=t=>y(i)?i.value=t:null),title:"\u6570\u636E\u6743\u9650",width:"800"},{footer:d(()=>[s(b,{disabled:a(S),type:"primary",onClick:I},{default:d(()=>e[10]||(e[10]=[c("\u786E \u5B9A")])),_:1},8,["disabled"]),s(b,{onClick:e[5]||(e[5]=t=>i.value=!1)},{default:d(()=>e[11]||(e[11]=[c("\u53D6 \u6D88")])),_:1})]),default:d(()=>[ne((f(),h(M,{ref_key:"formRef",ref:V,model:a(o),"label-width":"80px"},{default:d(()=>[s(_,{label:"\u89D2\u8272\u540D\u79F0"},{default:d(()=>[s(n,null,{default:d(()=>[c(D(a(o).name),1)]),_:1})]),_:1}),s(_,{label:"\u89D2\u8272\u6807\u8BC6"},{default:d(()=>[s(n,null,{default:d(()=>[c(D(a(o).code),1)]),_:1})]),_:1}),s(_,{label:"\u6743\u9650\u8303\u56F4"},{default:d(()=>[s(F,{modelValue:a(o).dataScope,"onUpdate:modelValue":e[0]||(e[0]=t=>a(o).dataScope=t)},{default:d(()=>[(f(!0),ie(me,null,pe(a(H)(a(K).SYSTEM_DATA_SCOPE),t=>(f(),h(A,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])),[[q,a(S)]]),a(o).dataScope===a(C).DEPT_CUSTOM?(f(),h(_,{key:0,label:"\u90E8\u95E8\u8303\u56F4","label-width":"80px"},{default:d(()=>[s(N,{class:"w-full h-400px !overflow-y-scroll",shadow:"never"},{header:d(()=>[e[7]||(e[7]=c(" \u5168\u9009/\u5168\u4E0D\u9009: ")),s(x,{modelValue:a(r),"onUpdate:modelValue":e[1]||(e[1]=t=>y(r)?r.value=t:null),"active-text":"\u662F","inactive-text":"\u5426","inline-prompt":"",onChange:e[2]||(e[2]=t=>{p.value.setCheckedNodes(r.value?k.value:[])})},null,8,["modelValue"]),e[8]||(e[8]=c(" \u5168\u90E8\u5C55\u5F00/\u6298\u53E0: ")),s(x,{modelValue:a(m),"onUpdate:modelValue":e[3]||(e[3]=t=>y(m)?m.value=t:null),"active-text":"\u5C55\u5F00","inactive-text":"\u6298\u53E0","inline-prompt":"",onChange:g},null,8,["modelValue"]),e[9]||(e[9]=c(" \u7236\u5B50\u8054\u52A8(\u9009\u4E2D\u7236\u8282\u70B9\uFF0C\u81EA\u52A8\u9009\u62E9\u5B50\u8282\u70B9): ")),s(x,{modelValue:a(v),"onUpdate:modelValue":e[4]||(e[4]=t=>y(v)?v.value=t:null),"active-text":"\u662F","inactive-text":"\u5426","inline-prompt":""},null,8,["modelValue"])]),default:d(()=>[s(O,{ref_key:"treeRef",ref:p,"check-strictly":!a(v),data:a(k),props:a(Y),"default-expand-all":"","empty-text":"\u52A0\u8F7D\u4E2D\uFF0C\u8BF7\u7A0D\u540E","node-key":"id","show-checkbox":""},null,8,["check-strictly","data","props"])]),_:1})]),_:1})):ce("",!0)]),_:1},8,["modelValue"])}}});export{ve as _};
