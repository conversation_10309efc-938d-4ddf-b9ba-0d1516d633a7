import{aC as m,M as c}from"./index-BeQABqnP.js";import{I as n}from"./index-DSlr-2zm.js";import{aj as V,k as i}from"./form-designer-DQFPUccF.js";import{k as f,e as g,l as y,m as K,G as _,H as l,z as u,u as o}from"./form-create-B86qX0W_.js";const v=f({name:"RocketMQConfigForm",__name:"RocketMQConfigForm",props:{modelValue:{}},emits:["update:modelValue"],setup(t,{emit:d}){const e=m(t,"modelValue",d);return g(()=>{c(e.value)&&(e.value={type:n.ROCKETMQ,nameServer:"",accessKey:"",secretKey:"",group:"",topic:"",tags:""})}),(S,a)=>{const s=i,p=V;return K(),y(_,null,[l(p,{label:"NameServer",prop:"config.nameServer"},{default:u(()=>[l(s,{modelValue:o(e).nameServer,"onUpdate:modelValue":a[0]||(a[0]=r=>o(e).nameServer=r),placeholder:"\u8BF7\u8F93\u5165 NameServer \u5730\u5740\uFF0C\u5982\uFF1A127.0.0.1:9876"},null,8,["modelValue"])]),_:1}),l(p,{label:"AccessKey",prop:"config.accessKey"},{default:u(()=>[l(s,{modelValue:o(e).accessKey,"onUpdate:modelValue":a[1]||(a[1]=r=>o(e).accessKey=r),placeholder:"\u8BF7\u8F93\u5165 AccessKey"},null,8,["modelValue"])]),_:1}),l(p,{label:"SecretKey",prop:"config.secretKey"},{default:u(()=>[l(s,{modelValue:o(e).secretKey,"onUpdate:modelValue":a[2]||(a[2]=r=>o(e).secretKey=r),placeholder:"\u8BF7\u8F93\u5165 SecretKey","show-password":"",type:"password"},null,8,["modelValue"])]),_:1}),l(p,{label:"\u6D88\u8D39\u7EC4",prop:"config.group"},{default:u(()=>[l(s,{modelValue:o(e).group,"onUpdate:modelValue":a[3]||(a[3]=r=>o(e).group=r),placeholder:"\u8BF7\u8F93\u5165\u6D88\u8D39\u7EC4"},null,8,["modelValue"])]),_:1}),l(p,{label:"\u4E3B\u9898",prop:"config.topic"},{default:u(()=>[l(s,{modelValue:o(e).topic,"onUpdate:modelValue":a[4]||(a[4]=r=>o(e).topic=r),placeholder:"\u8BF7\u8F93\u5165\u4E3B\u9898"},null,8,["modelValue"])]),_:1}),l(p,{label:"\u6807\u7B7E",prop:"config.tags"},{default:u(()=>[l(s,{modelValue:o(e).tags,"onUpdate:modelValue":a[5]||(a[5]=r=>o(e).tags=r),placeholder:"\u8BF7\u8F93\u5165\u6807\u7B7E"},null,8,["modelValue"])]),_:1})],64)}}});export{v as _};
