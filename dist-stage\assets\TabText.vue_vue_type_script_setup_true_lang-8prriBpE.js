import"./index-BeQABqnP.js";import{k as p}from"./form-designer-DQFPUccF.js";import{k as d,c as n,y as i,m as V,u as c,h as x}from"./form-create-B86qX0W_.js";const _=d({__name:"TabText",props:{modelValue:{}},emits:["update:modelValue","input"],setup(o,{emit:u}){const m=o,t=u,e=n({get:()=>m.modelValue,set:a=>{t("update:modelValue",a),t("input",a)}});return(a,l)=>{const s=p;return V(),i(s,{type:"textarea",rows:5,placeholder:"\u8BF7\u8F93\u5165\u5185\u5BB9",modelValue:c(e),"onUpdate:modelValue":l[0]||(l[0]=r=>x(e)?e.value=r:null)},null,8,["modelValue"])}}});export{_};
