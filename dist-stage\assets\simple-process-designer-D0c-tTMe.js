import{_ as Xl}from"./Dialog.vue_vue_type_style_index_0_lang-DpoeWsKb.js";import{d as Vl,_ as me,ar as Te,D as Fl,c as yl,Q as _t}from"./index-BeQABqnP.js";import{_ as Ll}from"./DictTag.vue_vue_type_script_lang-B0tTMPJ6.js";import{k as pe,r as $,i as ve,l as i,m as l,v as o,y as c,C as N,u as e,h as ie,z as a,H as t,af as ke,E as P,F as M,G as L,$ as j,A as Ee,a8 as Ne,B as Se,c as Ue,P as tl,b as Cl,g as Rl,e as Wl,a6 as bt,p as Et,N as Nt}from"./form-create-B86qX0W_.js";import{a as w,A as Tt,c as Hl,R as ql,d as al,e as ul,f as _e,D as Me,F as de,S as kt,g as Be,C as oe,P as St,h as gl,T as Re,i as Ql,j as Ut,k as Jl,M as Zl,l as wt,m as Vt,n as Ct,o as Dl,p as Rt,q as Dt,b as et,r as xt,s as It,t as lt,u as tt,v as Ve,w as at,x as ye,y as Ot,z as hl,B as ot,E as nt,G as At,H as Pt,I as Ft}from"./consts-CvaDWb4S.js";import{n as Lt,K as De,aG as Ye,a0 as xl,$ as Il,a7 as _l,o as it,s as ol,u as cl,V as je,f as Le,v as Ml,i as bl,Z as Gl,_ as zl,h as Ke,aj as Xe,D as El,R as Ht,k as pl,j as qt,x as We,Q as Qe,a1 as dt,l as Bl,t as $l,at as st,ad as Ol,F as rt,aH as Mt,e as Gt,a6 as zt,aq as Bt,aI as $t,aJ as jt,aK as Kt}from"./form-designer-DQFPUccF.js";import{a as nl,u as Ce,b as il,c as jl,d as dl,e as Ie,f as ut,g as Al,h as ct,i as Yt}from"./node-CMR68ZU9.js";import{d as ml,a as Kl}from"./formatTime-CN67D7Gb.js";import{d as pt}from"./tree-COGD3qag.js";import{g as Xt,c as Yl,a as mt,b as Wt}from"./utils-CmcUoW9l.js";import{_ as Qt,a as Jt}from"./HttpRequestSetting.vue_vue_type_script_setup_true_lang-CDPNdlWq.js";import{B as vt}from"./constants-C3gLHYOK.js";import{a as Zt}from"./index-53YAIPW1.js";import{g as ea}from"./index-Dj2w7lLz.js";import{p as la}from"./index-BCN8BzfC.js";import{d as ta}from"./download-oWiM5xVU.js";const aa={class:"node-handler-wrapper"},oa={class:"node-handler"},na={class:"handler-item-wrapper"},ia={class:"add-icon"},He=pe({name:"NodeHandler",__name:"NodeHandler",props:{childNode:{type:Object,default:null},currentNode:{type:Object,required:!0}},emits:["update:childNode"],setup(Q,{emit:le}){const X=Vl(),U=$(!1),v=Q,n=le,d=ve("readonly"),x=I=>{var h;if(I===w.PARALLEL_BRANCH_NODE&&[w.CONDITION_BRANCH_NODE,w.INCLUSIVE_BRANCH_NODE].includes((h=v.currentNode)==null?void 0:h.type))X.error("\u6761\u4EF6\u5206\u652F\u3001\u5305\u5BB9\u5206\u652F\u540E\u9762\uFF0C\u4E0D\u5141\u8BB8\u76F4\u63A5\u6DFB\u52A0\u5E76\u884C\u5206\u652F");else{if(U.value=!1,I===w.USER_TASK_NODE||I===w.TRANSACTOR_NODE){const R={id:"Activity_"+Te(),name:ul.get(I),showText:"",type:I,approveMethod:al.SEQUENTIAL_APPROVE,rejectHandler:{type:ql.FINISH_PROCESS},timeoutHandler:{enable:!1},assignEmptyHandler:{type:Hl.APPROVE},assignStartUserHandlerType:Tt.START_USER_AUDIT,childNode:v.childNode,taskCreateListener:{enable:!1},taskAssignListener:{enable:!1},taskCompleteListener:{enable:!1}};n("update:childNode",R)}if(I===w.COPY_TASK_NODE){const R={id:"Activity_"+Te(),name:ul.get(w.COPY_TASK_NODE),showText:"",type:w.COPY_TASK_NODE,childNode:v.childNode};n("update:childNode",R)}if(I===w.CONDITION_BRANCH_NODE){const R={name:"\u6761\u4EF6\u5206\u652F",type:w.CONDITION_BRANCH_NODE,id:"GateWay_"+Te(),childNode:v.childNode,conditionNodes:[{id:"Flow_"+Te(),name:"\u6761\u4EF61",showText:"",type:w.CONDITION_NODE,childNode:void 0,conditionSetting:{defaultFlow:!1,conditionType:_e.RULE,conditionGroups:De(Me)}},{id:"Flow_"+Te(),name:"\u5176\u5B83\u60C5\u51B5",showText:"\u672A\u6EE1\u8DB3\u5176\u5B83\u6761\u4EF6\u65F6\uFF0C\u5C06\u8FDB\u5165\u6B64\u5206\u652F",type:w.CONDITION_NODE,childNode:void 0,conditionSetting:{defaultFlow:!0}}]};n("update:childNode",R)}if(I===w.PARALLEL_BRANCH_NODE){const R={name:"\u5E76\u884C\u5206\u652F",type:w.PARALLEL_BRANCH_NODE,id:"GateWay_"+Te(),childNode:v.childNode,conditionNodes:[{id:"Flow_"+Te(),name:"\u5E76\u884C1",showText:"\u65E0\u9700\u914D\u7F6E\u6761\u4EF6\u540C\u65F6\u6267\u884C",type:w.CONDITION_NODE,childNode:void 0},{id:"Flow_"+Te(),name:"\u5E76\u884C2",showText:"\u65E0\u9700\u914D\u7F6E\u6761\u4EF6\u540C\u65F6\u6267\u884C",type:w.CONDITION_NODE,childNode:void 0}]};n("update:childNode",R)}if(I===w.INCLUSIVE_BRANCH_NODE){const R={name:"\u5305\u5BB9\u5206\u652F",type:w.INCLUSIVE_BRANCH_NODE,id:"GateWay_"+Te(),childNode:v.childNode,conditionNodes:[{id:"Flow_"+Te(),name:"\u5305\u5BB9\u6761\u4EF61",showText:"",type:w.CONDITION_NODE,childNode:void 0,conditionSetting:{defaultFlow:!1,conditionType:_e.RULE,conditionGroups:De(Me)}},{id:"Flow_"+Te(),name:"\u5176\u5B83\u60C5\u51B5",showText:"\u672A\u6EE1\u8DB3\u5176\u5B83\u6761\u4EF6\u65F6\uFF0C\u5C06\u8FDB\u5165\u6B64\u5206\u652F",type:w.CONDITION_NODE,childNode:void 0,conditionSetting:{defaultFlow:!0}}]};n("update:childNode",R)}if(I===w.DELAY_TIMER_NODE){const R={id:"Activity_"+Te(),name:ul.get(w.DELAY_TIMER_NODE),showText:"",type:w.DELAY_TIMER_NODE,childNode:v.childNode};n("update:childNode",R)}if(I===w.ROUTER_BRANCH_NODE){const R={id:"GateWay_"+Te(),name:ul.get(w.ROUTER_BRANCH_NODE),showText:"",type:w.ROUTER_BRANCH_NODE,childNode:v.childNode};n("update:childNode",R)}if(I===w.TRIGGER_NODE){const R={id:"Activity_"+Te(),name:ul.get(w.TRIGGER_NODE),showText:"",type:w.TRIGGER_NODE,childNode:v.childNode};n("update:childNode",R)}if(I===w.CHILD_PROCESS_NODE){const R={id:"Activity_"+Te(),name:ul.get(w.CHILD_PROCESS_NODE),showText:"",type:w.CHILD_PROCESS_NODE,childNode:v.childNode,childProcessSetting:{calledProcessDefinitionKey:"",calledProcessDefinitionName:"",async:!1,skipStartUserNode:!1,startUserSetting:{type:1},timeoutSetting:{enable:!1},multiInstanceSetting:{enable:!1}}};n("update:childNode",R)}}};return(I,h)=>{const R=me,S=Lt;return l(),i("div",aa,[o("div",oa,[e(d)?N("",!0):(l(),c(S,{key:0,trigger:"hover",visible:e(U),"onUpdate:visible":h[10]||(h[10]=k=>ie(U)?U.value=k:null),placement:"right-start",width:"auto"},{reference:a(()=>[o("div",ia,[t(R,{icon:"ep:plus"})])]),default:a(()=>[o("div",na,[o("div",{class:"handler-item",onClick:h[0]||(h[0]=k=>x(e(w).USER_TASK_NODE))},h[11]||(h[11]=[o("div",{class:"approve handler-item-icon"},[o("span",{class:"iconfont icon-approve icon-size"})],-1),o("div",{class:"handler-item-text"},"\u5BA1\u6279\u4EBA",-1)])),o("div",{class:"handler-item",onClick:h[1]||(h[1]=k=>x(e(w).TRANSACTOR_NODE))},h[12]||(h[12]=[o("div",{class:"transactor handler-item-icon"},[o("span",{class:"iconfont icon-transactor icon-size"})],-1),o("div",{class:"handler-item-text"},"\u529E\u7406\u4EBA",-1)])),o("div",{class:"handler-item",onClick:h[2]||(h[2]=k=>x(e(w).COPY_TASK_NODE))},h[13]||(h[13]=[o("div",{class:"handler-item-icon copy"},[o("span",{class:"iconfont icon-size icon-copy"})],-1),o("div",{class:"handler-item-text"},"\u6284\u9001",-1)])),o("div",{class:"handler-item",onClick:h[3]||(h[3]=k=>x(e(w).CONDITION_BRANCH_NODE))},h[14]||(h[14]=[o("div",{class:"handler-item-icon condition"},[o("span",{class:"iconfont icon-size icon-exclusive"})],-1),o("div",{class:"handler-item-text"},"\u6761\u4EF6\u5206\u652F",-1)])),o("div",{class:"handler-item",onClick:h[4]||(h[4]=k=>x(e(w).PARALLEL_BRANCH_NODE))},h[15]||(h[15]=[o("div",{class:"handler-item-icon parallel"},[o("span",{class:"iconfont icon-size icon-parallel"})],-1),o("div",{class:"handler-item-text"},"\u5E76\u884C\u5206\u652F",-1)])),o("div",{class:"handler-item",onClick:h[5]||(h[5]=k=>x(e(w).INCLUSIVE_BRANCH_NODE))},h[16]||(h[16]=[o("div",{class:"handler-item-icon inclusive"},[o("span",{class:"iconfont icon-size icon-inclusive"})],-1),o("div",{class:"handler-item-text"},"\u5305\u5BB9\u5206\u652F",-1)])),o("div",{class:"handler-item",onClick:h[6]||(h[6]=k=>x(e(w).DELAY_TIMER_NODE))},h[17]||(h[17]=[o("div",{class:"handler-item-icon delay"},[o("span",{class:"iconfont icon-size icon-delay"})],-1),o("div",{class:"handler-item-text"},"\u5EF6\u8FDF\u5668",-1)])),o("div",{class:"handler-item",onClick:h[7]||(h[7]=k=>x(e(w).ROUTER_BRANCH_NODE))},h[18]||(h[18]=[o("div",{class:"handler-item-icon router"},[o("span",{class:"iconfont icon-size icon-router"})],-1),o("div",{class:"handler-item-text"},"\u8DEF\u7531\u5206\u652F",-1)])),o("div",{class:"handler-item",onClick:h[8]||(h[8]=k=>x(e(w).TRIGGER_NODE))},h[19]||(h[19]=[o("div",{class:"handler-item-icon trigger"},[o("span",{class:"iconfont icon-size icon-trigger"})],-1),o("div",{class:"handler-item-text"},"\u89E6\u53D1\u5668",-1)])),o("div",{class:"handler-item",onClick:h[9]||(h[9]=k=>x(e(w).CHILD_PROCESS_NODE))},h[20]||(h[20]=[o("div",{class:"handler-item-icon child-process"},[o("span",{class:"iconfont icon-size icon-child-process"})],-1),o("div",{class:"handler-item-text"},"\u5B50\u6D41\u7A0B",-1)]))])]),_:1},8,["visible"]))])])}}}),da={class:"config-header"},sa=["placeholder"],ra={key:1,class:"node-name"},ua={key:1},ca={key:2},pa={class:"field-setting-pane"},ma={class:"field-permit-title"},va={class:"other-titles"},fa={class:"field-setting-item-label"},ya={class:"item-radio-wrap"},ga={class:"item-radio-wrap"},ha={class:"item-radio-wrap"},_a=pe({name:"StartUserNodeConfig",__name:"StartUserNodeConfig",props:{flowNode:{type:Object,required:!0}},setup(Q,{expose:le}){const X=Q,U=ve("startUserIds"),v=ve("startDeptIds"),n=ve("userList"),d=ve("deptList"),{settingVisible:x,closeDrawer:I,openDrawer:h}=nl(),R=Ce(X),{nodeName:S,showInput:k,clickIcon:r,blurEvent:s}=il(w.COPY_TASK_NODE),f=$("user"),{formType:A,fieldsPermissionConfig:p,getNodeConfigFormFields:_}=jl(de.WRITE),H=Y=>{if(!Y||Y.length===0)return"";const C=[];return Y.forEach(Z=>{const F=n==null?void 0:n.value.find(T=>T.id===Z);F&&F.nickname&&C.push(F.nickname)}),C.join(",")},D=Y=>{if(!Y||Y.length===0)return"";const C=[];return Y.forEach(Z=>{const F=d==null?void 0:d.value.find(T=>T.id===Z);F&&F.name&&C.push(F.name)}),C.join(",")},z=async()=>(f.value="user",R.value.name=S.value,R.value.showText="\u5DF2\u8BBE\u7F6E",R.value.fieldsPermission=p.value,R.value.buttonsSetting=kt,x.value=!1,!0),K=Y=>{p.value.forEach(C=>{C.permission=Y==="READ"?de.READ:Y==="WRITE"?de.WRITE:de.NONE})};return le({openDrawer:h,showStartUserNodeConfig:Y=>{S.value=Y.name,_(Y.fieldsPermission)}}),(Y,C)=>{const Z=me,F=_l,T=it,b=Il,O=cl,m=ol,G=xl,W=je,B=Le,ee=Ye,J=ke("mountedFocus");return l(),c(ee,{"append-to-body":!0,modelValue:e(x),"onUpdate:modelValue":C[7]||(C[7]=q=>ie(x)?x.value=q:null),"show-close":!1,size:550,"before-close":z},{header:a(()=>[o("div",da,[e(k)?Ee((l(),i("input",{key:0,type:"text",class:"config-editable-input",onBlur:C[0]||(C[0]=q=>e(s)()),"onUpdate:modelValue":C[1]||(C[1]=q=>ie(S)?S.value=q:null),placeholder:e(S)},null,40,sa)),[[J],[Ne,e(S)]]):(l(),i("div",ra,[P(M(e(S))+" ",1),t(Z,{class:"ml-1",icon:"ep:edit-pen",size:16,onClick:C[2]||(C[2]=q=>e(r)())})])),C[8]||(C[8]=o("div",{class:"divide-line"},null,-1))])]),footer:a(()=>[t(W),o("div",null,[t(B,{type:"primary",onClick:z},{default:a(()=>C[15]||(C[15]=[P("\u786E \u5B9A")])),_:1}),t(B,{onClick:e(I)},{default:a(()=>C[16]||(C[16]=[P("\u53D6 \u6D88")])),_:1},8,["onClick"])])]),default:a(()=>[t(G,{type:"border-card",modelValue:e(f),"onUpdate:modelValue":C[6]||(C[6]=q=>ie(f)?f.value=q:null)},{default:a(()=>[t(b,{label:"\u6743\u9650",name:"user"},{default:a(()=>[e(U)&&e(U).length!==0||e(v)&&e(v).length!==0?e(U)&&e(U).length>0?(l(),i("div",ua,[e(U).length==1?(l(),c(F,{key:0},{default:a(()=>[P(M(H(e(U)))+" \u53EF\u53D1\u8D77\u6D41\u7A0B ",1)]),_:1})):(l(),c(F,{key:1},{default:a(()=>[t(T,{class:"box-item",effect:"dark",placement:"top",content:H(e(U))},{default:a(()=>[P(M(H(e(U).slice(0,2)))+" \u7B49 "+M(e(U).length)+" \u4EBA\u53EF\u53D1\u8D77\u6D41\u7A0B ",1)]),_:1},8,["content"])]),_:1}))])):e(v)&&e(v).length>0?(l(),i("div",ca,[e(v).length==1?(l(),c(F,{key:0},{default:a(()=>[P(M(D(e(v)))+" \u53EF\u53D1\u8D77\u6D41\u7A0B ",1)]),_:1})):(l(),c(F,{key:1},{default:a(()=>[t(T,{class:"box-item",effect:"dark",placement:"top",content:D(e(v))},{default:a(()=>[P(M(D(e(v).slice(0,2)))+" \u7B49 "+M(e(v).length)+" \u4E2A\u90E8\u95E8\u53EF\u53D1\u8D77\u6D41\u7A0B ",1)]),_:1},8,["content"])]),_:1}))])):N("",!0):(l(),c(F,{key:0},{default:a(()=>C[9]||(C[9]=[P(" \u5168\u90E8\u6210\u5458\u53EF\u4EE5\u53D1\u8D77\u6D41\u7A0B ")])),_:1}))]),_:1}),e(A)===10?(l(),c(b,{key:0,label:"\u8868\u5355\u5B57\u6BB5\u6743\u9650",name:"fields"},{default:a(()=>[o("div",pa,[C[14]||(C[14]=o("div",{class:"field-setting-desc"},"\u5B57\u6BB5\u6743\u9650",-1)),o("div",ma,[C[10]||(C[10]=o("div",{class:"setting-title-label first-title"}," \u5B57\u6BB5\u540D\u79F0 ",-1)),o("div",va,[o("span",{class:"setting-title-label cursor-pointer",onClick:C[3]||(C[3]=q=>K("READ"))}," \u53EA\u8BFB "),o("span",{class:"setting-title-label cursor-pointer",onClick:C[4]||(C[4]=q=>K("WRITE"))}," \u53EF\u7F16\u8F91 "),o("span",{class:"setting-title-label cursor-pointer",onClick:C[5]||(C[5]=q=>K("NONE"))}," \u9690\u85CF ")])]),(l(!0),i(L,null,j(e(p),(q,u)=>(l(),i("div",{class:"field-setting-item",key:u},[o("div",fa,M(q.title),1),t(m,{class:"field-setting-item-group",modelValue:q.permission,"onUpdate:modelValue":qe=>q.permission=qe},{default:a(()=>[o("div",ya,[t(O,{value:e(de).READ,size:"large",label:e(de).READ},{default:a(()=>C[11]||(C[11]=[o("span",null,null,-1)])),_:1},8,["value","label"])]),o("div",ga,[t(O,{value:e(de).WRITE,size:"large",label:e(de).WRITE},{default:a(()=>C[12]||(C[12]=[o("span",null,null,-1)])),_:1},8,["value","label"])]),o("div",ha,[t(O,{value:e(de).NONE,size:"large",label:e(de).NONE},{default:a(()=>C[13]||(C[13]=[o("span",null,null,-1)])),_:1},8,["value","label"])])]),_:2},1032,["modelValue","onUpdate:modelValue"])]))),128))])]),_:1})):N("",!0)]),_:1},8,["modelValue"])]),_:1},8,["modelValue"])}}}),ba={class:"node-wrapper"},Ea={class:"node-container"},Na={class:"node-title-container"},Ta=["placeholder"],ka=["title"],Sa={key:1,class:"node-text"},Ua=pe({name:"StartEventNode",__name:"StartUserNode",props:{flowNode:{type:Object,default:()=>null}},emits:["update:modelValue"],setup(Q,{emit:le}){const X=Q,U=ve("readonly"),v=ve("tasks",$([])),n=Ce(X),{showInput:d,blurEvent:x,clickTitle:I}=dl(n,w.START_USER_NODE),h=$(),R=()=>{U?v&&v.value&&(k.value=n.value.name,r.value=v.value.filter(s=>(s==null?void 0:s.taskDefinitionKey)===n.value.id),S.value=!0):(h.value.showStartUserNodeConfig(n.value),h.value.openDrawer())},S=$(!1),k=$(void 0),r=$([]);return(s,f)=>{var Y;const A=me,p=zl,_=Ll,H=Gl,D=bl,z=Ml,K=ke("mountedFocus");return l(),i(L,null,[o("div",ba,[o("div",Ea,[o("div",{class:Se(["node-box",[{"node-config-error":!e(n).showText},`${e(Ie)((Y=e(n))==null?void 0:Y.activityStatus)}`]])},[o("div",Na,[f[5]||(f[5]=o("div",{class:"node-title-icon start-user"},[o("span",{class:"iconfont icon-start-user"})],-1)),!e(U)&&e(d)?Ee((l(),i("input",{key:0,type:"text",class:"editable-title-input",onBlur:f[0]||(f[0]=C=>e(x)()),"onUpdate:modelValue":f[1]||(f[1]=C=>e(n).name=C),placeholder:e(n).name},null,40,Ta)),[[K],[Ne,e(n).name]]):(l(),i("div",{key:1,class:"node-title",onClick:f[2]||(f[2]=(...C)=>e(I)&&e(I)(...C))},M(e(n).name),1))]),o("div",{class:"node-content",onClick:R},[e(n).showText?(l(),i("div",{key:0,class:"node-text",title:e(n).showText},M(e(n).showText),9,ka)):(l(),i("div",Sa,M(e(Be).get(e(w).START_USER_NODE)),1)),e(U)?N("",!0):(l(),c(A,{key:2,icon:"ep:arrow-right-bold"}))])],2),e(n)?(l(),c(He,{key:0,"child-node":e(n).childNode,"onUpdate:childNode":f[3]||(f[3]=C=>e(n).childNode=C),"current-node":e(n)},null,8,["child-node","current-node"])):N("",!0)])]),!e(U)&&e(n)?(l(),c(_a,{key:0,ref_key:"nodeSetting",ref:h,"flow-node":e(n)},null,8,["flow-node"])):N("",!0),t(z,{title:e(k)||"\u5BA1\u6279\u8BB0\u5F55",modelValue:e(S),"onUpdate:modelValue":f[4]||(f[4]=C=>ie(S)?S.value=C:null),width:"1000px","append-to-body":""},{default:a(()=>[t(D,null,{default:a(()=>[t(H,{data:e(r),size:"small",border:"","header-cell-class-name":"table-header-gray"},{default:a(()=>[t(p,{label:"\u5E8F\u53F7","header-align":"center",align:"center",type:"index",width:"50"}),t(p,{label:"\u5BA1\u6279\u4EBA","min-width":"100",align:"center"},{default:a(C=>{var Z,F;return[P(M(((Z=C.row.assigneeUser)==null?void 0:Z.nickname)||((F=C.row.ownerUser)==null?void 0:F.nickname)),1)]}),_:1}),t(p,{label:"\u90E8\u95E8","min-width":"100",align:"center"},{default:a(C=>{var Z,F;return[P(M(((Z=C.row.assigneeUser)==null?void 0:Z.deptName)||((F=C.row.ownerUser)==null?void 0:F.deptName)),1)]}),_:1}),t(p,{formatter:e(ml),align:"center",label:"\u5F00\u59CB\u65F6\u95F4",prop:"createTime","min-width":"140"},null,8,["formatter"]),t(p,{formatter:e(ml),align:"center",label:"\u7ED3\u675F\u65F6\u95F4",prop:"endTime","min-width":"140"},null,8,["formatter"]),t(p,{align:"center",label:"\u5BA1\u6279\u72B6\u6001",prop:"status","min-width":"90"},{default:a(C=>[t(_,{type:e(Fl).BPM_TASK_STATUS,value:C.row.status},null,8,["type","value"])]),_:1}),t(p,{align:"center",label:"\u5BA1\u6279\u5EFA\u8BAE",prop:"reason","min-width":"120"}),t(p,{align:"center",label:"\u8017\u65F6",prop:"durationInMillis",width:"100"},{default:a(C=>[P(M(e(Kl)(C.row.durationInMillis)),1)]),_:1})]),_:1},8,["data"])]),_:1})]),_:1},8,["title","modelValue"])],64)}}}),wa={class:"end-node-wrapper"},Va=pe({name:"EndEventNode",__name:"EndEventNode",props:{flowNode:{type:Object,default:()=>null}},setup(Q){const le=Ce(Q),X=ve("readonly"),U=ve("processInstance",$({})),v=$(!1),n=$([]),d=()=>{X&&U&&U.value&&(n.value=[{assigneeUser:U.value.startUser,createTime:U.value.startTime,endTime:U.value.endTime,status:U.value.status,durationInMillis:U.value.durationInMillis}],v.value=!0)};return(x,I)=>{var s;const h=zl,R=Ll,S=Gl,k=bl,r=Ml;return l(),i(L,null,[o("div",wa,[o("div",{class:Se(["end-node-box cursor-pointer",`${e(Ie)((s=e(le))==null?void 0:s.activityStatus)}`]),onClick:d},I[1]||(I[1]=[o("span",{class:"node-fixed-name",title:"\u7ED3\u675F"},"\u7ED3\u675F",-1)]),2)]),t(r,{title:"\u5BA1\u6279\u4FE1\u606F",modelValue:e(v),"onUpdate:modelValue":I[0]||(I[0]=f=>ie(v)?v.value=f:null),width:"1000px","append-to-body":""},{default:a(()=>[t(k,null,{default:a(()=>[t(S,{data:e(n),size:"small",border:"","header-cell-class-name":"table-header-gray"},{default:a(()=>[t(h,{label:"\u5E8F\u53F7","header-align":"center",align:"center",type:"index",width:"50"}),t(h,{label:"\u53D1\u8D77\u4EBA",prop:"assigneeUser.nickname","min-width":"100",align:"center"}),t(h,{label:"\u90E8\u95E8","min-width":"100",align:"center"},{default:a(f=>{var A,p;return[P(M(((A=f.row.assigneeUser)==null?void 0:A.deptName)||((p=f.row.ownerUser)==null?void 0:p.deptName)),1)]}),_:1}),t(h,{formatter:e(ml),align:"center",label:"\u5F00\u59CB\u65F6\u95F4",prop:"createTime","min-width":"140"},null,8,["formatter"]),t(h,{formatter:e(ml),align:"center",label:"\u7ED3\u675F\u65F6\u95F4",prop:"endTime","min-width":"140"},null,8,["formatter"]),t(h,{align:"center",label:"\u5BA1\u6279\u72B6\u6001",prop:"status","min-width":"90"},{default:a(f=>[t(R,{type:e(Fl).BPM_PROCESS_INSTANCE_STATUS,value:f.row.status},null,8,["type","value"])]),_:1}),t(h,{align:"center",label:"\u8017\u65F6",prop:"durationInMillis",width:"100"},{default:a(f=>[P(M(e(Kl)(f.row.durationInMillis)),1)]),_:1})]),_:1},8,["data"])]),_:1})]),_:1},8,["modelValue"])],64)}}}),Ca={key:0},Ra=pe({__name:"UserTaskListener",props:{modelValue:{type:Object,required:!0},formFieldOptions:{type:Object,required:!0}},emits:["update:modelValue"],setup(Q,{expose:le,emit:X}){const U=Q,v=X,n=$(),d=Ue({get:()=>U.modelValue,set(I){v("update:modelValue",I)}}),x=$([{name:"\u521B\u5EFA\u4EFB\u52A1",type:"Create"},{name:"\u6307\u6D3E\u4EFB\u52A1\u6267\u884C\u4EBA\u5458",type:"Assign"},{name:"\u5B8C\u6210\u4EFB\u52A1",type:"Complete"}]);return le({validate:async()=>!!n&&await n.value.validate()}),(I,h)=>{const R=_l,S=je,k=El,r=Xe,s=Ht,f=pl,A=Ke;return l(),c(A,{ref_key:"listenerFormRef",ref:n,model:e(d),"label-position":"top"},{default:a(()=>[(l(!0),i(L,null,j(e(x),(p,_)=>(l(),i("div",{key:_},[t(S,{"content-position":"left"},{default:a(()=>[t(R,{tag:"b",size:"large"},{default:a(()=>[P(M(p.name),1)]),_:2},1024)]),_:2},1024),t(r,null,{default:a(()=>[t(k,{modelValue:e(d)[`task${p.type}ListenerEnable`],"onUpdate:modelValue":H=>e(d)[`task${p.type}ListenerEnable`]=H,"active-text":"\u5F00\u542F","inactive-text":"\u5173\u95ED"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),e(d)[`task${p.type}ListenerEnable`]?(l(),i("div",Ca,[t(r,null,{default:a(()=>[t(s,{title:"\u4EC5\u652F\u6301 POST \u8BF7\u6C42\uFF0C\u4EE5\u8BF7\u6C42\u4F53\u65B9\u5F0F\u63A5\u6536\u53C2\u6570",type:"warning","show-icon":"",closable:!1})]),_:1}),t(r,{label:"\u8BF7\u6C42\u5730\u5740",prop:`task${p.type}ListenerPath`,rules:{required:!0,message:"\u8BF7\u6C42\u5730\u5740\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}},{default:a(()=>[t(f,{modelValue:e(d)[`task${p.type}ListenerPath`],"onUpdate:modelValue":H=>e(d)[`task${p.type}ListenerPath`]=H},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"]),t(Qt,{header:e(d)[`task${p.type}Listener`].header,body:e(d)[`task${p.type}Listener`].body,bind:`task${p.type}Listener`},null,8,["header","body","bind"])])):N("",!0)]))),128))]),_:1},8,["model"])}}}),Da={class:"config-header"},xa=["placeholder"],Ia={key:1,class:"node-name"},Oa={key:0,class:"flex flex-items-center mb-3"},Aa={class:"flex-col"},Pa={key:9},Fa={class:"flex-col"},La={key:10},Ha={class:"flex-col"},qa={key:12},Ma={class:"flex-col"},Ga={key:13},za={key:14},Ba={class:"button-setting-pane"},$a={class:"button-setting-item-label"},ja={class:"button-setting-item-label"},Ka=["onBlur","onUpdate:modelValue","placeholder"],Ya={class:"button-setting-item-label"},Xa={class:"field-setting-pane"},Wa={class:"field-permit-title"},Qa={class:"other-titles"},Ja={class:"field-setting-item-label"},Za={class:"item-radio-wrap"},eo={class:"item-radio-wrap"},lo={class:"item-radio-wrap"},to=yl(pe({name:"UserTaskNodeConfig",__name:"UserTaskNodeConfig",props:{flowNode:{type:Object,required:!0}},emits:["find:returnTaskNodes"],setup(Q,{expose:le,emit:X}){const U=Q,v=X,n=Ue(()=>{let g="\u90E8\u95E8\u8D1F\u8D23\u4EBA\u6765\u6E90";return u.value.candidateStrategy==oe.MULTI_LEVEL_DEPT_LEADER?g+="(\u6307\u5B9A\u90E8\u95E8\u5411\u4E0A)":u.value.candidateStrategy==oe.FORM_DEPT_LEADER?g+="(\u8868\u5355\u5185\u90E8\u95E8\u5411\u4E0A)":g+="(\u53D1\u8D77\u4EBA\u90E8\u95E8\u5411\u4E0A)",g}),d=Ce(U),{settingVisible:x,closeDrawer:I,openDrawer:h}=nl(),{nodeName:R,showInput:S,clickIcon:k,blurEvent:r}=il(w.USER_TASK_NODE),s=$("user"),{formType:f,fieldsPermissionConfig:A,formFieldOptions:p,getNodeConfigFormFields:_}=jl(de.READ),H=Ue(()=>(p.unshift({field:St.START_USER_ID,title:"\u53D1\u8D77\u4EBA",type:"UserSelect",required:!0}),p.filter(g=>g.type==="UserSelect"))),D=Ue(()=>p.filter(g=>g.type==="DeptSelect")),{buttonsSetting:z,btnDisplayNameEdit:K,changeBtnDisplayName:Y,btnDisplayNameBlurEvent:C}=function(){const g=$(),E=$([]);return{buttonsSetting:g,btnDisplayNameEdit:E,changeBtnDisplayName:we=>{E.value[we]=!0},btnDisplayNameBlurEvent:we=>{E.value[we]=!1;const fe=g.value[we];fe.displayName=fe.displayName||et.get(fe.id)}}}(),Z=$(gl.USER),F=$(),T=tl({candidateStrategy:[{required:!0,message:"\u5BA1\u6279\u4EBA\u8BBE\u7F6E\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],userIds:[{required:!0,message:"\u7528\u6237\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],roleIds:[{required:!0,message:"\u89D2\u8272\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],deptIds:[{required:!0,message:"\u90E8\u95E8\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],userGroups:[{required:!0,message:"\u7528\u6237\u7EC4\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],formUser:[{required:!0,message:"\u8868\u5355\u5185\u7528\u6237\u5B57\u6BB5\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],formDept:[{required:!0,message:"\u8868\u5355\u5185\u90E8\u95E8\u5B57\u6BB5\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],postIds:[{required:!0,message:"\u5C97\u4F4D\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],expression:[{required:!0,message:"\u6D41\u7A0B\u8868\u8FBE\u5F0F\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],approveMethod:[{required:!0,message:"\u591A\u4EBA\u5BA1\u6279\u65B9\u5F0F\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],approveRatio:[{required:!0,message:"\u901A\u8FC7\u6BD4\u4F8B\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],returnNodeId:[{required:!0,message:"\u9A73\u56DE\u8282\u70B9\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],timeoutHandlerEnable:[{required:!0}],timeoutHandlerType:[{required:!0}],timeDuration:[{required:!0,message:"\u8D85\u65F6\u65F6\u95F4\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],maxRemindCount:[{required:!0,message:"\u63D0\u9192\u6B21\u6570\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],assignEmptyHandlerType:[{required:!0}],assignEmptyHandlerUserIds:[{required:!0,message:"\u7528\u6237\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],assignStartUserHandlerType:[{required:!0}]}),{configForm:b,roleOptions:O,postOptions:m,userOptions:G,userGroupOptions:W,deptTreeOptions:B,handleCandidateParam:ee,parseCandidateParam:J,getShowText:q}=ut(d.value.type),u=b,qe=()=>{u.value.userIds=[],u.value.deptIds=[],u.value.roleIds=[],u.value.postIds=[],u.value.userGroups=[],u.value.deptLevel=1,u.value.formUser="",u.value.formDept="",u.value.approveMethod=al.SEQUENTIAL_APPROVE},Ge=()=>{u.value.rejectHandlerType=ql.FINISH_PROCESS,u.value.approveMethod===al.APPROVE_BY_RATIO&&(u.value.approveRatio=100),F.value.clearValidate("approveRatio")},ge=$([]),{timeoutHandlerChange:Oe,cTimeoutType:Ae,timeoutHandlerTypeChanged:te,timeUnit:ue,timeUnitChange:ae,isoTimeDuration:he,cTimeoutMaxRemindCount:V}=function(){const g=$(Re.HOUR),E=Ue(()=>{if(u.value.timeoutHandlerEnable)return u.value.timeoutHandlerType}),we=Ue(()=>{if(!u.value.timeoutHandlerEnable)return;let be="PT";return g.value===Re.MINUTE&&(be+=u.value.timeDuration+"M"),g.value===Re.HOUR&&(be+=u.value.timeDuration+"H"),g.value===Re.DAY&&(be+=u.value.timeDuration+"D"),be}),fe=Ue(()=>{if(u.value.timeoutHandlerEnable&&u.value.timeoutHandlerType===Ql.REMINDER)return u.value.maxRemindCount});return{timeoutHandlerChange:()=>{u.value.timeoutHandlerEnable&&(g.value=2,u.value.timeDuration=6,u.value.timeoutHandlerType=1,u.value.maxRemindCount=1)},cTimeoutType:E,timeoutHandlerTypeChanged:()=>{u.value.timeoutHandlerType===Ql.REMINDER&&(u.value.maxRemindCount=1)},timeUnit:g,timeUnitChange:()=>{g.value===Re.MINUTE&&(u.value.timeDuration=60),g.value===Re.HOUR&&(u.value.timeDuration=6),g.value===Re.DAY&&(u.value.timeDuration=1)},isoTimeDuration:we,cTimeoutMaxRemindCount:fe}}(),re=$(),ne=Ue(()=>d.value.type===w.TRANSACTOR_NODE?"\u529E\u7406":"\u5BA1\u6279"),xe=async()=>{var E,we,fe,be,Je,Ze;if(d.value.name=R.value,d.value.approveType=Z.value,Z.value!==gl.USER)return d.value.showText=Xt(Z.value),x.value=!1,!0;if(!F||!re||!(await F.value.validate()&&await re.value.validate()))return!1;const g=q();return!!g&&(d.value.candidateStrategy=u.value.candidateStrategy,d.value.candidateParam=ee(),d.value.approveMethod=u.value.approveMethod,u.value.approveMethod===al.APPROVE_BY_RATIO&&(d.value.approveRatio=u.value.approveRatio),d.value.rejectHandler={type:u.value.rejectHandlerType,returnNodeId:u.value.returnNodeId},d.value.timeoutHandler={enable:u.value.timeoutHandlerEnable,type:Ae.value,timeDuration:he.value,maxRemindCount:V.value},d.value.assignEmptyHandler={type:u.value.assignEmptyHandlerType,userIds:u.value.assignEmptyHandlerType===Hl.ASSIGN_USER?u.value.assignEmptyHandlerUserIds:void 0},d.value.assignStartUserHandlerType=u.value.assignStartUserHandlerType,d.value.fieldsPermission=A.value,d.value.buttonsSetting=z.value,d.value.taskCreateListener={enable:u.value.taskCreateListenerEnable??!1,path:u.value.taskCreateListenerPath,header:(E=u.value.taskCreateListener)==null?void 0:E.header,body:(we=u.value.taskCreateListener)==null?void 0:we.body},d.value.taskAssignListener={enable:u.value.taskAssignListenerEnable??!1,path:u.value.taskAssignListenerPath,header:(fe=u.value.taskAssignListener)==null?void 0:fe.header,body:(be=u.value.taskAssignListener)==null?void 0:be.body},d.value.taskCompleteListener={enable:u.value.taskCompleteListenerEnable??!1,path:u.value.taskCompleteListenerPath,header:(Je=u.value.taskCompleteListener)==null?void 0:Je.header,body:(Ze=u.value.taskCompleteListener)==null?void 0:Ze.body},d.value.signEnable=u.value.signEnable,d.value.reasonRequire=u.value.reasonRequire,d.value.showText=g,x.value=!1,!0)};le({openDrawer:h,showUserTaskNodeConfig:g=>{var we,fe,be,Je,Ze,ce,Pe,Fe,Nl,Tl,sl,$e,el,kl,Sl,ll,rl,Ul,wl,fl,y;if(R.value=g.name,Z.value=g.approveType?g.approveType:gl.USER,Z.value!==gl.USER)return;u.value.candidateStrategy=g.candidateStrategy,J(g.candidateStrategy,g==null?void 0:g.candidateParam),u.value.approveMethod=g.approveMethod,g.approveMethod==al.APPROVE_BY_RATIO&&(u.value.approveRatio=g.approveRatio),u.value.rejectHandlerType=(we=g.rejectHandler)==null?void 0:we.type,u.value.returnNodeId=(fe=g.rejectHandler)==null?void 0:fe.returnNodeId;const E=[];if(v("find:returnTaskNodes",E),ge.value=E,u.value.timeoutHandlerEnable=(be=g.timeoutHandler)==null?void 0:be.enable,((Je=g.timeoutHandler)==null?void 0:Je.enable)&&((Ze=g.timeoutHandler)==null?void 0:Ze.timeDuration)){const se=g.timeoutHandler.timeDuration;let ze=se.slice(2,se.length-1),ht=se.slice(se.length-1);u.value.timeDuration=parseInt(ze),ue.value=Yl(ht)}u.value.timeoutHandlerType=(ce=g.timeoutHandler)==null?void 0:ce.type,u.value.maxRemindCount=(Pe=g.timeoutHandler)==null?void 0:Pe.maxRemindCount,u.value.assignEmptyHandlerType=(Fe=g.assignEmptyHandler)==null?void 0:Fe.type,u.value.assignEmptyHandlerUserIds=(Nl=g.assignEmptyHandler)==null?void 0:Nl.userIds,u.value.assignStartUserHandlerType=g.assignStartUserHandlerType,z.value=De(g.buttonsSetting)||(g.type===w.TRANSACTOR_NODE?xt:It),_(g.fieldsPermission),u.value.taskCreateListenerEnable=(Tl=g.taskCreateListener)==null?void 0:Tl.enable,u.value.taskCreateListenerPath=(sl=g.taskCreateListener)==null?void 0:sl.path,u.value.taskCreateListener={header:(($e=g.taskCreateListener)==null?void 0:$e.header)??[],body:((el=g.taskCreateListener)==null?void 0:el.body)??[]},u.value.taskAssignListenerEnable=(kl=g.taskAssignListener)==null?void 0:kl.enable,u.value.taskAssignListenerPath=(Sl=g.taskAssignListener)==null?void 0:Sl.path,u.value.taskAssignListener={header:((ll=g.taskAssignListener)==null?void 0:ll.header)??[],body:((rl=g.taskAssignListener)==null?void 0:rl.body)??[]},u.value.taskCompleteListenerEnable=(Ul=g.taskCompleteListener)==null?void 0:Ul.enable,u.value.taskCompleteListenerPath=(wl=g.taskCompleteListener)==null?void 0:wl.path,u.value.taskCompleteListener={header:((fl=g.taskCompleteListener)==null?void 0:fl.header)??[],body:((y=g.taskCompleteListener)==null?void 0:y.body)??[]},u.value.signEnable=(g==null?void 0:g.signEnable)??!1,u.value.reasonRequire=(g==null?void 0:g.reasonRequire)??!1}});const vl=g=>{A.value.forEach(E=>{E.permission=g==="READ"?de.READ:g==="WRITE"?de.WRITE:de.NONE})};return(g,E)=>{const we=me,fe=cl,be=ol,Je=qt,Ze=bl,ce=Xe,Pe=Qe,Fe=We,Nl=dt,Tl=pl,sl=Bl,$e=je,el=El,kl=$l,Sl=Ke,ll=Il,rl=Le,Ul=xl,wl=Ye,fl=ke("mountedFocus");return l(),c(wl,{"append-to-body":!0,modelValue:e(x),"onUpdate:modelValue":E[33]||(E[33]=y=>ie(x)?x.value=y:null),"show-close":!1,size:580,"before-close":xe,class:"justify-start"},{header:a(()=>[o("div",Da,[e(S)?Ee((l(),i("input",{key:0,type:"text",class:"config-editable-input",onBlur:E[0]||(E[0]=y=>e(r)()),"onUpdate:modelValue":E[1]||(E[1]=y=>ie(R)?R.value=y:null),placeholder:e(R)},null,40,xa)),[[fl],[Ne,e(R)]]):(l(),i("div",Ia,[P(M(e(R))+" ",1),t(we,{class:"ml-1",icon:"ep:edit-pen",size:16,onClick:E[2]||(E[2]=y=>e(k)())})])),E[34]||(E[34]=o("div",{class:"divide-line"},null,-1))])]),footer:a(()=>[t($e),o("div",null,[t(rl,{type:"primary",onClick:xe},{default:a(()=>E[50]||(E[50]=[P("\u786E \u5B9A")])),_:1}),t(rl,{onClick:e(I)},{default:a(()=>E[51]||(E[51]=[P("\u53D6 \u6D88")])),_:1},8,["onClick"])])]),default:a(()=>[e(d).type===e(w).USER_TASK_NODE?(l(),i("div",Oa,[E[35]||(E[35]=o("span",{class:"font-size-16px mr-3"},"\u5BA1\u6279\u7C7B\u578B :",-1)),t(be,{modelValue:e(Z),"onUpdate:modelValue":E[3]||(E[3]=y=>ie(Z)?Z.value=y:null)},{default:a(()=>[(l(!0),i(L,null,j(e(Ut),(y,se)=>(l(),c(fe,{key:se,value:y.value,label:y.value},{default:a(()=>[P(M(y.label),1)]),_:2},1032,["value","label"]))),128))]),_:1},8,["modelValue"])])):N("",!0),e(Z)===e(gl).USER?(l(),c(Ul,{key:1,type:"border-card",modelValue:e(s),"onUpdate:modelValue":E[32]||(E[32]=y=>ie(s)?s.value=y:null)},{default:a(()=>[t(ll,{label:`${e(ne)}\u4EBA`,name:"user"},{default:a(()=>[o("div",null,[t(Sl,{ref_key:"formRef",ref:F,model:e(u),"label-position":"top",rules:e(T)},{default:a(()=>[t(ce,{label:`${e(ne)}\u4EBA\u8BBE\u7F6E`,prop:"candidateStrategy"},{default:a(()=>[t(be,{modelValue:e(u).candidateStrategy,"onUpdate:modelValue":E[4]||(E[4]=y=>e(u).candidateStrategy=y),onChange:qe},{default:a(()=>[t(Ze,null,{default:a(()=>[(l(!0),i(L,null,j(e(Jl),(y,se)=>(l(),c(Je,{key:se,span:8},{default:a(()=>[t(fe,{value:y.value,label:y.value},{default:a(()=>[P(M(y.label),1)]),_:2},1032,["value","label"])]),_:2},1024))),128))]),_:1})]),_:1},8,["modelValue"])]),_:1},8,["label"]),e(u).candidateStrategy==e(oe).ROLE?(l(),c(ce,{key:0,label:"\u6307\u5B9A\u89D2\u8272",prop:"roleIds"},{default:a(()=>[t(Fe,{filterable:"",modelValue:e(u).roleIds,"onUpdate:modelValue":E[5]||(E[5]=y=>e(u).roleIds=y),clearable:"",multiple:"",style:{width:"100%"}},{default:a(()=>[(l(!0),i(L,null,j(e(O),y=>(l(),c(Pe,{key:y.id,label:y.name,value:y.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):N("",!0),e(u).candidateStrategy==e(oe).DEPT_MEMBER||e(u).candidateStrategy==e(oe).DEPT_LEADER||e(u).candidateStrategy==e(oe).MULTI_LEVEL_DEPT_LEADER?(l(),c(ce,{key:1,label:"\u6307\u5B9A\u90E8\u95E8",prop:"deptIds",span:"24"},{default:a(()=>[t(Nl,{ref:"treeRef",modelValue:e(u).deptIds,"onUpdate:modelValue":E[6]||(E[6]=y=>e(u).deptIds=y),data:e(B),props:e(pt),"empty-text":"\u52A0\u8F7D\u4E2D\uFF0C\u8BF7\u7A0D\u540E",multiple:"","node-key":"id","check-strictly":!0,style:{width:"100%"},"show-checkbox":""},null,8,["modelValue","data","props"])]),_:1})):N("",!0),e(u).candidateStrategy==e(oe).POST?(l(),c(ce,{key:2,label:"\u6307\u5B9A\u5C97\u4F4D",prop:"postIds",span:"24"},{default:a(()=>[t(Fe,{filterable:"",modelValue:e(u).postIds,"onUpdate:modelValue":E[7]||(E[7]=y=>e(u).postIds=y),clearable:"",multiple:"",style:{width:"100%"}},{default:a(()=>[(l(!0),i(L,null,j(e(m),y=>(l(),c(Pe,{key:y.id,label:y.name,value:y.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):N("",!0),e(u).candidateStrategy==e(oe).USER?(l(),c(ce,{key:3,label:"\u6307\u5B9A\u7528\u6237",prop:"userIds",span:"24"},{default:a(()=>[t(Fe,{filterable:"",modelValue:e(u).userIds,"onUpdate:modelValue":E[8]||(E[8]=y=>e(u).userIds=y),clearable:"",multiple:"",style:{width:"100%"}},{default:a(()=>[(l(!0),i(L,null,j(e(G),y=>(l(),c(Pe,{key:y.id,label:y.nickname,value:y.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):N("",!0),e(u).candidateStrategy===e(oe).USER_GROUP?(l(),c(ce,{key:4,label:"\u6307\u5B9A\u7528\u6237\u7EC4",prop:"userGroups"},{default:a(()=>[t(Fe,{filterable:"",modelValue:e(u).userGroups,"onUpdate:modelValue":E[9]||(E[9]=y=>e(u).userGroups=y),clearable:"",multiple:"",style:{width:"100%"}},{default:a(()=>[(l(!0),i(L,null,j(e(W),y=>(l(),c(Pe,{key:y.id,label:y.name,value:y.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):N("",!0),e(u).candidateStrategy===e(oe).FORM_USER?(l(),c(ce,{key:5,label:"\u8868\u5355\u5185\u7528\u6237\u5B57\u6BB5",prop:"formUser"},{default:a(()=>[t(Fe,{filterable:"",modelValue:e(u).formUser,"onUpdate:modelValue":E[10]||(E[10]=y=>e(u).formUser=y),clearable:"",style:{width:"100%"}},{default:a(()=>[(l(!0),i(L,null,j(e(H),(y,se)=>(l(),c(Pe,{key:se,label:y.title,value:y.field,disabled:!y.required},null,8,["label","value","disabled"]))),128))]),_:1},8,["modelValue"])]),_:1})):N("",!0),e(u).candidateStrategy===e(oe).FORM_DEPT_LEADER?(l(),c(ce,{key:6,label:"\u8868\u5355\u5185\u90E8\u95E8\u5B57\u6BB5",prop:"formDept"},{default:a(()=>[t(Fe,{filterable:"",modelValue:e(u).formDept,"onUpdate:modelValue":E[11]||(E[11]=y=>e(u).formDept=y),clearable:"",style:{width:"100%"}},{default:a(()=>[(l(!0),i(L,null,j(e(D),(y,se)=>(l(),c(Pe,{key:se,label:y.title,value:y.field,disabled:!y.required},null,8,["label","value","disabled"]))),128))]),_:1},8,["modelValue"])]),_:1})):N("",!0),e(u).candidateStrategy==e(oe).MULTI_LEVEL_DEPT_LEADER||e(u).candidateStrategy==e(oe).START_USER_DEPT_LEADER||e(u).candidateStrategy==e(oe).START_USER_MULTI_LEVEL_DEPT_LEADER||e(u).candidateStrategy==e(oe).FORM_DEPT_LEADER?(l(),c(ce,{key:7,label:e(n),prop:"deptLevel",span:"24"},{default:a(()=>[t(Fe,{filterable:"",modelValue:e(u).deptLevel,"onUpdate:modelValue":E[12]||(E[12]=y=>e(u).deptLevel=y),clearable:""},{default:a(()=>[(l(!0),i(L,null,j(e(Zl),(y,se)=>(l(),c(Pe,{key:se,label:y.label,value:y.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["label"])):N("",!0),e(u).candidateStrategy===e(oe).EXPRESSION?(l(),c(ce,{key:8,label:"\u6D41\u7A0B\u8868\u8FBE\u5F0F",prop:"expression"},{default:a(()=>[t(Tl,{type:"textarea",modelValue:e(u).expression,"onUpdate:modelValue":E[13]||(E[13]=y=>e(u).expression=y),clearable:"",style:{width:"100%"}},null,8,["modelValue"])]),_:1})):N("",!0),t(ce,{label:`\u591A\u4EBA${e(ne)}\u65B9\u5F0F`,prop:"approveMethod"},{default:a(()=>[t(be,{modelValue:e(u).approveMethod,"onUpdate:modelValue":E[15]||(E[15]=y=>e(u).approveMethod=y),onChange:Ge},{default:a(()=>[o("div",Aa,[(l(!0),i(L,null,j(e(wt),(y,se)=>(l(),i("div",{key:se,class:"flex items-center"},[t(fe,{value:y.value,label:y.value},{default:a(()=>[P(M(y.label),1)]),_:2},1032,["value","label"]),t(ce,{prop:"approveRatio"},{default:a(()=>[y.value===e(al).APPROVE_BY_RATIO&&e(u).approveMethod===e(al).APPROVE_BY_RATIO?(l(),c(sl,{key:0,modelValue:e(u).approveRatio,"onUpdate:modelValue":E[14]||(E[14]=ze=>e(u).approveRatio=ze),min:10,max:100,step:10,size:"small"},null,8,["modelValue"])):N("",!0)]),_:2},1024)]))),128))])]),_:1},8,["modelValue"])]),_:1},8,["label"]),e(d).type===e(w).USER_TASK_NODE?(l(),i("div",Pa,[t($e,{"content-position":"left"},{default:a(()=>E[36]||(E[36]=[P("\u5BA1\u6279\u4EBA\u62D2\u7EDD\u65F6")])),_:1}),t(ce,{prop:"rejectHandlerType"},{default:a(()=>[t(be,{modelValue:e(u).rejectHandlerType,"onUpdate:modelValue":E[16]||(E[16]=y=>e(u).rejectHandlerType=y)},{default:a(()=>[o("div",Fa,[(l(!0),i(L,null,j(e(Vt),(y,se)=>(l(),i("div",{key:se},[(l(),c(fe,{key:y.value,value:y.value,label:y.label},null,8,["value","label"]))]))),128))])]),_:1},8,["modelValue"])]),_:1}),e(u).rejectHandlerType==e(ql).RETURN_USER_TASK?(l(),c(ce,{key:0,label:"\u9A73\u56DE\u8282\u70B9",prop:"returnNodeId"},{default:a(()=>[t(Fe,{filterable:"",modelValue:e(u).returnNodeId,"onUpdate:modelValue":E[17]||(E[17]=y=>e(u).returnNodeId=y),clearable:"",style:{width:"100%"}},{default:a(()=>[(l(!0),i(L,null,j(e(ge),y=>(l(),c(Pe,{key:y.id,label:y.name,value:y.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):N("",!0)])):N("",!0),e(d).type===e(w).USER_TASK_NODE?(l(),i("div",La,[t($e,{"content-position":"left"},{default:a(()=>E[37]||(E[37]=[P("\u5BA1\u6279\u4EBA\u8D85\u65F6\u672A\u5904\u7406\u65F6")])),_:1}),t(ce,{label:"\u542F\u7528\u5F00\u5173",prop:"timeoutHandlerEnable"},{default:a(()=>[t(el,{modelValue:e(u).timeoutHandlerEnable,"onUpdate:modelValue":E[18]||(E[18]=y=>e(u).timeoutHandlerEnable=y),"active-text":"\u5F00\u542F","inactive-text":"\u5173\u95ED",onChange:e(Oe)},null,8,["modelValue","onChange"])]),_:1}),e(u).timeoutHandlerEnable?(l(),c(ce,{key:0,label:"\u6267\u884C\u52A8\u4F5C",prop:"timeoutHandlerType"},{default:a(()=>[t(be,{modelValue:e(u).timeoutHandlerType,"onUpdate:modelValue":E[19]||(E[19]=y=>e(u).timeoutHandlerType=y),onChange:e(te)},{default:a(()=>[(l(!0),i(L,null,j(e(Ct),y=>(l(),c(kl,{key:y.value,value:y.value,label:y.label},null,8,["value","label"]))),128))]),_:1},8,["modelValue","onChange"])]),_:1})):N("",!0),e(u).timeoutHandlerEnable?(l(),c(ce,{key:1,label:"\u8D85\u65F6\u65F6\u95F4\u8BBE\u7F6E"},{default:a(()=>[E[38]||(E[38]=o("span",{class:"mr-2"},"\u5F53\u8D85\u8FC7",-1)),t(ce,{prop:"timeDuration"},{default:a(()=>[t(sl,{class:"mr-2",style:{width:"100px"},modelValue:e(u).timeDuration,"onUpdate:modelValue":E[20]||(E[20]=y=>e(u).timeDuration=y),min:1,"controls-position":"right"},null,8,["modelValue"])]),_:1}),t(Fe,{filterable:"",modelValue:e(ue),"onUpdate:modelValue":E[21]||(E[21]=y=>ie(ue)?ue.value=y:null),class:"mr-2",style:{width:"100px"},onChange:e(ae)},{default:a(()=>[(l(!0),i(L,null,j(e(Dl),y=>(l(),c(Pe,{key:y.value,label:y.label,value:y.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","onChange"]),E[39]||(E[39]=P(" \u672A\u5904\u7406 "))]),_:1})):N("",!0),e(u).timeoutHandlerEnable&&e(u).timeoutHandlerType===1?(l(),c(ce,{key:2,label:"\u6700\u5927\u63D0\u9192\u6B21\u6570",prop:"maxRemindCount"},{default:a(()=>[t(sl,{modelValue:e(u).maxRemindCount,"onUpdate:modelValue":E[22]||(E[22]=y=>e(u).maxRemindCount=y),min:1,max:10},null,8,["modelValue"])]),_:1})):N("",!0)])):N("",!0),t($e,{"content-position":"left"},{default:a(()=>[P(M(e(ne))+"\u4EBA\u4E3A\u7A7A\u65F6",1)]),_:1}),t(ce,{prop:"assignEmptyHandlerType"},{default:a(()=>[t(be,{modelValue:e(u).assignEmptyHandlerType,"onUpdate:modelValue":E[23]||(E[23]=y=>e(u).assignEmptyHandlerType=y)},{default:a(()=>[o("div",Ha,[(l(!0),i(L,null,j(e(Rt),(y,se)=>(l(),i("div",{key:se},[(l(),c(fe,{key:y.value,value:y.value,label:y.label},null,8,["value","label"]))]))),128))])]),_:1},8,["modelValue"])]),_:1}),e(u).assignEmptyHandlerType==e(Hl).ASSIGN_USER?(l(),c(ce,{key:11,label:"\u6307\u5B9A\u7528\u6237",prop:"assignEmptyHandlerUserIds",span:"24"},{default:a(()=>[t(Fe,{filterable:"",modelValue:e(u).assignEmptyHandlerUserIds,"onUpdate:modelValue":E[24]||(E[24]=y=>e(u).assignEmptyHandlerUserIds=y),clearable:"",multiple:"",style:{width:"100%"}},{default:a(()=>[(l(!0),i(L,null,j(e(G),y=>(l(),c(Pe,{key:y.id,label:y.nickname,value:y.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):N("",!0),e(d).type===e(w).USER_TASK_NODE?(l(),i("div",qa,[t($e,{"content-position":"left"},{default:a(()=>E[40]||(E[40]=[P("\u5BA1\u6279\u4EBA\u4E0E\u63D0\u4EA4\u4EBA\u4E3A\u540C\u4E00\u4EBA\u65F6")])),_:1}),t(ce,{prop:"assignStartUserHandlerType"},{default:a(()=>[t(be,{modelValue:e(u).assignStartUserHandlerType,"onUpdate:modelValue":E[25]||(E[25]=y=>e(u).assignStartUserHandlerType=y)},{default:a(()=>[o("div",Ma,[(l(!0),i(L,null,j(e(Dt),(y,se)=>(l(),i("div",{key:se},[(l(),c(fe,{key:y.value,value:y.value,label:y.label},null,8,["value","label"]))]))),128))])]),_:1},8,["modelValue"])]),_:1})])):N("",!0),e(d).type===e(w).USER_TASK_NODE?(l(),i("div",Ga,[t($e,{"content-position":"left"},{default:a(()=>E[41]||(E[41]=[P("\u662F\u5426\u9700\u8981\u7B7E\u540D")])),_:1}),t(ce,{prop:"signEnable"},{default:a(()=>[t(el,{modelValue:e(u).signEnable,"onUpdate:modelValue":E[26]||(E[26]=y=>e(u).signEnable=y),"active-text":"\u662F","inactive-text":"\u5426"},null,8,["modelValue"])]),_:1})])):N("",!0),e(d).type===e(w).USER_TASK_NODE?(l(),i("div",za,[t($e,{"content-position":"left"},{default:a(()=>E[42]||(E[42]=[P("\u5BA1\u6279\u610F\u89C1")])),_:1}),t(ce,{prop:"reasonRequire"},{default:a(()=>[t(el,{modelValue:e(u).reasonRequire,"onUpdate:modelValue":E[27]||(E[27]=y=>e(u).reasonRequire=y),"active-text":"\u5FC5\u586B","inactive-text":"\u975E\u5FC5\u586B"},null,8,["modelValue"])]),_:1})])):N("",!0)]),_:1},8,["model","rules"])])]),_:1},8,["label"]),e(d).type===e(w).USER_TASK_NODE?(l(),c(ll,{key:0,label:"\u64CD\u4F5C\u6309\u94AE\u8BBE\u7F6E",name:"buttons"},{default:a(()=>[o("div",Ba,[E[43]||(E[43]=o("div",{class:"button-setting-desc"},"\u64CD\u4F5C\u6309\u94AE",-1)),E[44]||(E[44]=o("div",{class:"button-setting-title"},[o("div",{class:"button-title-label"},"\u64CD\u4F5C\u6309\u94AE"),o("div",{class:"pl-4 button-title-label"},"\u663E\u793A\u540D\u79F0"),o("div",{class:"button-title-label"},"\u542F\u7528")],-1)),(l(!0),i(L,null,j(e(z),(y,se)=>(l(),i("div",{class:"button-setting-item",key:se},[o("div",$a,M(e(et).get(y.id)),1),o("div",ja,[e(K)[se]?Ee((l(),i("input",{key:0,type:"text",class:"editable-title-input",onBlur:ze=>e(C)(se),"onUpdate:modelValue":ze=>y.displayName=ze,placeholder:y.displayName},null,40,Ka)),[[fl],[Ne,y.displayName]]):(l(),c(rl,{key:1,text:"",onClick:ze=>e(Y)(se)},{default:a(()=>[P(M(y.displayName)+" \xA0",1),t(we,{icon:"ep:edit"})]),_:2},1032,["onClick"]))]),o("div",Ya,[t(el,{modelValue:y.enable,"onUpdate:modelValue":ze=>y.enable=ze},null,8,["modelValue","onUpdate:modelValue"])])]))),128))])]),_:1})):N("",!0),e(f)===10?(l(),c(ll,{key:1,label:"\u8868\u5355\u5B57\u6BB5\u6743\u9650",name:"fields"},{default:a(()=>[o("div",Xa,[E[49]||(E[49]=o("div",{class:"field-setting-desc"},"\u5B57\u6BB5\u6743\u9650",-1)),o("div",Wa,[E[45]||(E[45]=o("div",{class:"setting-title-label first-title"}," \u5B57\u6BB5\u540D\u79F0 ",-1)),o("div",Qa,[o("span",{class:"setting-title-label cursor-pointer",onClick:E[28]||(E[28]=y=>vl("READ"))}," \u53EA\u8BFB "),o("span",{class:"setting-title-label cursor-pointer",onClick:E[29]||(E[29]=y=>vl("WRITE"))}," \u53EF\u7F16\u8F91 "),o("span",{class:"setting-title-label cursor-pointer",onClick:E[30]||(E[30]=y=>vl("NONE"))}," \u9690\u85CF ")])]),(l(!0),i(L,null,j(e(A),(y,se)=>(l(),i("div",{class:"field-setting-item",key:se},[o("div",Ja,M(y.title),1),t(be,{class:"field-setting-item-group",modelValue:y.permission,"onUpdate:modelValue":ze=>y.permission=ze},{default:a(()=>[o("div",Za,[t(fe,{value:e(de).READ,size:"large",label:e(de).READ},{default:a(()=>E[46]||(E[46]=[o("span",null,null,-1)])),_:1},8,["value","label"])]),o("div",eo,[t(fe,{value:e(de).WRITE,size:"large",label:e(de).WRITE},{default:a(()=>E[47]||(E[47]=[o("span",null,null,-1)])),_:1},8,["value","label"])]),o("div",lo,[t(fe,{value:e(de).NONE,size:"large",label:e(de).NONE},{default:a(()=>E[48]||(E[48]=[o("span",null,null,-1)])),_:1},8,["value","label"])])]),_:2},1032,["modelValue","onUpdate:modelValue"])]))),128))])]),_:1})):N("",!0),t(ll,{label:"\u76D1\u542C\u5668",name:"listener"},{default:a(()=>[t(Ra,{ref_key:"userTaskListenerRef",ref:re,modelValue:e(u),"onUpdate:modelValue":E[31]||(E[31]=y=>ie(u)?u.value=y:null),"form-field-options":e(p)},null,8,["modelValue","form-field-options"])]),_:1})]),_:1},8,["modelValue"])):N("",!0)]),_:1},8,["modelValue"])}}}),[["__scopeId","data-v-b550df49"]]),ao={class:"node-wrapper"},oo={class:"node-container"},no={class:"node-title-container"},io=["placeholder"],so=["title"],ro={key:1,class:"node-text"},uo={key:0,class:"node-toolbar"},co={class:"toolbar-icon"},po=pe({name:"UserTaskNode",__name:"UserTaskNode",props:{flowNode:{type:Object,required:!0}},emits:["update:flowNode","find:parentNode"],setup(Q,{emit:le}){const X=Q,U=le,v=ve("readonly"),n=ve("tasks",$([])),d=Ce(X),{showInput:x,blurEvent:I,clickTitle:h}=dl(d,w.START_USER_NODE),R=$(),S=()=>{v?n&&n.value&&(f.value=d.value.name,A.value=n.value.filter(p=>(p==null?void 0:p.taskDefinitionKey)===d.value.id),s.value=!0):(R.value.showUserTaskNodeConfig(d.value),R.value.openDrawer())},k=()=>{U("update:flowNode",d.value.childNode)},r=p=>{U("find:parentNode",p,w.USER_TASK_NODE)},s=$(!1),f=$(void 0),A=$([]);return(p,_)=>{var F;const H=me,D=zl,z=Ll,K=Gl,Y=bl,C=Ml,Z=ke("mountedFocus");return l(),i(L,null,[o("div",ao,[o("div",oo,[o("div",{class:Se(["node-box",[{"node-config-error":!e(d).showText},`${e(Ie)((F=e(d))==null?void 0:F.activityStatus)}`]])},[o("div",no,[o("div",{class:Se("node-title-icon "+(e(d).type===e(w).TRANSACTOR_NODE?"transactor-task":"user-task"))},[o("span",{class:Se("iconfont "+(e(d).type===e(w).TRANSACTOR_NODE?"icon-transactor":"icon-approve"))},null,2)],2),!e(v)&&e(x)?Ee((l(),i("input",{key:0,type:"text",class:"editable-title-input",onBlur:_[0]||(_[0]=T=>e(I)()),"onUpdate:modelValue":_[1]||(_[1]=T=>e(d).name=T),placeholder:e(d).name},null,40,io)),[[Z],[Ne,e(d).name]]):(l(),i("div",{key:1,class:"node-title",onClick:_[2]||(_[2]=(...T)=>e(h)&&e(h)(...T))},M(e(d).name),1))]),o("div",{class:"node-content",onClick:S},[e(d).showText?(l(),i("div",{key:0,class:"node-text",title:e(d).showText},M(e(d).showText),9,so)):(l(),i("div",ro,M(e(Be).get(e(d).type)),1)),e(v)?N("",!0):(l(),c(H,{key:2,icon:"ep:arrow-right-bold"}))]),e(v)?N("",!0):(l(),i("div",uo,[o("div",co,[t(H,{color:"#0089ff",icon:"ep:circle-close-filled",size:18,onClick:k})])]))],2),e(d)?(l(),c(He,{key:0,"child-node":e(d).childNode,"onUpdate:childNode":_[3]||(_[3]=T=>e(d).childNode=T),"current-node":e(d)},null,8,["child-node","current-node"])):N("",!0)])]),e(d)?(l(),c(to,{key:0,ref_key:"nodeSetting",ref:R,"flow-node":e(d),"onFind:returnTaskNodes":r},null,8,["flow-node"])):N("",!0),t(C,{title:e(f)||"\u5BA1\u6279\u8BB0\u5F55",modelValue:e(s),"onUpdate:modelValue":_[4]||(_[4]=T=>ie(s)?s.value=T:null),width:"1000px","append-to-body":""},{default:a(()=>[t(Y,null,{default:a(()=>[t(K,{data:e(A),size:"small",border:"","header-cell-class-name":"table-header-gray"},{default:a(()=>[t(D,{label:"\u5E8F\u53F7","header-align":"center",align:"center",type:"index",width:"50"}),t(D,{label:"\u5BA1\u6279\u4EBA","min-width":"100",align:"center"},{default:a(T=>{var b,O;return[P(M(((b=T.row.assigneeUser)==null?void 0:b.nickname)||((O=T.row.ownerUser)==null?void 0:O.nickname)),1)]}),_:1}),t(D,{label:"\u90E8\u95E8","min-width":"100",align:"center"},{default:a(T=>{var b,O;return[P(M(((b=T.row.assigneeUser)==null?void 0:b.deptName)||((O=T.row.ownerUser)==null?void 0:O.deptName)),1)]}),_:1}),t(D,{formatter:e(ml),align:"center",label:"\u5F00\u59CB\u65F6\u95F4",prop:"createTime","min-width":"140"},null,8,["formatter"]),t(D,{formatter:e(ml),align:"center",label:"\u7ED3\u675F\u65F6\u95F4",prop:"endTime","min-width":"140"},null,8,["formatter"]),t(D,{align:"center",label:"\u5BA1\u6279\u72B6\u6001",prop:"status","min-width":"90"},{default:a(T=>[t(z,{type:e(Fl).BPM_TASK_STATUS,value:T.row.status},null,8,["type","value"])]),_:1}),t(D,{align:"center",label:"\u5BA1\u6279\u5EFA\u8BAE",prop:"reason","min-width":"120"}),t(D,{align:"center",label:"\u8017\u65F6",prop:"durationInMillis",width:"100"},{default:a(T=>[P(M(e(Kl)(T.row.durationInMillis)),1)]),_:1})]),_:1},8,["data"])]),_:1})]),_:1},8,["title","modelValue"])],64)}}}),mo={class:"config-header"},vo=["placeholder"],fo={key:1,class:"node-name"},yo={class:"field-setting-pane"},go={class:"field-permit-title"},ho={class:"other-titles"},_o={class:"field-setting-item-label"},bo={class:"item-radio-wrap"},Eo={class:"item-radio-wrap"},No={class:"item-radio-wrap"},To=pe({name:"CopyTaskNodeConfig",__name:"CopyTaskNodeConfig",props:{flowNode:{type:Object,required:!0}},setup(Q,{expose:le}){const X=Q,U=Ue(()=>{let J="\u90E8\u95E8\u8D1F\u8D23\u4EBA\u6765\u6E90";return m.value.candidateStrategy==oe.MULTI_LEVEL_DEPT_LEADER?J+="(\u6307\u5B9A\u90E8\u95E8\u5411\u4E0A)":J+="(\u53D1\u8D77\u4EBA\u90E8\u95E8\u5411\u4E0A)",J}),{settingVisible:v,closeDrawer:n,openDrawer:d}=nl(),x=Ce(X),{nodeName:I,showInput:h,clickIcon:R,blurEvent:S}=il(w.COPY_TASK_NODE),k=$("user"),{formType:r,fieldsPermissionConfig:s,formFieldOptions:f,getNodeConfigFormFields:A}=jl(de.READ),p=Ue(()=>f.filter(J=>J.type==="UserSelect")),_=Ue(()=>f.filter(J=>J.type==="DeptSelect")),H=$(),D=tl({candidateStrategy:[{required:!0,message:"\u6284\u9001\u4EBA\u8BBE\u7F6E\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],userIds:[{required:!0,message:"\u7528\u6237\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],roleIds:[{required:!0,message:"\u89D2\u8272\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],deptIds:[{required:!0,message:"\u90E8\u95E8\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],userGroups:[{required:!0,message:"\u7528\u6237\u7EC4\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],postIds:[{required:!0,message:"\u5C97\u4F4D\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],formUser:[{required:!0,message:"\u8868\u5355\u5185\u7528\u6237\u5B57\u6BB5\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],formDept:[{required:!0,message:"\u8868\u5355\u5185\u90E8\u95E8\u5B57\u6BB5\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],expression:[{required:!0,message:"\u6D41\u7A0B\u8868\u8FBE\u5F0F\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),{configForm:z,roleOptions:K,postOptions:Y,userOptions:C,userGroupOptions:Z,deptTreeOptions:F,getShowText:T,handleCandidateParam:b,parseCandidateParam:O}=ut(w.COPY_TASK_NODE),m=z,G=Ue(()=>Jl.filter(J=>J.value!==oe.START_USER)),W=()=>{m.value.userIds=[],m.value.deptIds=[],m.value.roleIds=[],m.value.postIds=[],m.value.userGroups=[],m.value.deptLevel=1,m.value.formUser=""},B=async()=>{if(k.value="user",!H||!await H.value.validate())return!1;const J=T();return!!J&&(x.value.name=I.value,x.value.candidateParam=b(),x.value.candidateStrategy=m.value.candidateStrategy,x.value.showText=J,x.value.fieldsPermission=s.value,v.value=!1,!0)},ee=J=>{s.value.forEach(q=>{q.permission=J==="READ"?de.READ:J==="WRITE"?de.WRITE:de.NONE})};return le({openDrawer:d,showCopyTaskNodeConfig:J=>{I.value=J.name,m.value.candidateStrategy=J.candidateStrategy,O(J.candidateStrategy,J==null?void 0:J.candidateParam),A(J.fieldsPermission)}}),(J,q)=>{const u=me,qe=cl,Ge=ol,ge=Xe,Oe=Qe,Ae=We,te=dt,ue=pl,ae=Ke,he=Il,V=xl,re=je,ne=Le,xe=Ye,vl=ke("mountedFocus");return l(),c(xe,{"append-to-body":!0,modelValue:e(v),"onUpdate:modelValue":q[17]||(q[17]=g=>ie(v)?v.value=g:null),"show-close":!1,size:550,"before-close":B},{header:a(()=>[o("div",mo,[e(h)?Ee((l(),i("input",{key:0,type:"text",class:"config-editable-input",onBlur:q[0]||(q[0]=g=>e(S)()),"onUpdate:modelValue":q[1]||(q[1]=g=>ie(I)?I.value=g:null),placeholder:e(I)},null,40,vo)),[[vl],[Ne,e(I)]]):(l(),i("div",fo,[P(M(e(I))+" ",1),t(u,{class:"ml-1",icon:"ep:edit-pen",size:16,onClick:q[2]||(q[2]=g=>e(R)())})])),q[18]||(q[18]=o("div",{class:"divide-line"},null,-1))])]),footer:a(()=>[t(re),o("div",null,[t(ne,{type:"primary",onClick:B},{default:a(()=>q[24]||(q[24]=[P("\u786E \u5B9A")])),_:1}),t(ne,{onClick:e(n)},{default:a(()=>q[25]||(q[25]=[P("\u53D6 \u6D88")])),_:1},8,["onClick"])])]),default:a(()=>[t(V,{type:"border-card",modelValue:e(k),"onUpdate:modelValue":q[16]||(q[16]=g=>ie(k)?k.value=g:null)},{default:a(()=>[t(he,{label:"\u6284\u9001\u4EBA",name:"user"},{default:a(()=>[o("div",null,[t(ae,{ref_key:"formRef",ref:H,model:e(m),"label-position":"top",rules:e(D)},{default:a(()=>[t(ge,{label:"\u6284\u9001\u4EBA\u8BBE\u7F6E",prop:"candidateStrategy"},{default:a(()=>[t(Ge,{modelValue:e(m).candidateStrategy,"onUpdate:modelValue":q[3]||(q[3]=g=>e(m).candidateStrategy=g),onChange:W},{default:a(()=>[(l(!0),i(L,null,j(e(G),(g,E)=>(l(),c(qe,{key:E,value:g.value,label:g.value},{default:a(()=>[P(M(g.label),1)]),_:2},1032,["value","label"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(m).candidateStrategy==e(oe).ROLE?(l(),c(ge,{key:0,label:"\u6307\u5B9A\u89D2\u8272",prop:"roleIds"},{default:a(()=>[t(Ae,{modelValue:e(m).roleIds,"onUpdate:modelValue":q[4]||(q[4]=g=>e(m).roleIds=g),clearable:"",multiple:"",style:{width:"100%"}},{default:a(()=>[(l(!0),i(L,null,j(e(K),g=>(l(),c(Oe,{key:g.id,label:g.name,value:g.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):N("",!0),e(m).candidateStrategy==e(oe).DEPT_MEMBER||e(m).candidateStrategy==e(oe).DEPT_LEADER||e(m).candidateStrategy==e(oe).MULTI_LEVEL_DEPT_LEADER?(l(),c(ge,{key:1,label:"\u6307\u5B9A\u90E8\u95E8",prop:"deptIds",span:"24"},{default:a(()=>[t(te,{ref:"treeRef",modelValue:e(m).deptIds,"onUpdate:modelValue":q[5]||(q[5]=g=>e(m).deptIds=g),data:e(F),props:e(pt),"empty-text":"\u52A0\u8F7D\u4E2D\uFF0C\u8BF7\u7A0D\u540E",multiple:"","node-key":"id",style:{width:"100%"},"show-checkbox":""},null,8,["modelValue","data","props"])]),_:1})):N("",!0),e(m).candidateStrategy==e(oe).POST?(l(),c(ge,{key:2,label:"\u6307\u5B9A\u5C97\u4F4D",prop:"postIds",span:"24"},{default:a(()=>[t(Ae,{modelValue:e(m).postIds,"onUpdate:modelValue":q[6]||(q[6]=g=>e(m).postIds=g),clearable:"",multiple:"",style:{width:"100%"}},{default:a(()=>[(l(!0),i(L,null,j(e(Y),g=>(l(),c(Oe,{key:g.id,label:g.name,value:g.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):N("",!0),e(m).candidateStrategy==e(oe).USER?(l(),c(ge,{key:3,label:"\u6307\u5B9A\u7528\u6237",prop:"userIds",span:"24"},{default:a(()=>[t(Ae,{modelValue:e(m).userIds,"onUpdate:modelValue":q[7]||(q[7]=g=>e(m).userIds=g),clearable:"",multiple:"",style:{width:"100%"}},{default:a(()=>[(l(!0),i(L,null,j(e(C),g=>(l(),c(Oe,{key:g.id,label:g.nickname,value:g.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):N("",!0),e(m).candidateStrategy===e(oe).USER_GROUP?(l(),c(ge,{key:4,label:"\u6307\u5B9A\u7528\u6237\u7EC4",prop:"userGroups"},{default:a(()=>[t(Ae,{modelValue:e(m).userGroups,"onUpdate:modelValue":q[8]||(q[8]=g=>e(m).userGroups=g),clearable:"",multiple:"",style:{width:"100%"}},{default:a(()=>[(l(!0),i(L,null,j(e(Z),g=>(l(),c(Oe,{key:g.id,label:g.name,value:g.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):N("",!0),e(m).candidateStrategy===e(oe).FORM_USER?(l(),c(ge,{key:5,label:"\u8868\u5355\u5185\u7528\u6237\u5B57\u6BB5",prop:"formUser"},{default:a(()=>[t(Ae,{modelValue:e(m).formUser,"onUpdate:modelValue":q[9]||(q[9]=g=>e(m).formUser=g),clearable:"",style:{width:"100%"}},{default:a(()=>[(l(!0),i(L,null,j(e(p),(g,E)=>(l(),c(Oe,{key:E,label:g.title,value:g.field,disabled:!g.required},null,8,["label","value","disabled"]))),128))]),_:1},8,["modelValue"])]),_:1})):N("",!0),e(m).candidateStrategy===e(oe).FORM_DEPT_LEADER?(l(),c(ge,{key:6,label:"\u8868\u5355\u5185\u90E8\u95E8\u5B57\u6BB5",prop:"formDept"},{default:a(()=>[t(Ae,{modelValue:e(m).formDept,"onUpdate:modelValue":q[10]||(q[10]=g=>e(m).formDept=g),clearable:"",style:{width:"100%"}},{default:a(()=>[(l(!0),i(L,null,j(e(_),(g,E)=>(l(),c(Oe,{key:E,label:g.title,value:g.field,disabled:!g.required},null,8,["label","value","disabled"]))),128))]),_:1},8,["modelValue"])]),_:1})):N("",!0),e(m).candidateStrategy==e(oe).MULTI_LEVEL_DEPT_LEADER||e(m).candidateStrategy==e(oe).START_USER_DEPT_LEADER||e(m).candidateStrategy==e(oe).START_USER_MULTI_LEVEL_DEPT_LEADER||e(m).candidateStrategy==e(oe).FORM_DEPT_LEADER?(l(),c(ge,{key:7,label:e(U),prop:"deptLevel",span:"24"},{default:a(()=>[t(Ae,{modelValue:e(m).deptLevel,"onUpdate:modelValue":q[11]||(q[11]=g=>e(m).deptLevel=g),clearable:""},{default:a(()=>[(l(!0),i(L,null,j(e(Zl),(g,E)=>(l(),c(Oe,{key:E,label:g.label,value:g.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["label"])):N("",!0),e(m).candidateStrategy===e(oe).EXPRESSION?(l(),c(ge,{key:8,label:"\u6D41\u7A0B\u8868\u8FBE\u5F0F",prop:"expression"},{default:a(()=>[t(ue,{type:"textarea",modelValue:e(m).expression,"onUpdate:modelValue":q[12]||(q[12]=g=>e(m).expression=g),clearable:"",style:{width:"100%"}},null,8,["modelValue"])]),_:1})):N("",!0)]),_:1},8,["model","rules"])])]),_:1}),e(r)===10?(l(),c(he,{key:0,label:"\u8868\u5355\u5B57\u6BB5\u6743\u9650",name:"fields"},{default:a(()=>[o("div",yo,[q[23]||(q[23]=o("div",{class:"field-setting-desc"},"\u5B57\u6BB5\u6743\u9650",-1)),o("div",go,[q[19]||(q[19]=o("div",{class:"setting-title-label first-title"}," \u5B57\u6BB5\u540D\u79F0 ",-1)),o("div",ho,[o("span",{class:"setting-title-label cursor-pointer",onClick:q[13]||(q[13]=g=>ee("READ"))}," \u53EA\u8BFB "),o("span",{class:"setting-title-label cursor-pointer",onClick:q[14]||(q[14]=g=>ee("WRITE"))}," \u53EF\u7F16\u8F91 "),o("span",{class:"setting-title-label cursor-pointer",onClick:q[15]||(q[15]=g=>ee("NONE"))}," \u9690\u85CF ")])]),(l(!0),i(L,null,j(e(s),(g,E)=>(l(),i("div",{class:"field-setting-item",key:E},[o("div",_o,M(g.title),1),t(Ge,{class:"field-setting-item-group",modelValue:g.permission,"onUpdate:modelValue":we=>g.permission=we},{default:a(()=>[o("div",bo,[t(qe,{value:e(de).READ,size:"large",label:e(de).WRITE},{default:a(()=>q[20]||(q[20]=[o("span",null,null,-1)])),_:1},8,["value","label"])]),o("div",Eo,[t(qe,{value:e(de).WRITE,size:"large",label:e(de).WRITE,disabled:""},{default:a(()=>q[21]||(q[21]=[o("span",null,null,-1)])),_:1},8,["value","label"])]),o("div",No,[t(qe,{value:e(de).NONE,size:"large",label:e(de).NONE},{default:a(()=>q[22]||(q[22]=[o("span",null,null,-1)])),_:1},8,["value","label"])])]),_:2},1032,["modelValue","onUpdate:modelValue"])]))),128))])]),_:1})):N("",!0)]),_:1},8,["modelValue"])]),_:1},8,["modelValue"])}}}),ko={class:"node-wrapper"},So={class:"node-container"},Uo={class:"node-title-container"},wo=["placeholder"],Vo=["title"],Co={key:1,class:"node-text"},Ro={key:0,class:"node-toolbar"},Do={class:"toolbar-icon"},xo=pe({name:"CopyTaskNode",__name:"CopyTaskNode",props:{flowNode:{type:Object,required:!0}},emits:["update:flowNode"],setup(Q,{emit:le}){const X=Q,U=le,v=ve("readonly"),n=Ce(X),{showInput:d,blurEvent:x,clickTitle:I}=dl(n,w.COPY_TASK_NODE),h=$(),R=()=>{v||(h.value.showCopyTaskNodeConfig(n.value),h.value.openDrawer())},S=()=>{U("update:flowNode",n.value.childNode)};return(k,r)=>{var A;const s=me,f=ke("mountedFocus");return l(),i("div",ko,[o("div",So,[o("div",{class:Se(["node-box",[{"node-config-error":!e(n).showText},`${e(Ie)((A=e(n))==null?void 0:A.activityStatus)}`]])},[o("div",Uo,[r[4]||(r[4]=o("div",{class:"node-title-icon copy-task"},[o("span",{class:"iconfont icon-copy"})],-1)),!e(v)&&e(d)?Ee((l(),i("input",{key:0,type:"text",class:"editable-title-input",onBlur:r[0]||(r[0]=p=>e(x)()),"onUpdate:modelValue":r[1]||(r[1]=p=>e(n).name=p),placeholder:e(n).name},null,40,wo)),[[f],[Ne,e(n).name]]):(l(),i("div",{key:1,class:"node-title",onClick:r[2]||(r[2]=(...p)=>e(I)&&e(I)(...p))},M(e(n).name),1))]),o("div",{class:"node-content",onClick:R},[e(n).showText?(l(),i("div",{key:0,class:"node-text",title:e(n).showText},M(e(n).showText),9,Vo)):(l(),i("div",Co,M(e(Be).get(e(w).COPY_TASK_NODE)),1)),e(v)?N("",!0):(l(),c(s,{key:2,icon:"ep:arrow-right-bold"}))]),e(v)?N("",!0):(l(),i("div",Ro,[o("div",Do,[t(s,{color:"#0089ff",icon:"ep:circle-close-filled",size:18,onClick:S})])]))],2),e(n)?(l(),c(He,{key:0,"child-node":e(n).childNode,"onUpdate:childNode":r[3]||(r[3]=p=>e(n).childNode=p),"current-node":e(n)},null,8,["child-node","current-node"])):N("",!0)]),!e(v)&&e(n)?(l(),c(To,{key:0,ref_key:"nodeSetting",ref:h,"flow-node":e(n)},null,8,["flow-node"])):N("",!0)])}}}),Io={class:"condition-group-tool"},Oo={class:"flex items-center"},Ao={key:0,class:"condition-group-delete"},Po={class:"flex items-center justify-between"},Fo={class:"flex"},Lo={class:"mr-2"},Ho={class:"mr-2"},qo={class:"mr-2"},Mo={key:0,class:"mr-1 flex items-center"},Go={class:"flex items-center"},zo={title:"\u6DFB\u52A0\u6761\u4EF6\u7EC4",class:"mt-4 cursor-pointer"},ft=yl(pe({__name:"Condition",props:{modelValue:{type:Object,required:!0}},emits:["update:modelValue"],setup(Q,{expose:le,emit:X}){const U=Q,v=X,n=Ue({get:()=>U.modelValue,set(k){v("update:modelValue",k)}}),d=ve("formType"),x=Ue(()=>lt.filter(k=>(d==null?void 0:d.value)!==vt.CUSTOM||k.value!==_e.RULE)),I=Al(),h=tl({conditionType:[{required:!0,message:"\u914D\u7F6E\u65B9\u5F0F\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],conditionExpression:[{required:!0,message:"\u6761\u4EF6\u8868\u8FBE\u5F0F\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),R=$(),S=()=>{n.value.conditionType===_e.RULE&&(n.value.conditionGroups||(n.value.conditionGroups=De(Me)))};return le({validate:async()=>!!R&&await R.value.validate()}),(k,r)=>{const s=cl,f=ol,A=Xe,p=El,_=me,H=it,D=Qe,z=We,K=pl,Y=Ol,C=st,Z=Ke;return l(),c(Z,{ref_key:"formRef",ref:R,model:e(n),rules:e(h),"label-position":"top"},{default:a(()=>[t(A,{label:"\u914D\u7F6E\u65B9\u5F0F",prop:"conditionType"},{default:a(()=>[t(f,{modelValue:e(n).conditionType,"onUpdate:modelValue":r[0]||(r[0]=F=>e(n).conditionType=F),onChange:S},{default:a(()=>[(l(!0),i(L,null,j(e(x),(F,T)=>(l(),c(s,{key:T,value:F.value,label:F.value},{default:a(()=>[P(M(F.label),1)]),_:2},1032,["value","label"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(n).conditionType===e(_e).RULE&&e(n).conditionGroups?(l(),c(A,{key:0,label:"\u6761\u4EF6\u89C4\u5219"},{default:a(()=>[o("div",Io,[o("div",Oo,[r[4]||(r[4]=o("div",{class:"mr-4"},"\u6761\u4EF6\u7EC4\u5173\u7CFB",-1)),t(p,{modelValue:e(n).conditionGroups.and,"onUpdate:modelValue":r[1]||(r[1]=F=>e(n).conditionGroups.and=F),"inline-prompt":"","active-text":"\u4E14","inactive-text":"\u6216"},null,8,["modelValue"])])]),t(C,{direction:"vertical",spacer:e(n).conditionGroups.and?"\u4E14":"\u6216"},{default:a(()=>[(l(!0),i(L,null,j(e(n).conditionGroups.conditions,(F,T)=>(l(),c(Y,{class:"condition-group",style:{width:"530px"},key:T},{header:a(()=>[o("div",Po,[r[6]||(r[6]=o("div",null,"\u6761\u4EF6\u7EC4",-1)),o("div",Fo,[r[5]||(r[5]=o("div",{class:"mr-4"},"\u89C4\u5219\u5173\u7CFB",-1)),t(p,{modelValue:F.and,"onUpdate:modelValue":b=>F.and=b,"inline-prompt":"","active-text":"\u4E14","inactive-text":"\u6216"},null,8,["modelValue","onUpdate:modelValue"])])])]),default:a(()=>[e(n).conditionGroups.conditions.length>1?(l(),i("div",Ao,[t(_,{color:"#0089ff",icon:"ep:circle-close-filled",size:18,onClick:b=>{return O=e(n).conditionGroups.conditions,m=T,void O.splice(m,1);var O,m}},null,8,["onClick"])])):N("",!0),(l(!0),i(L,null,j(F.rules,(b,O)=>(l(),i("div",{class:"flex pt-2",key:O},[o("div",Lo,[t(A,{prop:`conditionGroups.conditions.${T}.rules.${O}.leftSide`,rules:{required:!0,message:"\u5DE6\u503C\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}},{default:a(()=>[t(z,{style:{width:"160px"},modelValue:b.leftSide,"onUpdate:modelValue":m=>b.leftSide=m,clearable:""},{default:a(()=>[(l(!0),i(L,null,j(e(I),(m,G)=>(l(),c(D,{key:G,label:m.title,value:m.field,disabled:!m.required},{default:a(()=>[m.required?N("",!0):(l(),c(H,{key:0,content:"\u8868\u5355\u5B57\u6BB5\u975E\u5FC5\u586B\u65F6\u4E0D\u80FD\u4F5C\u4E3A\u6D41\u7A0B\u5206\u652F\u6761\u4EF6",effect:"dark",placement:"right-start"},{default:a(()=>[o("span",null,M(m.title),1)]),_:2},1024))]),_:2},1032,["label","value","disabled"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"])]),o("div",Ho,[t(z,{modelValue:b.opCode,"onUpdate:modelValue":m=>b.opCode=m,style:{width:"100px"}},{default:a(()=>[(l(!0),i(L,null,j(e(tt),m=>(l(),c(D,{key:m.value,label:m.label,value:m.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),o("div",qo,[t(A,{prop:`conditionGroups.conditions.${T}.rules.${O}.rightSide`,rules:{required:!0,message:"\u53F3\u503C\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}},{default:a(()=>[t(K,{modelValue:b.rightSide,"onUpdate:modelValue":m=>b.rightSide=m,style:{width:"160px"}},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"])]),F.rules.length>1?(l(),i("div",Mo,[t(_,{icon:"ep:delete",size:18,onClick:m=>{return G=O,void F.rules.splice(G,1);var G}},null,8,["onClick"])])):N("",!0),o("div",Go,[t(_,{icon:"ep:plus",size:18,onClick:m=>{return G=O,void F.rules.splice(G+1,0,{opCode:"==",leftSide:"",rightSide:""});var G}},null,8,["onClick"])])]))),128))]),_:2},1024))),128))]),_:1},8,["spacer"]),o("div",zo,[t(_,{color:"#0089ff",icon:"ep:plus",size:24,onClick:r[2]||(r[2]=F=>{var b;return T=(b=e(n).conditionGroups)==null?void 0:b.conditions,void T.push({and:!0,rules:[{opCode:"==",leftSide:"",rightSide:""}]});var T})})])]),_:1})):N("",!0),e(n).conditionType===e(_e).EXPRESSION?(l(),c(A,{key:1,label:"\u6761\u4EF6\u8868\u8FBE\u5F0F",prop:"conditionExpression"},{default:a(()=>[t(K,{type:"textarea",modelValue:e(n).conditionExpression,"onUpdate:modelValue":r[3]||(r[3]=F=>e(n).conditionExpression=F),clearable:"",style:{width:"100%"}},null,8,["modelValue"])]),_:1})):N("",!0)]),_:1},8,["model","rules"])}}}),[["__scopeId","data-v-41561363"]]),Bo={class:"config-header"},$o=["placeholder"],jo={key:1,class:"node-name"},Ko={key:0,class:"mb-3 font-size-16px"},Yo={key:1},yt=yl(pe({name:"ConditionNodeConfig",__name:"ConditionNodeConfig",props:{conditionNode:{type:Object,required:!0},nodeIndex:{type:Number,required:!0}},setup(Q,{expose:le}){const X=Q,U=$(!1),v=$(X.conditionNode),n=$({conditionType:_e.RULE,conditionExpression:"",conditionGroups:{and:!0,conditions:[{and:!0,rules:[{opCode:"==",leftSide:"",rightSide:""}]}]}});Cl(()=>X.conditionNode,k=>{v.value=k});const d=$(!1);le({open:()=>{v.value.conditionSetting?n.value=De(v.value.conditionSetting):n.value={conditionType:_e.RULE,conditionExpression:"",conditionGroups:{and:!0,conditions:[{and:!0,rules:[{opCode:"==",leftSide:"",rightSide:""}]}]}},U.value=!0}});const x=()=>{U.value=!1},I=async k=>{await S()?k():k(!0)},h=Al(),R=$(),S=async()=>{var k,r,s,f,A,p,_,H;if(!((k=v.value.conditionSetting)!=null&&k.defaultFlow)){if(!await R.value.validate())return!1;const D=ct((r=n.value)==null?void 0:r.conditionType,(s=n.value)==null?void 0:s.conditionExpression,n.value.conditionGroups,h);if(!D)return!1;v.value.showText=D,v.value.conditionSetting=De({...v.value.conditionSetting,conditionType:(f=n.value)==null?void 0:f.conditionType,conditionExpression:((A=n.value)==null?void 0:A.conditionType)===_e.EXPRESSION?(p=n.value)==null?void 0:p.conditionExpression:void 0,conditionGroups:((_=n.value)==null?void 0:_.conditionType)===_e.RULE?(H=n.value)==null?void 0:H.conditionGroups:void 0})}return U.value=!1,!0};return(k,r)=>{const s=me,f=je,A=Le,p=Ye,_=ke("mountedFocus");return l(),c(p,{"append-to-body":!0,modelValue:e(U),"onUpdate:modelValue":r[4]||(r[4]=H=>ie(U)?U.value=H:null),"show-close":!1,size:588,"before-close":I},{header:a(()=>[o("div",Bo,[e(d)?Ee((l(),i("input",{key:0,type:"text",class:"config-editable-input",onBlur:r[0]||(r[0]=H=>{var D,z;return d.value=!1,void(v.value.name=v.value.name||mt(X.nodeIndex,(z=(D=v.value)==null?void 0:D.conditionSetting)==null?void 0:z.defaultFlow))}),"onUpdate:modelValue":r[1]||(r[1]=H=>e(v).name=H),placeholder:e(v).name},null,40,$o)),[[_],[Ne,e(v).name]]):(l(),i("div",jo,[P(M(e(v).name)+" ",1),t(s,{class:"ml-1",icon:"ep:edit-pen",size:16,onClick:r[2]||(r[2]=H=>{d.value=!0})})])),r[5]||(r[5]=o("div",{class:"divide-line"},null,-1))])]),footer:a(()=>[t(f),o("div",null,[t(A,{type:"primary",onClick:S},{default:a(()=>r[6]||(r[6]=[P("\u786E \u5B9A")])),_:1}),t(A,{onClick:x},{default:a(()=>r[7]||(r[7]=[P("\u53D6 \u6D88")])),_:1})])]),default:a(()=>{var H;return[o("div",null,[(H=e(v).conditionSetting)!=null&&H.defaultFlow?(l(),i("div",Ko,"\u672A\u6EE1\u8DB3\u5176\u5B83\u6761\u4EF6\u65F6\uFF0C\u5C06\u8FDB\u5165\u6B64\u5206\u652F\uFF08\u8BE5\u5206\u652F\u4E0D\u53EF\u7F16\u8F91\u548C\u5220\u9664\uFF09")):(l(),i("div",Yo,[t(ft,{ref_key:"conditionRef",ref:R,modelValue:e(n),"onUpdate:modelValue":r[3]||(r[3]=D=>ie(n)?n.value=D:null)},null,8,["modelValue"])]))])]}),_:1},8,["modelValue"])}}}),[["__scopeId","data-v-75ae5026"]]),Xo={class:"branch-node-wrapper"},Wo={class:"branch-node-container"},Qo={class:"node-wrapper"},Jo={class:"node-container"},Zo={class:"branch-node-title-container"},en={key:0},ln=["onBlur","onUpdate:modelValue"],tn=["onClick"],an={class:"branch-priority"},on=["onClick"],nn=["title"],dn={key:1,class:"branch-node-text"},sn={key:0,class:"node-toolbar"},rn={class:"toolbar-icon"},un=["onClick"],cn=["onClick"],pn=pe({name:"ExclusiveNode",__name:"ExclusiveNode",props:{flowNode:{type:Object,required:!0}},emits:["update:modelValue","find:parentNode","find:recursiveFindParentNode"],setup(Q,{emit:le}){const{proxy:X}=Rl(),U=Q,v=le,n=ve("readonly"),d=$(U.flowNode);Cl(()=>U.flowNode,S=>{d.value=S});const x=$([]),I=()=>{const S=d.value.conditionNodes;if(S){const k=S.length;let r=k-1;const s={id:"Flow_"+Te(),name:"\u6761\u4EF6"+k,showText:"",type:w.CONDITION_NODE,childNode:void 0,conditionNodes:[],conditionSetting:{defaultFlow:!1,conditionType:_e.RULE,conditionGroups:De(Me)}};S.splice(r,0,s)}},h=(S,k)=>{d.value.conditionNodes&&(d.value.conditionNodes[S]=d.value.conditionNodes.splice(S+k,1,d.value.conditionNodes[S])[0])},R=(S,k,r)=>{k&&k.type!==w.START_USER_NODE&&(k.type===r&&S.push(k),v("find:parentNode",S,r))};return(S,k)=>{var A;const r=Le,s=me,f=ke("mountedFocus");return l(),i("div",Xo,[o("div",Wo,[e(n)?(l(),i("div",{key:0,class:Se(["branch-node-readonly",`${e(Ie)((A=e(d))==null?void 0:A.activityStatus)}`])},k[1]||(k[1]=[o("span",{class:"iconfont icon-exclusive icon-size condition"},null,-1)]),2)):(l(),c(r,{key:1,class:"branch-node-add",color:"#67c23a",onClick:I,plain:""},{default:a(()=>k[2]||(k[2]=[P("\u6DFB\u52A0\u6761\u4EF6")])),_:1})),(l(!0),i(L,null,j(e(d).conditionNodes,(p,_)=>{var H,D,z;return l(),i("div",{class:"branch-node-item",key:_},[_==0?(l(),i(L,{key:0},[k[3]||(k[3]=o("div",{class:"branch-line-first-top"},null,-1)),k[4]||(k[4]=o("div",{class:"branch-line-first-bottom"},null,-1))],64)):N("",!0),_+1==((H=e(d).conditionNodes)==null?void 0:H.length)?(l(),i(L,{key:1},[k[5]||(k[5]=o("div",{class:"branch-line-last-top"},null,-1)),k[6]||(k[6]=o("div",{class:"branch-line-last-bottom"},null,-1))],64)):N("",!0),o("div",Qo,[o("div",Jo,[o("div",{class:Se(["node-box",[{"node-config-error":!p.showText},`${e(Ie)(p.activityStatus)}`]])},[o("div",Zo,[!e(n)&&e(x)[_]?(l(),i("div",en,[Ee(o("input",{type:"text",class:"input-max-width editable-title-input",onBlur:K=>(Y=>{var Z,F;x.value[Y]=!1;const C=(Z=d.value.conditionNodes)==null?void 0:Z.at(Y);C.name=C.name||mt(Y,(F=C.conditionSetting)==null?void 0:F.defaultFlow)})(_),"onUpdate:modelValue":K=>p.name=K},null,40,ln),[[f],[Ne,p.name]])])):(l(),i("div",{key:1,class:"branch-title",onClick:K=>(Y=>{x.value[Y]=!0})(_)},M(p.name),9,tn)),o("div",an," \u4F18\u5148\u7EA7"+M(_+1),1)]),o("div",{class:"branch-node-content",onClick:K=>(Y=>{n||X.$refs[Y][0].open()})(p.id)},[p.showText?(l(),i("div",{key:0,class:"branch-node-text",title:p.showText},M(p.showText),9,nn)):(l(),i("div",dn,M(e(Be).get(e(w).CONDITION_NODE)),1))],8,on),e(n)||_+1===((D=e(d).conditionNodes)==null?void 0:D.length)?N("",!0):(l(),i("div",sn,[o("div",rn,[t(s,{color:"#0089ff",icon:"ep:circle-close-filled",size:18,onClick:K=>(Y=>{const C=d.value.conditionNodes;if(C&&(C.splice(Y,1),C.length==1)){const Z=d.value.childNode;v("update:modelValue",Z)}})(_)},null,8,["onClick"])])])),_!=0&&_+1!==((z=e(d).conditionNodes)==null?void 0:z.length)?(l(),i("div",{key:1,class:"branch-node-move move-node-left",onClick:K=>h(_,-1)},[t(s,{icon:"ep:arrow-left"})],8,un)):N("",!0),e(d).conditionNodes&&_<e(d).conditionNodes.length-2?(l(),i("div",{key:2,class:"branch-node-move move-node-right",onClick:K=>h(_,1)},[t(s,{icon:"ep:arrow-right"})],8,cn)):N("",!0)],2),t(He,{"child-node":p.childNode,"onUpdate:childNode":K=>p.childNode=K,"current-node":p},null,8,["child-node","onUpdate:childNode","current-node"])])]),t(yt,{"node-index":_,"condition-node":p,ref_for:!0,ref:p.id},null,8,["node-index","condition-node"]),p&&p.childNode?(l(),c(Pl,{key:2,"parent-node":p,"flow-node":p.childNode,"onUpdate:flowNode":K=>p.childNode=K,"onFind:recursiveFindParentNode":R},null,8,["parent-node","flow-node","onUpdate:flowNode"])):N("",!0)])}),128))]),e(d)?(l(),c(He,{key:0,"child-node":e(d).childNode,"onUpdate:childNode":k[0]||(k[0]=p=>e(d).childNode=p),"current-node":e(d)},null,8,["child-node","current-node"])):N("",!0)])}}}),mn={class:"branch-node-wrapper"},vn={class:"branch-node-container"},fn={class:"node-wrapper"},yn={class:"node-container"},gn={class:"branch-node-title-container"},hn={key:0},_n=["onBlur","onUpdate:modelValue"],bn=["onClick"],En=["onClick"],Nn=["title"],Tn={key:1,class:"branch-node-text"},kn={key:0,class:"node-toolbar"},Sn={class:"toolbar-icon"},Un=pe({name:"ParallelNode",__name:"ParallelNode",props:{flowNode:{type:Object,required:!0}},emits:["update:modelValue","find:parentNode","find:recursiveFindParentNode"],setup(Q,{emit:le}){const{proxy:X}=Rl(),U=Q,v=le,n=$(U.flowNode),d=ve("readonly");Cl(()=>U.flowNode,R=>{n.value=R});const x=$([]),I=()=>{const R=n.value.conditionNodes;if(R){const S=R.length;let k=S-1;const r={id:"Flow_"+Te(),name:"\u5E76\u884C"+S,showText:"\u65E0\u9700\u914D\u7F6E\u6761\u4EF6\u540C\u65F6\u6267\u884C",type:w.CONDITION_NODE,childNode:void 0,conditionNodes:[]};R.splice(k,0,r)}},h=(R,S,k)=>{S&&S.type!==w.START_USER_NODE&&(S.type===k&&R.push(S),v("find:parentNode",R,k))};return(R,S)=>{var f;const k=Le,r=me,s=ke("mountedFocus");return l(),i("div",mn,[o("div",vn,[e(d)?(l(),i("div",{key:0,class:Se(["branch-node-readonly",`${e(Ie)((f=e(n))==null?void 0:f.activityStatus)}`])},S[1]||(S[1]=[o("span",{class:"iconfont icon-parallel icon-size parallel"},null,-1)]),2)):(l(),c(k,{key:1,class:"branch-node-add",color:"#626aef",onClick:I,plain:""},{default:a(()=>S[2]||(S[2]=[P("\u6DFB\u52A0\u5206\u652F")])),_:1})),(l(!0),i(L,null,j(e(n).conditionNodes,(A,p)=>{var _;return l(),i("div",{class:"branch-node-item",key:p},[p==0?(l(),i(L,{key:0},[S[3]||(S[3]=o("div",{class:"branch-line-first-top"},null,-1)),S[4]||(S[4]=o("div",{class:"branch-line-first-bottom"},null,-1))],64)):N("",!0),p+1==((_=e(n).conditionNodes)==null?void 0:_.length)?(l(),i(L,{key:1},[S[5]||(S[5]=o("div",{class:"branch-line-last-top"},null,-1)),S[6]||(S[6]=o("div",{class:"branch-line-last-bottom"},null,-1))],64)):N("",!0),o("div",fn,[o("div",yn,[o("div",{class:Se(["node-box",`${e(Ie)(A.activityStatus)}`])},[o("div",gn,[e(x)[p]?(l(),i("div",hn,[Ee(o("input",{type:"text",class:"input-max-width editable-title-input",onBlur:H=>(D=>{var K;x.value[D]=!1;const z=(K=n.value.conditionNodes)==null?void 0:K.at(D);z.name=z.name||`\u5E76\u884C${D+1}`})(p),"onUpdate:modelValue":H=>A.name=H},null,40,_n),[[s],[Ne,A.name]])])):(l(),i("div",{key:1,class:"branch-title",onClick:H=>(D=>{x.value[D]=!0})(p)},M(A.name),9,bn)),S[7]||(S[7]=o("div",{class:"branch-priority"},"\u65E0\u4F18\u5148\u7EA7",-1))]),o("div",{class:"branch-node-content",onClick:H=>{return D=A.id,void X.$refs[D][0].open();var D}},[A.showText?(l(),i("div",{key:0,class:"branch-node-text",title:A.showText},M(A.showText),9,Nn)):(l(),i("div",Tn,M(e(Be).get(e(w).CONDITION_NODE)),1))],8,En),e(d)?N("",!0):(l(),i("div",kn,[o("div",Sn,[t(r,{color:"#0089ff",icon:"ep:circle-close-filled",size:18,onClick:H=>(D=>{const z=n.value.conditionNodes;if(z&&(z.splice(D,1),z.length==1)){const K=n.value.childNode;v("update:modelValue",K)}})(p)},null,8,["onClick"])])]))],2),t(He,{"child-node":A.childNode,"onUpdate:childNode":H=>A.childNode=H,"current-node":A},null,8,["child-node","onUpdate:childNode","current-node"])])]),A&&A.childNode?(l(),c(Pl,{key:2,"parent-node":A,"flow-node":A.childNode,"onUpdate:flowNode":H=>A.childNode=H,"onFind:recursiveFindParentNode":h},null,8,["parent-node","flow-node","onUpdate:flowNode"])):N("",!0)])}),128))]),e(n)?(l(),c(He,{key:0,"child-node":e(n).childNode,"onUpdate:childNode":S[0]||(S[0]=A=>e(n).childNode=A),"current-node":e(n)},null,8,["child-node","current-node"])):N("",!0)])}}}),wn={class:"branch-node-wrapper"},Vn={class:"branch-node-container"},Cn={class:"node-wrapper"},Rn={class:"node-container"},Dn={class:"branch-node-title-container"},xn={key:0},In=["onBlur","onUpdate:modelValue"],On=["onClick"],An=["onClick"],Pn=["title"],Fn={key:1,class:"branch-node-text"},Ln={key:0,class:"node-toolbar"},Hn={class:"toolbar-icon"},qn=["onClick"],Mn=["onClick"],Gn=pe({name:"InclusiveNode",__name:"InclusiveNode",props:{flowNode:{type:Object,required:!0}},emits:["update:modelValue","find:parentNode","find:recursiveFindParentNode"],setup(Q,{emit:le}){const{proxy:X}=Rl(),U=Q,v=le,n=ve("readonly"),d=$(U.flowNode);Cl(()=>U.flowNode,S=>{d.value=S});const x=$([]),I=()=>{const S=d.value.conditionNodes;if(S){const k=S.length;let r=k-1;const s={id:"Flow_"+Te(),name:"\u5305\u5BB9\u6761\u4EF6"+k,showText:"",type:w.CONDITION_NODE,childNode:void 0,conditionNodes:[],conditionSetting:{defaultFlow:!1,conditionType:_e.RULE,conditionGroups:De(Me)}};S.splice(r,0,s)}},h=(S,k)=>{d.value.conditionNodes&&(d.value.conditionNodes[S]=d.value.conditionNodes.splice(S+k,1,d.value.conditionNodes[S])[0])},R=(S,k,r)=>{k&&k.type!==w.START_USER_NODE&&(k.type===r&&S.push(k),v("find:parentNode",S,r))};return(S,k)=>{var A;const r=Le,s=me,f=ke("mountedFocus");return l(),i("div",wn,[o("div",Vn,[e(n)?(l(),i("div",{key:0,class:Se(["branch-node-readonly",`${e(Ie)((A=e(d))==null?void 0:A.activityStatus)}`])},k[1]||(k[1]=[o("span",{class:"iconfont icon-inclusive icon-size inclusive"},null,-1)]),2)):(l(),c(r,{key:1,class:"branch-node-add",color:"#345da2",onClick:I,plain:""},{default:a(()=>k[2]||(k[2]=[P("\u6DFB\u52A0\u6761\u4EF6")])),_:1})),(l(!0),i(L,null,j(e(d).conditionNodes,(p,_)=>{var H,D,z;return l(),i("div",{class:"branch-node-item",key:_},[_==0?(l(),i(L,{key:0},[k[3]||(k[3]=o("div",{class:"branch-line-first-top"},null,-1)),k[4]||(k[4]=o("div",{class:"branch-line-first-bottom"},null,-1))],64)):N("",!0),_+1==((H=e(d).conditionNodes)==null?void 0:H.length)?(l(),i(L,{key:1},[k[5]||(k[5]=o("div",{class:"branch-line-last-top"},null,-1)),k[6]||(k[6]=o("div",{class:"branch-line-last-bottom"},null,-1))],64)):N("",!0),o("div",Cn,[o("div",Rn,[o("div",{class:Se(["node-box",[{"node-config-error":!p.showText},`${e(Ie)(p.activityStatus)}`]])},[o("div",Dn,[!e(n)&&e(x)[_]?(l(),i("div",xn,[Ee(o("input",{type:"text",class:"editable-title-input",onBlur:K=>(Y=>{var Z,F;x.value[Y]=!1;const C=(Z=d.value.conditionNodes)==null?void 0:Z.at(Y);C.name=C.name||Wt(Y,(F=C.conditionSetting)==null?void 0:F.defaultFlow)})(_),"onUpdate:modelValue":K=>p.name=K},null,40,In),[[f],[Ne,p.name]])])):(l(),i("div",{key:1,class:"branch-title",onClick:K=>(Y=>{x.value[Y]=!0})(_)},M(p.name),9,On))]),o("div",{class:"branch-node-content",onClick:K=>(Y=>{n||X.$refs[Y][0].open()})(p.id)},[p.showText?(l(),i("div",{key:0,class:"branch-node-text",title:p.showText},M(p.showText),9,Pn)):(l(),i("div",Fn,M(e(Be).get(e(w).CONDITION_NODE)),1))],8,An),e(n)||_+1===((D=e(d).conditionNodes)==null?void 0:D.length)?N("",!0):(l(),i("div",Ln,[o("div",Hn,[t(s,{color:"#0089ff",icon:"ep:circle-close-filled",size:18,onClick:K=>(Y=>{const C=d.value.conditionNodes;if(C&&(C.splice(Y,1),C.length==1)){const Z=d.value.childNode;v("update:modelValue",Z)}})(_)},null,8,["onClick"])])])),e(n)||_==0||_+1===((z=e(d).conditionNodes)==null?void 0:z.length)?N("",!0):(l(),i("div",{key:1,class:"branch-node-move move-node-left",onClick:K=>h(_,-1)},[t(s,{icon:"ep:arrow-left"})],8,qn)),!e(n)&&e(d).conditionNodes&&_<e(d).conditionNodes.length-2?(l(),i("div",{key:2,class:"branch-node-move move-node-right",onClick:K=>h(_,1)},[t(s,{icon:"ep:arrow-right"})],8,Mn)):N("",!0)],2),t(He,{"child-node":p.childNode,"onUpdate:childNode":K=>p.childNode=K,"current-node":p},null,8,["child-node","onUpdate:childNode","current-node"])])]),t(yt,{"node-index":_,"condition-node":p,ref_for:!0,ref:p.id},null,8,["node-index","condition-node"]),p&&p.childNode?(l(),c(Pl,{key:2,"parent-node":p,"flow-node":p.childNode,"onUpdate:flowNode":K=>p.childNode=K,"onFind:recursiveFindParentNode":R},null,8,["parent-node","flow-node","onUpdate:flowNode"])):N("",!0)])}),128))]),e(d)?(l(),c(He,{key:0,"child-node":e(d).childNode,"onUpdate:childNode":k[0]||(k[0]=p=>e(d).childNode=p),"current-node":e(d)},null,8,["child-node","current-node"])):N("",!0)])}}}),zn={class:"config-header"},Bn=["placeholder"],$n={key:1,class:"node-name"},jn=pe({name:"DelayTimerNodeConfig",__name:"DelayTimerNodeConfig",props:{flowNode:{type:Object,required:!0}},setup(Q,{expose:le}){const X=Q,{settingVisible:U,closeDrawer:v,openDrawer:n}=nl(),d=Ce(X),{nodeName:x,showInput:I,clickIcon:h,blurEvent:R}=il(w.DELAY_TIMER_NODE),S=$(),k=tl({delayType:[{required:!0,message:"\u5EF6\u8FDF\u65F6\u95F4\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],timeDuration:[{required:!0,message:"\u5EF6\u8FDF\u65F6\u95F4\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],dateTime:[{required:!0,message:"\u5EF6\u8FDF\u65F6\u95F4\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}]}),r=$({delayType:Ve.FIXED_TIME_DURATION,timeDuration:1,timeUnit:Re.HOUR,dateTime:""}),s=async()=>{if(!S||!await S.value.validate())return!1;const p=f();return!!p&&(d.value.name=x.value,d.value.showText=p,r.value.delayType===Ve.FIXED_TIME_DURATION&&(d.value.delaySetting={delayType:r.value.delayType,delayTime:A()}),r.value.delayType===Ve.FIXED_DATE_TIME&&(d.value.delaySetting={delayType:r.value.delayType,delayTime:r.value.dateTime}),U.value=!1,!0)},f=()=>{let p="";return r.value.delayType===Ve.FIXED_TIME_DURATION&&(p=`\u5EF6\u8FDF${r.value.timeDuration}${Dl.find(_=>_.value===r.value.timeUnit).label}`),r.value.delayType===Ve.FIXED_DATE_TIME&&(p=`\u5EF6\u8FDF\u81F3${r.value.dateTime.replace("T"," ")}`),p},A=()=>{let p="PT";return r.value.timeUnit===Re.MINUTE&&(p+=r.value.timeDuration+"M"),r.value.timeUnit===Re.HOUR&&(p+=r.value.timeDuration+"H"),r.value.timeUnit===Re.DAY&&(p+=r.value.timeDuration+"D"),p};return le({openDrawer:n,showDelayTimerNodeConfig:p=>{if(x.value=p.name,p.delaySetting){if(r.value.delayType=p.delaySetting.delayType,r.value.delayType===Ve.FIXED_TIME_DURATION){const _=p.delaySetting.delayTime;let H=_.slice(2,_.length-1),D=_.slice(_.length-1);r.value.timeDuration=parseInt(H),r.value.timeUnit=Yl(D)}r.value.delayType===Ve.FIXED_DATE_TIME&&(r.value.dateTime=p.delaySetting.delayTime)}}}),(p,_)=>{const H=me,D=$l,z=ol,K=Xe,Y=Bl,C=Qe,Z=We,F=_l,T=rt,b=Ke,O=je,m=Le,G=Ye,W=ke("mountedFocus");return l(),c(G,{"append-to-body":!0,modelValue:e(U),"onUpdate:modelValue":_[7]||(_[7]=B=>ie(U)?U.value=B:null),"show-close":!1,size:550,"before-close":s},{header:a(()=>[o("div",zn,[e(I)?Ee((l(),i("input",{key:0,type:"text",class:"config-editable-input",onBlur:_[0]||(_[0]=B=>e(R)()),"onUpdate:modelValue":_[1]||(_[1]=B=>ie(x)?x.value=B:null),placeholder:e(x)},null,40,Bn)),[[W],[Ne,e(x)]]):(l(),i("div",$n,[P(M(e(x))+" ",1),t(H,{class:"ml-1",icon:"ep:edit-pen",size:16,onClick:_[2]||(_[2]=B=>e(h)())})])),_[8]||(_[8]=o("div",{class:"divide-line"},null,-1))])]),footer:a(()=>[t(O),o("div",null,[t(m,{type:"primary",onClick:s},{default:a(()=>_[11]||(_[11]=[P("\u786E \u5B9A")])),_:1}),t(m,{onClick:e(v)},{default:a(()=>_[12]||(_[12]=[P("\u53D6 \u6D88")])),_:1},8,["onClick"])])]),default:a(()=>[o("div",null,[t(b,{ref_key:"formRef",ref:S,model:e(r),"label-position":"top",rules:e(k)},{default:a(()=>[t(K,{label:"\u5EF6\u8FDF\u65F6\u95F4",prop:"delayType"},{default:a(()=>[t(z,{modelValue:e(r).delayType,"onUpdate:modelValue":_[3]||(_[3]=B=>e(r).delayType=B)},{default:a(()=>[(l(!0),i(L,null,j(e(at),B=>(l(),c(D,{key:B.value,label:B.label,value:B.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(r).delayType===e(Ve).FIXED_TIME_DURATION?(l(),c(K,{key:0},{default:a(()=>[t(K,{prop:"timeDuration"},{default:a(()=>[t(Y,{class:"mr-2",style:{width:"100px"},modelValue:e(r).timeDuration,"onUpdate:modelValue":_[4]||(_[4]=B=>e(r).timeDuration=B),min:1,"controls-position":"right"},null,8,["modelValue"])]),_:1}),t(Z,{modelValue:e(r).timeUnit,"onUpdate:modelValue":_[5]||(_[5]=B=>e(r).timeUnit=B),class:"mr-2",style:{width:"100px"}},{default:a(()=>[(l(!0),i(L,null,j(e(Dl),B=>(l(),c(C,{key:B.value,label:B.label,value:B.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),t(F,null,{default:a(()=>_[9]||(_[9]=[P("\u540E\u8FDB\u5165\u4E0B\u4E00\u8282\u70B9")])),_:1})]),_:1})):N("",!0),e(r).delayType===e(Ve).FIXED_DATE_TIME?(l(),c(K,{key:1,prop:"dateTime"},{default:a(()=>[t(T,{class:"mr-2",modelValue:e(r).dateTime,"onUpdate:modelValue":_[6]||(_[6]=B=>e(r).dateTime=B),type:"datetime",placeholder:"\u8BF7\u9009\u62E9\u65E5\u671F\u548C\u65F6\u95F4","value-format":"YYYY-MM-DDTHH:mm:ss"},null,8,["modelValue"]),t(F,null,{default:a(()=>_[10]||(_[10]=[P("\u540E\u8FDB\u5165\u4E0B\u4E00\u8282\u70B9")])),_:1})]),_:1})):N("",!0)]),_:1},8,["model","rules"])])]),_:1},8,["modelValue"])}}}),Kn={class:"node-wrapper"},Yn={class:"node-container"},Xn={class:"node-title-container"},Wn=["placeholder"],Qn=["title"],Jn={key:1,class:"node-text"},Zn={key:0,class:"node-toolbar"},ei={class:"toolbar-icon"},li=pe({name:"DelayTimerNode",__name:"DelayTimerNode",props:{flowNode:{type:Object,required:!0}},emits:["update:flowNode"],setup(Q,{emit:le}){const X=Q,U=le,v=ve("readonly"),n=Ce(X),{showInput:d,blurEvent:x,clickTitle:I}=dl(n,w.DELAY_TIMER_NODE),h=$(),R=()=>{v||(h.value.showDelayTimerNodeConfig(n.value),h.value.openDrawer())},S=()=>{U("update:flowNode",n.value.childNode)};return(k,r)=>{var A;const s=me,f=ke("mountedFocus");return l(),i("div",Kn,[o("div",Yn,[o("div",{class:Se(["node-box",[{"node-config-error":!e(n).showText},`${e(Ie)((A=e(n))==null?void 0:A.activityStatus)}`]])},[o("div",Xn,[r[4]||(r[4]=o("div",{class:"node-title-icon delay-node"},[o("span",{class:"iconfont icon-delay"})],-1)),!e(v)&&e(d)?Ee((l(),i("input",{key:0,type:"text",class:"editable-title-input",onBlur:r[0]||(r[0]=p=>e(x)()),"onUpdate:modelValue":r[1]||(r[1]=p=>e(n).name=p),placeholder:e(n).name},null,40,Wn)),[[f],[Ne,e(n).name]]):(l(),i("div",{key:1,class:"node-title",onClick:r[2]||(r[2]=(...p)=>e(I)&&e(I)(...p))},M(e(n).name),1))]),o("div",{class:"node-content",onClick:R},[e(n).showText?(l(),i("div",{key:0,class:"node-text",title:e(n).showText},M(e(n).showText),9,Qn)):(l(),i("div",Jn,M(e(Be).get(e(w).DELAY_TIMER_NODE)),1)),e(v)?N("",!0):(l(),c(s,{key:2,icon:"ep:arrow-right-bold"}))]),e(v)?N("",!0):(l(),i("div",Zn,[o("div",ei,[t(s,{color:"#0089ff",icon:"ep:circle-close-filled",size:18,onClick:S})])]))],2),e(n)?(l(),c(He,{key:0,"child-node":e(n).childNode,"onUpdate:childNode":r[3]||(r[3]=p=>e(n).childNode=p),"current-node":e(n)},null,8,["child-node","current-node"])):N("",!0)]),!e(v)&&e(n)?(l(),c(jn,{key:0,ref_key:"nodeSetting",ref:h,"flow-node":e(n)},null,8,["flow-node"])):N("",!0)])}}}),ti={class:"config-header"},ai=["placeholder"],oi={key:1,class:"node-name"},ni={class:"flex flex-items-center"},ii=pe({name:"RouterNodeConfig",__name:"RouterNodeConfig",props:{flowNode:{type:Object,required:!0}},setup(Q,{expose:le}){const X=Vl(),U=Q,v=ve("processNodeTree"),{settingVisible:n,closeDrawer:d,openDrawer:x}=nl(),I=Ce(U),{nodeName:h,showInput:R,clickIcon:S,blurEvent:k}=il(w.ROUTER_BRANCH_NODE),r=$([]),s=$([]),f=$([]),A=async()=>{let D=!0;for(const K of f.value)K&&!await K.validate()&&(D=!1);if(!D)return!1;const z=p();return!!z&&(I.value.name=h.value,I.value.showText=z,I.value.routerGroups=r.value,n.value=!1,!0)},p=()=>{if(!r.value||!Array.isArray(r.value)||r.value.length<=0)return X.warning("\u8BF7\u914D\u7F6E\u8DEF\u7531\uFF01"),"";for(const D of r.value){if(!D.nodeId||!D.conditionType||D.conditionType===_e.EXPRESSION&&!D.conditionExpression)return X.warning("\u8BF7\u5B8C\u5584\u8DEF\u7531\u914D\u7F6E\u9879\uFF01"),"";if(D.conditionType===_e.RULE){for(const z of D.conditionGroups.conditions)for(const K of z.rules)if(!K.leftSide||!K.rightSide)return X.warning("\u8BF7\u5B8C\u5584\u8DEF\u7531\u914D\u7F6E\u9879\uFF01"),""}}return`${r.value.length}\u6761\u8DEF\u7531\u5206\u652F`},_=()=>{r.value.push({nodeId:"",conditionType:_e.RULE,conditionExpression:"",conditionGroups:{and:!0,conditions:[{and:!0,rules:[{opCode:"==",leftSide:"",rightSide:""}]}]}})},H=D=>{for(;D&&(D.type!==w.ROUTER_BRANCH_NODE&&D.type!==w.CONDITION_NODE&&s.value.push({label:D.name,value:D.id}),D.childNode&&D.type!==w.END_EVENT_NODE);)D.conditionNodes&&D.conditionNodes.length&&D.conditionNodes.forEach(z=>{H(z)}),D=D.childNode};return le({openDrawer:x,showRouteNodeConfig:D=>{H(v==null?void 0:v.value),r.value=[],h.value=D.name,D.routerGroups&&(r.value=D.routerGroups)}}),(D,z)=>{const K=me,Y=_l,C=Qe,Z=We,F=Le,T=Ol,b=Ke,O=je,m=Ye,G=ke("mountedFocus");return l(),c(m,{"append-to-body":!0,modelValue:e(n),"onUpdate:modelValue":z[3]||(z[3]=W=>ie(n)?n.value=W:null),"show-close":!1,size:630,"before-close":A},{header:a(()=>[o("div",ti,[e(R)?Ee((l(),i("input",{key:0,type:"text",class:"config-editable-input",onBlur:z[0]||(z[0]=W=>e(k)()),"onUpdate:modelValue":z[1]||(z[1]=W=>ie(h)?h.value=W:null),placeholder:e(h)},null,40,ai)),[[G],[Ne,e(h)]]):(l(),i("div",oi,[P(M(e(h))+" ",1),t(K,{class:"ml-1",icon:"ep:edit-pen",size:16,onClick:z[2]||(z[2]=W=>e(S)())})])),z[4]||(z[4]=o("div",{class:"divide-line"},null,-1))])]),footer:a(()=>[t(O),o("div",null,[t(F,{type:"primary",onClick:A},{default:a(()=>z[7]||(z[7]=[P("\u786E \u5B9A")])),_:1}),t(F,{onClick:e(d)},{default:a(()=>z[8]||(z[8]=[P("\u53D6 \u6D88")])),_:1},8,["onClick"])])]),default:a(()=>[o("div",null,[t(b,{"label-position":"top"},{default:a(()=>[(l(!0),i(L,null,j(e(r),(W,B)=>(l(),c(T,{class:"mb-15px",key:B},{header:a(()=>[o("div",ni,[t(Y,{size:"large"},{default:a(()=>[P("\u8DEF\u7531"+M(B+1),1)]),_:2},1024),t(Z,{class:"ml-15px",modelValue:W.nodeId,"onUpdate:modelValue":ee=>W.nodeId=ee,style:{width:"180px"}},{default:a(()=>[(l(!0),i(L,null,j(e(s),ee=>(l(),c(C,{key:ee.value,label:ee.label,value:ee.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"]),t(F,{class:"mla",type:"danger",link:"",onClick:ee=>(J=>{r.value.splice(J,1)})(B)},{default:a(()=>z[5]||(z[5]=[P(" \u5220\u9664 ")])),_:2},1032,["onClick"])])]),default:a(()=>[t(ft,{ref_for:!0,ref:ee=>e(f)[B]=ee,modelValue:e(r)[B],"onUpdate:modelValue":ee=>e(r)[B]=ee},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024))),128))]),_:1}),t(F,{class:"w-1/1",type:"primary",icon:e(Mt),onClick:_},{default:a(()=>z[6]||(z[6]=[P(" \u65B0\u589E\u8DEF\u7531\u5206\u652F ")])),_:1},8,["icon"])])]),_:1},8,["modelValue"])}}}),di={class:"node-wrapper"},si={class:"node-container"},ri={class:"node-title-container"},ui=["placeholder"],ci=["title"],pi={key:1,class:"node-text"},mi={key:0,class:"node-toolbar"},vi={class:"toolbar-icon"},fi=pe({name:"RouterNode",__name:"RouterNode",props:{flowNode:{type:Object,required:!0}},emits:["update:flowNode"],setup(Q,{emit:le}){const X=Q,U=le,v=ve("readonly"),n=Ce(X),{showInput:d,blurEvent:x,clickTitle:I}=dl(n,w.ROUTER_BRANCH_NODE),h=$(),R=()=>{v||(h.value.showRouteNodeConfig(n.value),h.value.openDrawer())},S=()=>{U("update:flowNode",n.value.childNode)};return(k,r)=>{var A;const s=me,f=ke("mountedFocus");return l(),i("div",di,[o("div",si,[o("div",{class:Se(["node-box",[{"node-config-error":!e(n).showText},`${e(Ie)((A=e(n))==null?void 0:A.activityStatus)}`]])},[o("div",ri,[r[4]||(r[4]=o("div",{class:"node-title-icon router-node"},[o("span",{class:"iconfont icon-router"})],-1)),!e(v)&&e(d)?Ee((l(),i("input",{key:0,type:"text",class:"editable-title-input",onBlur:r[0]||(r[0]=p=>e(x)()),"onUpdate:modelValue":r[1]||(r[1]=p=>e(n).name=p),placeholder:e(n).name},null,40,ui)),[[f],[Ne,e(n).name]]):(l(),i("div",{key:1,class:"node-title",onClick:r[2]||(r[2]=(...p)=>e(I)&&e(I)(...p))},M(e(n).name),1))]),o("div",{class:"node-content",onClick:R},[e(n).showText?(l(),i("div",{key:0,class:"node-text",title:e(n).showText},M(e(n).showText),9,ci)):(l(),i("div",pi,M(e(Be).get(e(w).ROUTER_BRANCH_NODE)),1)),e(v)?N("",!0):(l(),c(s,{key:2,icon:"ep:arrow-right-bold"}))]),e(v)?N("",!0):(l(),i("div",mi,[o("div",vi,[t(s,{color:"#0089ff",icon:"ep:circle-close-filled",size:18,onClick:S})])]))],2),e(n)?(l(),c(He,{key:0,"child-node":e(n).childNode,"onUpdate:childNode":r[3]||(r[3]=p=>e(n).childNode=p),"current-node":e(n)},null,8,["child-node","current-node"])):N("",!0)]),!e(v)&&e(n)?(l(),c(ii,{key:0,ref_key:"nodeSetting",ref:h,"flow-node":e(n)},null,8,["flow-node"])):N("",!0)])}}}),yi={class:"h-410px"},gi={class:"condition-group-tool"},hi={class:"flex items-center"},_i={key:0,class:"condition-group-delete"},bi={class:"flex items-center justify-between"},Ei={class:"flex"},Ni={class:"mr-2"},Ti={class:"mr-2"},ki={class:"mr-2"},Si={key:0,class:"cursor-pointer mr-1 flex items-center"},Ui={class:"cursor-pointer flex items-center"},wi={title:"\u6DFB\u52A0\u6761\u4EF6\u7EC4",class:"mt-4 cursor-pointer"},gt=yl(pe({name:"ConditionDialog",__name:"ConditionDialog",emits:["updateCondition"],setup(Q,{expose:le,emit:X}){const U=$({conditionType:_e.RULE,conditionGroups:De(Me)}),v=X,n=Vl(),d=$(!1),x=ve("formType"),I=Ue(()=>lt.filter(s=>(x==null?void 0:x.value)!==vt.CUSTOM||s.value!==_e.RULE)),h=Al(),R=tl({conditionType:[{required:!0,message:"\u914D\u7F6E\u65B9\u5F0F\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],conditionExpression:[{required:!0,message:"\u6761\u4EF6\u8868\u8FBE\u5F0F\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),S=$(),k=()=>{U.value.conditionType===_e.RULE&&(U.value.conditionGroups||(U.value.conditionGroups=De(Me)))},r=async()=>{S&&(await S.value.validate()?(d.value=!1,v("updateCondition",U.value)):n.warning("\u8BF7\u5B8C\u5584\u6761\u4EF6\u89C4\u5219"))};return le({open:s=>{s&&(U.value.conditionType=s.conditionType,U.value.conditionExpression=s.conditionExpression,U.value.conditionGroups=s.conditionGroups),d.value=!0}}),(s,f)=>{const A=cl,p=ol,_=Xe,H=El,D=me,z=Qe,K=We,Y=pl,C=Ol,Z=st,F=Ke,T=Gt,b=Le,O=Xl;return l(),c(O,{modelValue:e(d),"onUpdate:modelValue":f[5]||(f[5]=m=>ie(d)?d.value=m:null),title:"\u6761\u4EF6\u914D\u7F6E",width:"600px",fullscreen:!1},{footer:a(()=>[t(b,{type:"primary",onClick:r},{default:a(()=>f[9]||(f[9]=[P("\u786E \u5B9A")])),_:1}),t(b,{onClick:f[4]||(f[4]=m=>d.value=!1)},{default:a(()=>f[10]||(f[10]=[P("\u53D6 \u6D88")])),_:1})]),default:a(()=>[o("div",yi,[t(T,{"wrap-class":"h-full"},{default:a(()=>[t(F,{ref_key:"formRef",ref:S,model:e(U),rules:e(R),"label-position":"top"},{default:a(()=>[t(_,{label:"\u914D\u7F6E\u65B9\u5F0F",prop:"conditionType"},{default:a(()=>[t(p,{modelValue:e(U).conditionType,"onUpdate:modelValue":f[0]||(f[0]=m=>e(U).conditionType=m),onChange:k},{default:a(()=>[(l(!0),i(L,null,j(e(I),(m,G)=>(l(),c(A,{key:G,value:m.value,label:m.value},{default:a(()=>[P(M(m.label),1)]),_:2},1032,["value","label"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(U).conditionType===e(_e).RULE&&e(U).conditionGroups?(l(),c(_,{key:0,label:"\u6761\u4EF6\u89C4\u5219"},{default:a(()=>[o("div",gi,[o("div",hi,[f[6]||(f[6]=o("div",{class:"mr-4"},"\u6761\u4EF6\u7EC4\u5173\u7CFB",-1)),t(H,{modelValue:e(U).conditionGroups.and,"onUpdate:modelValue":f[1]||(f[1]=m=>e(U).conditionGroups.and=m),"inline-prompt":"","active-text":"\u4E14","inactive-text":"\u6216"},null,8,["modelValue"])])]),t(Z,{direction:"vertical",spacer:e(U).conditionGroups.and?"\u4E14":"\u6216"},{default:a(()=>[(l(!0),i(L,null,j(e(U).conditionGroups.conditions,(m,G)=>(l(),c(C,{class:"condition-group",style:{width:"530px"},key:G},{header:a(()=>[o("div",bi,[f[8]||(f[8]=o("div",null,"\u6761\u4EF6\u7EC4",-1)),o("div",Ei,[f[7]||(f[7]=o("div",{class:"mr-4"},"\u89C4\u5219\u5173\u7CFB",-1)),t(H,{modelValue:m.and,"onUpdate:modelValue":W=>m.and=W,"inline-prompt":"","active-text":"\u4E14","inactive-text":"\u6216"},null,8,["modelValue","onUpdate:modelValue"])])])]),default:a(()=>[e(U).conditionGroups.conditions.length>1?(l(),i("div",_i,[t(D,{color:"#0089ff",icon:"ep:circle-close-filled",size:18,onClick:W=>{return B=e(U).conditionGroups.conditions,ee=G,void B.splice(ee,1);var B,ee}},null,8,["onClick"])])):N("",!0),(l(!0),i(L,null,j(m.rules,(W,B)=>(l(),i("div",{class:"flex pt-2",key:B},[o("div",Ni,[t(_,{prop:`conditionGroups.conditions.${G}.rules.${B}.leftSide`,rules:{required:!0,message:"\u5DE6\u503C\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}},{default:a(()=>[t(K,{style:{width:"160px"},modelValue:W.leftSide,"onUpdate:modelValue":ee=>W.leftSide=ee},{default:a(()=>[(l(!0),i(L,null,j(e(h),(ee,J)=>(l(),c(z,{key:J,label:ee.title,value:ee.field,disabled:!ee.required},null,8,["label","value","disabled"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"])]),o("div",Ti,[t(K,{modelValue:W.opCode,"onUpdate:modelValue":ee=>W.opCode=ee,style:{width:"100px"}},{default:a(()=>[(l(!0),i(L,null,j(e(tt),ee=>(l(),c(z,{key:ee.value,label:ee.label,value:ee.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),o("div",ki,[t(_,{prop:`conditionGroups.conditions.${G}.rules.${B}.rightSide`,rules:{required:!0,message:"\u53F3\u503C\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}},{default:a(()=>[t(Y,{modelValue:W.rightSide,"onUpdate:modelValue":ee=>W.rightSide=ee,style:{width:"160px"}},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"])]),m.rules.length>1?(l(),i("div",Si,[t(D,{icon:"ep:delete",size:18,onClick:ee=>{return J=B,void m.rules.splice(J,1);var J}},null,8,["onClick"])])):N("",!0),o("div",Ui,[t(D,{icon:"ep:plus",size:18,onClick:ee=>{return J=B,void m.rules.splice(J+1,0,{opCode:"==",leftSide:"",rightSide:""});var J}},null,8,["onClick"])])]))),128))]),_:2},1024))),128))]),_:1},8,["spacer"]),o("div",wi,[t(D,{color:"#0089ff",icon:"ep:plus",size:24,onClick:f[2]||(f[2]=m=>{var W;return G=(W=e(U).conditionGroups)==null?void 0:W.conditions,void G.push({and:!0,rules:[{opCode:"==",leftSide:"",rightSide:""}]});var G})})])]),_:1})):N("",!0),e(U).conditionType===e(_e).EXPRESSION?(l(),c(_,{key:1,label:"\u6761\u4EF6\u8868\u8FBE\u5F0F",prop:"conditionExpression"},{default:a(()=>[t(Y,{type:"textarea",modelValue:e(U).conditionExpression,"onUpdate:modelValue":f[3]||(f[3]=m=>e(U).conditionExpression=m),clearable:"",style:{width:"100%"}},null,8,["modelValue"])]),_:1})):N("",!0)]),_:1},8,["model","rules"])]),_:1})])]),_:1},8,["modelValue"])}}}),[["__scopeId","data-v-c9cdf00b"]]),Vi={class:"config-header"},Ci=["placeholder"],Ri={key:1,class:"node-name"},Di={key:0},xi={key:1},Ii={class:"flex items-center justify-between"},Oi={key:0,class:"cursor-pointer"},Ai={class:"mr-2 flex items-center"},Pi={class:"mx-2"},Fi={class:"mr-2"},Li={class:"mr-1 pt-1 cursor-pointer"},Hi={key:2},qi={class:"flex items-center justify-between"},Mi={key:0,class:"cursor-pointer"},Gi={class:"flex flex-wrap gap-2"},zi=pe({name:"TriggerNodeConfig",__name:"TriggerNodeConfig",props:{flowNode:{type:Object,required:!0}},setup(Q,{expose:le}){const{proxy:X}=Rl(),U=Q,v=Vl(),{settingVisible:n,closeDrawer:d,openDrawer:x}=nl(),I=Ce(U),{nodeName:h,showInput:R,clickIcon:S,blurEvent:k}=il(w.TRIGGER_NODE),r=$(),s=tl({type:[{required:!0,message:"\u89E6\u53D1\u5668\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],"httpRequestSetting.url":[{required:!0,message:"\u8BF7\u6C42\u5730\u5740\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),f=$({type:ye.HTTP_REQUEST,httpRequestSetting:{url:"",header:[],body:[],response:[]},formSettings:[{conditionGroups:De(Me),updateFormFields:{},deleteFields:[]}]}),A=Yt(),p=Ue(()=>A.map(O=>({title:O.title,field:O.field,disabled:!1})));let _;const H=()=>f.value.type===ye.HTTP_REQUEST?(f.value.httpRequestSetting=(_==null?void 0:_.type)===ye.HTTP_REQUEST&&_.httpRequestSetting?_.httpRequestSetting:{url:"",header:[],body:[],response:[]},void(f.value.formSettings=void 0)):f.value.type===ye.HTTP_CALLBACK?(f.value.httpRequestSetting=(_==null?void 0:_.type)===ye.HTTP_CALLBACK&&_.httpRequestSetting?_.httpRequestSetting:{url:"",header:[],body:[],response:[]},void(f.value.formSettings=void 0)):f.value.type===ye.FORM_UPDATE?(f.value.formSettings=(_==null?void 0:_.type)===ye.FORM_UPDATE&&_.formSettings?_.formSettings:[{conditionGroups:De(Me),updateFormFields:{},deleteFields:[]}],void(f.value.httpRequestSetting=void 0)):f.value.type===ye.FORM_DELETE?(f.value.formSettings=(_==null?void 0:_.type)===ye.FORM_DELETE&&_.formSettings?_.formSettings:[{conditionGroups:De(Me),updateFormFields:void 0,deleteFields:[]}],void(f.value.httpRequestSetting=void 0)):void 0,D=()=>{f.value.formSettings.push({conditionGroups:De(Me),updateFormFields:{},deleteFields:[]})},z=O=>{f.value.formSettings.splice(O,1)},K=(O,m)=>{X.$refs[`condition-${O}`][0].open(m)},Y=O=>{O.conditionType=void 0},C=(O,m)=>{X.$refs[`condition-${O}`][0].open(m)},Z=(O,m)=>{f.value.formSettings[O].conditionType=m.conditionType,f.value.formSettings[O].conditionExpression=m.conditionExpression,f.value.formSettings[O].conditionGroups=m.conditionGroups},F=O=>ct(O.conditionType,O.conditionExpression,O.conditionGroups,A),T=async()=>{var m,G;if(!r||!await r.value.validate())return!1;const O=b();return!!O&&(I.value.name=h.value,I.value.showText=O,f.value.type===ye.HTTP_REQUEST?f.value.formSettings=void 0:f.value.type===ye.FORM_UPDATE?(f.value.httpRequestSetting=void 0,(m=f.value.formSettings)==null||m.forEach(W=>{W.deleteFields=void 0})):f.value.type===ye.FORM_DELETE&&(f.value.httpRequestSetting=void 0,(G=f.value.formSettings)==null||G.forEach(W=>{W.updateFormFields=void 0})),I.value.triggerSetting=f.value,n.value=!1,!0)},b=()=>{var m;let O="";if(f.value.type===ye.HTTP_REQUEST||f.value.type===ye.HTTP_CALLBACK)O=`${(m=f.value.httpRequestSetting)==null?void 0:m.url}`;else if(f.value.type===ye.FORM_UPDATE){for(const[G,W]of f.value.formSettings.entries())if(!W.updateFormFields||Object.keys(W.updateFormFields).length===0)return v.warning(`\u8BF7\u6DFB\u52A0\u8868\u5355\u8BBE\u7F6E${G+1}\u7684\u4FEE\u6539\u5B57\u6BB5`),"";O="\u4FEE\u6539\u8868\u5355\u6570\u636E"}else if(f.value.type===ye.FORM_DELETE){for(const[G,W]of f.value.formSettings.entries())if(!W.deleteFields||W.deleteFields.length===0)return v.warning(`\u8BF7\u9009\u62E9\u8868\u5355\u8BBE\u7F6E${G+1}\u8981\u5220\u9664\u7684\u5B57\u6BB5`),"";O="\u5220\u9664\u8868\u5355\u6570\u636E"}return O};return le({openDrawer:x,showTriggerNodeConfig:O=>{h.value=O.name,_=O.triggerSetting?JSON.parse(JSON.stringify(O.triggerSetting)):{},O.triggerSetting&&(f.value={type:O.triggerSetting.type,httpRequestSetting:O.triggerSetting.httpRequestSetting||{url:"",header:[],body:[],response:[]},formSettings:O.triggerSetting.formSettings||[{conditionGroups:De(Me),updateFormFields:{},deleteFields:[]}]})}}),(O,m)=>{const G=me,W=Qe,B=We,ee=Xe,J=Le,q=zt,u=je,qe=pl,Ge=Ol,ge=Ke,Oe=Ye,Ae=ke("mountedFocus");return l(),c(Oe,{"append-to-body":!0,modelValue:e(n),"onUpdate:modelValue":m[5]||(m[5]=te=>ie(n)?n.value=te:null),"show-close":!1,size:630,"before-close":T},{header:a(()=>[o("div",Vi,[e(R)?Ee((l(),i("input",{key:0,type:"text",class:"config-editable-input",onBlur:m[0]||(m[0]=te=>e(k)()),"onUpdate:modelValue":m[1]||(m[1]=te=>ie(h)?h.value=te:null),placeholder:e(h)},null,40,Ci)),[[Ae],[Ne,e(h)]]):(l(),i("div",Ri,[P(M(e(h))+" ",1),t(G,{class:"ml-1",icon:"ep:edit-pen",size:16,onClick:m[2]||(m[2]=te=>e(S)())})])),m[6]||(m[6]=o("div",{class:"divide-line"},null,-1))])]),footer:a(()=>[t(u),o("div",null,[t(J,{type:"primary",onClick:T},{default:a(()=>m[15]||(m[15]=[P("\u786E \u5B9A")])),_:1}),t(J,{onClick:e(d)},{default:a(()=>m[16]||(m[16]=[P("\u53D6 \u6D88")])),_:1},8,["onClick"])])]),default:a(()=>[o("div",null,[t(ge,{ref_key:"formRef",ref:r,model:e(f),"label-position":"top",rules:e(s)},{default:a(()=>[t(ee,{label:"\u89E6\u53D1\u5668\u7C7B\u578B",prop:"type"},{default:a(()=>[t(B,{modelValue:e(f).type,"onUpdate:modelValue":m[3]||(m[3]=te=>e(f).type=te),onChange:H},{default:a(()=>[(l(!0),i(L,null,j(e(Ot),(te,ue)=>(l(),c(W,{key:ue,value:te.value,label:te.label},null,8,["value","label"]))),128))]),_:1},8,["modelValue"])]),_:1}),[e(ye).HTTP_REQUEST,e(ye).HTTP_CALLBACK].includes(e(f).type)&&e(f).httpRequestSetting?(l(),i("div",Di,[t(Jt,{setting:e(f).httpRequestSetting,"onUpdate:setting":m[4]||(m[4]=te=>e(f).httpRequestSetting=te),responseEnable:e(f).type===e(ye).HTTP_REQUEST,formItemPrefix:"httpRequestSetting"},null,8,["setting","responseEnable"])])):N("",!0),e(f).type===e(ye).FORM_UPDATE?(l(),i("div",xi,[(l(!0),i(L,null,j(e(f).formSettings,(te,ue)=>(l(),i("div",{key:ue},[t(Ge,{class:"w-580px mt-4"},{header:a(()=>[o("div",Ii,[o("div",null,"\u4FEE\u6539\u8868\u5355\u8BBE\u7F6E "+M(ue+1),1),e(f).formSettings.length>1?(l(),c(J,{key:0,type:"primary",plain:"",circle:"",onClick:ae=>z(ue)},{default:a(()=>[t(G,{icon:"ep:close"})]),_:2},1032,["onClick"])):N("",!0)])]),default:a(()=>[t(gt,{ref_for:!0,ref:`condition-${ue}`,onUpdateCondition:ae=>Z(ue,ae)},null,8,["onUpdateCondition"]),te.conditionType?(l(),i("div",Oi,[t(q,{type:"success",effect:"light",closable:"",onClose:ae=>Y(te),onClick:ae=>C(ue,te)},{default:a(()=>[P(M(F(te)),1)]),_:2},1032,["onClose","onClick"])])):(l(),c(J,{key:1,type:"primary",text:"",onClick:ae=>K(ue,te)},{default:a(()=>[t(G,{icon:"ep:link",class:"mr-5px"}),m[7]||(m[7]=P("\u6DFB\u52A0\u6761\u4EF6 "))]),_:2},1032,["onClick"])),t(u,{"content-position":"left"},{default:a(()=>m[8]||(m[8]=[P("\u4FEE\u6539\u8868\u5355\u5B57\u6BB5\u8BBE\u7F6E")])),_:1}),(l(!0),i(L,null,j(Object.keys(te.updateFormFields||{}),ae=>(l(),i("div",{class:"flex items-center",key:ae},[o("div",Ai,[t(ee,null,{default:a(()=>[t(B,{class:"w-160px!","model-value":ae,"onUpdate:modelValue":he=>((V,re,ne)=>{if(!(V!=null&&V.updateFormFields))return;const xe=V.updateFormFields[re];delete V.updateFormFields[re],V.updateFormFields[ne]=xe})(te,ae,he),placeholder:"\u8BF7\u9009\u62E9\u8868\u5355\u5B57\u6BB5",disabled:ae!==""},{default:a(()=>[(l(!0),i(L,null,j(e(p),(he,V)=>(l(),c(W,{key:V,label:he.title,value:he.field,disabled:he.disabled},null,8,["label","value","disabled"]))),128))]),_:2},1032,["model-value","onUpdate:modelValue","disabled"])]),_:2},1024)]),o("div",Pi,[t(ee,null,{default:a(()=>m[9]||(m[9]=[P("\u7684\u503C\u8BBE\u7F6E\u4E3A")])),_:1})]),o("div",Fi,[t(ee,{prop:`formSettings.${ue}.updateFormFields.${ae}`,rules:{required:!0,message:"\u503C\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}},{default:a(()=>[t(qe,{class:"w-160px",modelValue:te.updateFormFields[ae],"onUpdate:modelValue":he=>te.updateFormFields[ae]=he,placeholder:"\u8BF7\u8F93\u5165",disabled:!ae},null,8,["modelValue","onUpdate:modelValue","disabled"])]),_:2},1032,["prop"])]),o("div",Li,[t(ee,null,{default:a(()=>[t(G,{icon:"ep:delete",size:18,onClick:he=>((V,re)=>{V!=null&&V.updateFormFields&&delete V.updateFormFields[re]})(te,ae)},null,8,["onClick"])]),_:2},1024)])]))),128)),t(J,{type:"primary",text:"",onClick:ae=>(he=>{he&&(he.updateFormFields||(he.updateFormFields={}),he.updateFormFields[""]=void 0)})(te)},{default:a(()=>[t(G,{icon:"ep:memo",class:"mr-5px"}),m[10]||(m[10]=P("\u6DFB\u52A0\u4FEE\u6539\u5B57\u6BB5 "))]),_:2},1032,["onClick"])]),_:2},1024)]))),128)),t(J,{class:"mt-6",type:"primary",text:"",onClick:D},{default:a(()=>[t(G,{icon:"ep:setting",class:"mr-5px"}),m[11]||(m[11]=P("\u6DFB\u52A0\u8BBE\u7F6E "))]),_:1})])):N("",!0),e(f).type===e(ye).FORM_DELETE?(l(),i("div",Hi,[(l(!0),i(L,null,j(e(f).formSettings,(te,ue)=>(l(),i("div",{key:ue},[t(Ge,{class:"w-580px mt-4"},{header:a(()=>[o("div",qi,[o("div",null,"\u5220\u9664\u8868\u5355\u8BBE\u7F6E "+M(ue+1),1),e(f).formSettings.length>1?(l(),c(J,{key:0,type:"primary",plain:"",circle:"",onClick:ae=>z(ue)},{default:a(()=>[t(G,{icon:"ep:close"})]),_:2},1032,["onClick"])):N("",!0)])]),default:a(()=>[t(gt,{ref_for:!0,ref:`condition-${ue}`,onUpdateCondition:ae=>Z(ue,ae)},null,8,["onUpdateCondition"]),te.conditionType?(l(),i("div",Mi,[t(q,{type:"warning",effect:"light",closable:"",onClose:ae=>Y(te),onClick:ae=>C(ue,te)},{default:a(()=>[P(M(F(te)),1)]),_:2},1032,["onClose","onClick"])])):(l(),c(J,{key:1,type:"primary",text:"",onClick:ae=>K(ue,te)},{default:a(()=>[t(G,{icon:"ep:link",class:"mr-5px"}),m[12]||(m[12]=P("\u6DFB\u52A0\u6761\u4EF6 "))]),_:2},1032,["onClick"])),t(u,{"content-position":"left"},{default:a(()=>m[13]||(m[13]=[P("\u5220\u9664\u8868\u5355\u5B57\u6BB5\u8BBE\u7F6E")])),_:1}),o("div",Gi,[t(B,{modelValue:te.deleteFields,"onUpdate:modelValue":ae=>te.deleteFields=ae,multiple:"",placeholder:"\u8BF7\u9009\u62E9\u8981\u5220\u9664\u7684\u5B57\u6BB5",class:"w-full"},{default:a(()=>[(l(!0),i(L,null,j(e(A),ae=>(l(),c(W,{key:ae.field,label:ae.title,value:ae.field},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])])]),_:2},1024)]))),128)),t(J,{class:"mt-6",type:"primary",text:"",onClick:D},{default:a(()=>[t(G,{icon:"ep:setting",class:"mr-5px"}),m[14]||(m[14]=P("\u6DFB\u52A0\u8BBE\u7F6E "))]),_:1})])):N("",!0)]),_:1},8,["model","rules"])])]),_:1},8,["modelValue"])}}}),Bi={class:"node-wrapper"},$i={class:"node-container"},ji={class:"node-title-container"},Ki=["placeholder"],Yi=["title"],Xi={key:1,class:"node-text"},Wi={key:0,class:"node-toolbar"},Qi={class:"toolbar-icon"},Ji=pe({name:"TriggerNode",__name:"TriggerNode",props:{flowNode:{type:Object,required:!0}},emits:["update:flowNode"],setup(Q,{emit:le}){const X=Q,U=le,v=ve("readonly"),n=Ce(X),{showInput:d,blurEvent:x,clickTitle:I}=dl(n,w.TRIGGER_NODE),h=$(),R=()=>{v||(h.value.showTriggerNodeConfig(n.value),h.value.openDrawer())},S=()=>{U("update:flowNode",n.value.childNode)};return(k,r)=>{var A;const s=me,f=ke("mountedFocus");return l(),i("div",Bi,[o("div",$i,[o("div",{class:Se(["node-box",[{"node-config-error":!e(n).showText},`${e(Ie)((A=e(n))==null?void 0:A.activityStatus)}`]])},[o("div",ji,[r[4]||(r[4]=o("div",{class:"node-title-icon trigger-node"},[o("span",{class:"iconfont icon-trigger"})],-1)),!e(v)&&e(d)?Ee((l(),i("input",{key:0,type:"text",class:"editable-title-input",onBlur:r[0]||(r[0]=p=>e(x)()),"onUpdate:modelValue":r[1]||(r[1]=p=>e(n).name=p),placeholder:e(n).name},null,40,Ki)),[[f],[Ne,e(n).name]]):(l(),i("div",{key:1,class:"node-title",onClick:r[2]||(r[2]=(...p)=>e(I)&&e(I)(...p))},M(e(n).name),1))]),o("div",{class:"node-content",onClick:R},[e(n).showText?(l(),i("div",{key:0,class:"node-text",title:e(n).showText},M(e(n).showText),9,Yi)):(l(),i("div",Xi,M(e(Be).get(e(w).TRIGGER_NODE)),1)),e(v)?N("",!0):(l(),c(s,{key:2,icon:"ep:arrow-right-bold"}))]),e(v)?N("",!0):(l(),i("div",Wi,[o("div",Qi,[t(s,{color:"#0089ff",icon:"ep:circle-close-filled",size:18,onClick:S})])]))],2),e(n)?(l(),c(He,{key:0,"child-node":e(n).childNode,"onUpdate:childNode":r[3]||(r[3]=p=>e(n).childNode=p),"current-node":e(n)},null,8,["child-node","current-node"])):N("",!0)]),!e(v)&&e(n)?(l(),c(zi,{key:0,ref_key:"nodeSetting",ref:h,"flow-node":e(n)},null,8,["flow-node"])):N("",!0)])}}}),Zi={class:"config-header"},ed=["placeholder"],ld={key:1,class:"node-name"},td={class:"mr-2"},ad={class:"mr-2"},od={class:"mr-1 flex items-center"},nd={class:"mr-2"},id={class:"mr-2"},dd={class:"mr-1 flex items-center"},sd={key:3},rd={key:4},ud=pe({name:"ChildProcessNodeConfig",__name:"ChildProcessNodeConfig",props:{flowNode:{type:Object,required:!0}},setup(Q,{expose:le}){const X=Q,{settingVisible:U,closeDrawer:v,openDrawer:n}=nl(),d=Ce(X),{nodeName:x,showInput:I,clickIcon:h,blurEvent:R}=il(w.CHILD_PROCESS_NODE),S=$("child"),k=$(),r=tl({async:[{required:!0,message:"\u662F\u5426\u5F02\u6B65\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],calledProcessDefinitionKey:[{required:!0,message:"\u5B50\u6D41\u7A0B\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],skipStartUserNode:[{required:!0,message:"\u662F\u5426\u81EA\u52A8\u8DF3\u8FC7\u5B50\u6D41\u7A0B\u53D1\u8D77\u8282\u70B9\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],startUserType:[{required:!0,message:"\u5B50\u6D41\u7A0B\u53D1\u8D77\u4EBA\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],startUserEmptyType:[{required:!0,message:"\u5F53\u5B50\u6D41\u7A0B\u53D1\u8D77\u4EBA\u4E3A\u7A7A\u65F6\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],startUserFormField:[{required:!0,message:"\u53D1\u8D77\u4EBA\u8868\u5355\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],timeoutEnable:[{required:!0,message:"\u8D85\u65F6\u8BBE\u7F6E\u662F\u5426\u5F00\u542F\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],timeoutType:[{required:!0,message:"\u8D85\u65F6\u8BBE\u7F6E\u65F6\u95F4\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],timeDuration:[{required:!0,message:"\u8D85\u65F6\u8BBE\u7F6E\u65F6\u95F4\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],dateTime:[{required:!0,message:"\u8D85\u65F6\u8BBE\u7F6E\u65F6\u95F4\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],multiInstanceEnable:[{required:!0,message:"\u591A\u5B9E\u4F8B\u8BBE\u7F6E\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}]}),s=$({async:!1,calledProcessDefinitionKey:"",skipStartUserNode:!1,inVariables:[],outVariables:[],startUserType:nt.MAIN_PROCESS_START_USER,startUserEmptyType:ot.MAIN_PROCESS_START_USER,startUserFormField:"",timeoutEnable:!1,timeoutType:Ve.FIXED_TIME_DURATION,timeDuration:1,timeUnit:Re.HOUR,dateTime:"",multiInstanceEnable:!1,sequential:!1,approveRatio:100,multiInstanceSourceType:hl.FIXED_QUANTITY,multiInstanceSource:""}),f=$(),A=Al(),p=Ue(()=>A.filter(T=>T.type==="inputNumber")),_=Ue(()=>A.filter(T=>T.type==="select"||T.type==="checkbox")),H=$(),D=async()=>{if(S.value="child",!k||!await k.value.validate())return!1;const T=f.value.find(b=>b.key===s.value.calledProcessDefinitionKey);return d.value.name=x.value,d.value.childProcessSetting&&(d.value.childProcessSetting.async=s.value.async,d.value.childProcessSetting.calledProcessDefinitionKey=T.key,d.value.childProcessSetting.calledProcessDefinitionName=T.name,d.value.childProcessSetting.skipStartUserNode=s.value.skipStartUserNode,d.value.childProcessSetting.inVariables=s.value.inVariables,d.value.childProcessSetting.outVariables=s.value.outVariables,d.value.childProcessSetting.startUserSetting.type=s.value.startUserType,d.value.childProcessSetting.startUserSetting.emptyType=s.value.startUserEmptyType,d.value.childProcessSetting.startUserSetting.formField=s.value.startUserFormField,d.value.childProcessSetting.timeoutSetting={enable:s.value.timeoutEnable},s.value.timeoutEnable&&(d.value.childProcessSetting.timeoutSetting.type=s.value.timeoutType,s.value.timeoutType===Ve.FIXED_TIME_DURATION&&(d.value.childProcessSetting.timeoutSetting.timeExpression=Z()),s.value.timeoutType===Ve.FIXED_DATE_TIME&&(d.value.childProcessSetting.timeoutSetting.timeExpression=s.value.dateTime)),d.value.childProcessSetting.multiInstanceSetting={enable:s.value.multiInstanceEnable},s.value.multiInstanceEnable&&(d.value.childProcessSetting.multiInstanceSetting.sequential=s.value.sequential,d.value.childProcessSetting.multiInstanceSetting.approveRatio=s.value.approveRatio,d.value.childProcessSetting.multiInstanceSetting.sourceType=s.value.multiInstanceSourceType,d.value.childProcessSetting.multiInstanceSetting.source=s.value.multiInstanceSource)),d.value.showText=`\u8C03\u7528\u5B50\u6D41\u7A0B\uFF1A${T.name}`,U.value=!1,!0};le({openDrawer:n,showChildProcessNodeConfig:T=>{var b;if(x.value=T.name,T.childProcessSetting){if(s.value.async=T.childProcessSetting.async,s.value.calledProcessDefinitionKey=(b=T.childProcessSetting)==null?void 0:b.calledProcessDefinitionKey,s.value.skipStartUserNode=T.childProcessSetting.skipStartUserNode,s.value.inVariables=T.childProcessSetting.inVariables,s.value.outVariables=T.childProcessSetting.outVariables,s.value.startUserType=T.childProcessSetting.startUserSetting.type,s.value.startUserEmptyType=T.childProcessSetting.startUserSetting.emptyType??ot.MAIN_PROCESS_START_USER,s.value.startUserFormField=T.childProcessSetting.startUserSetting.formField??"",s.value.timeoutEnable=T.childProcessSetting.timeoutSetting.enable??!1,s.value.timeoutEnable){if(s.value.timeoutType=T.childProcessSetting.timeoutSetting.type??Ve.FIXED_TIME_DURATION,s.value.timeoutType===Ve.FIXED_TIME_DURATION){const O=T.childProcessSetting.timeoutSetting.timeExpression??"";let m=O.slice(2,O.length-1),G=O.slice(O.length-1);s.value.timeDuration=parseInt(m),s.value.timeUnit=Yl(G)}s.value.timeoutType===Ve.FIXED_DATE_TIME&&(s.value.dateTime=T.childProcessSetting.timeoutSetting.timeExpression??"")}s.value.multiInstanceEnable=T.childProcessSetting.multiInstanceSetting.enable??!1,s.value.multiInstanceEnable&&(s.value.sequential=T.childProcessSetting.multiInstanceSetting.sequential??!1,s.value.approveRatio=T.childProcessSetting.multiInstanceSetting.approveRatio??100,s.value.multiInstanceSourceType=T.childProcessSetting.multiInstanceSetting.sourceType??hl.FIXED_QUANTITY,s.value.multiInstanceSource=T.childProcessSetting.multiInstanceSetting.source??"")}C()}});const z=T=>{T==null||T.push({source:"",target:""})},K=(T,b)=>{b==null||b.splice(T,1)},Y=()=>{s.value.inVariables=[],s.value.outVariables=[],C()},C=async()=>{const T=f.value.find(O=>O.key===s.value.calledProcessDefinitionKey),b=await ea(T.formId);H.value=[],b.fields&&b.fields.forEach(O=>{la(JSON.parse(O),H.value)})},Z=()=>{let T="PT";return s.value.timeUnit===Re.MINUTE&&(T+=s.value.timeDuration+"M"),s.value.timeUnit===Re.HOUR&&(T+=s.value.timeDuration+"H"),s.value.timeUnit===Re.DAY&&(T+=s.value.timeDuration+"D"),T},F=()=>{s.value.multiInstanceSource=""};return Wl(async()=>{f.value=await Zt(void 0)}),(T,b)=>{const O=me,m=El,G=Xe,W=Qe,B=We,ee=Le,J=cl,q=ol,u=je,qe=$l,Ge=Bl,ge=_l,Oe=rt,Ae=Ke,te=Il,ue=xl,ae=Ye,he=ke("mountedFocus");return l(),c(ae,{"append-to-body":!0,modelValue:e(U),"onUpdate:modelValue":b[24]||(b[24]=V=>ie(U)?U.value=V:null),"show-close":!1,size:550,"before-close":D},{header:a(()=>[o("div",Zi,[e(I)?Ee((l(),i("input",{key:0,type:"text",class:"config-editable-input",onBlur:b[0]||(b[0]=V=>e(R)()),"onUpdate:modelValue":b[1]||(b[1]=V=>ie(x)?x.value=V:null),placeholder:e(x)},null,40,ed)),[[he],[Ne,e(x)]]):(l(),i("div",ld,[P(M(e(x))+" ",1),t(O,{class:"ml-1",icon:"ep:edit-pen",size:16,onClick:b[2]||(b[2]=V=>e(h)())})])),b[25]||(b[25]=o("div",{class:"divide-line"},null,-1))])]),footer:a(()=>[t(u),o("div",null,[t(ee,{type:"primary",onClick:D},{default:a(()=>b[34]||(b[34]=[P("\u786E \u5B9A")])),_:1}),t(ee,{onClick:e(v)},{default:a(()=>b[35]||(b[35]=[P("\u53D6 \u6D88")])),_:1},8,["onClick"])])]),default:a(()=>[t(ue,{type:"border-card",modelValue:e(S),"onUpdate:modelValue":b[23]||(b[23]=V=>ie(S)?S.value=V:null)},{default:a(()=>[t(te,{label:"\u5B50\u6D41\u7A0B",name:"child"},{default:a(()=>[o("div",null,[t(Ae,{ref_key:"formRef",ref:k,model:e(s),"label-position":"top",rules:e(r)},{default:a(()=>[t(G,{label:"\u662F\u5426\u5F02\u6B65",prop:"async"},{default:a(()=>[t(m,{modelValue:e(s).async,"onUpdate:modelValue":b[3]||(b[3]=V=>e(s).async=V),"active-text":"\u5F02\u6B65","inactive-text":"\u4E0D\u5F02\u6B65"},null,8,["modelValue"])]),_:1}),t(G,{label:"\u9009\u62E9\u5B50\u6D41\u7A0B",prop:"calledProcessDefinitionKey"},{default:a(()=>[t(B,{modelValue:e(s).calledProcessDefinitionKey,"onUpdate:modelValue":b[4]||(b[4]=V=>e(s).calledProcessDefinitionKey=V),clearable:"",onChange:Y},{default:a(()=>[(l(!0),i(L,null,j(e(f),(V,re)=>(l(),c(W,{key:re,label:V.name,value:V.key},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(G,{label:"\u662F\u5426\u81EA\u52A8\u8DF3\u8FC7\u5B50\u6D41\u7A0B\u53D1\u8D77\u8282\u70B9",prop:"skipStartUserNode"},{default:a(()=>[t(m,{modelValue:e(s).skipStartUserNode,"onUpdate:modelValue":b[5]||(b[5]=V=>e(s).skipStartUserNode=V),"active-text":"\u8DF3\u8FC7","inactive-text":"\u4E0D\u8DF3\u8FC7"},null,8,["modelValue"])]),_:1}),t(G,{label:"\u4E3B\u2192\u5B50\u53D8\u91CF\u4F20\u9012",prop:"inVariables"},{default:a(()=>[(l(!0),i(L,null,j(e(s).inVariables,(V,re)=>(l(),i("div",{class:"flex pt-2",key:re},[o("div",td,[t(G,{prop:`inVariables.${re}.source`,rules:{required:!0,message:"\u53D8\u91CF\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}},{default:a(()=>[t(B,{class:"w-200px!",modelValue:V.source,"onUpdate:modelValue":ne=>V.source=ne},{default:a(()=>[(l(!0),i(L,null,j(e(A),(ne,xe)=>(l(),c(W,{key:xe,label:ne.title,value:ne.field},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"])]),o("div",ad,[t(G,{prop:`inVariables.${re}.target`,rules:{required:!0,message:"\u53D8\u91CF\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}},{default:a(()=>[t(B,{class:"w-200px!",modelValue:V.target,"onUpdate:modelValue":ne=>V.target=ne},{default:a(()=>[(l(!0),i(L,null,j(e(H),(ne,xe)=>(l(),c(W,{key:xe,label:ne.title,value:ne.field},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"])]),o("div",od,[t(O,{icon:"ep:delete",size:18,onClick:ne=>K(re,e(s).inVariables)},null,8,["onClick"])])]))),128)),t(ee,{type:"primary",text:"",onClick:b[6]||(b[6]=V=>z(e(s).inVariables))},{default:a(()=>[t(O,{icon:"ep:plus",class:"mr-5px"}),b[26]||(b[26]=P("\u6DFB\u52A0\u4E00\u884C "))]),_:1})]),_:1}),e(s).async===!1?(l(),c(G,{key:0,label:"\u5B50\u2192\u4E3B\u53D8\u91CF\u4F20\u9012",prop:"outVariables"},{default:a(()=>[(l(!0),i(L,null,j(e(s).outVariables,(V,re)=>(l(),i("div",{class:"flex pt-2",key:re},[o("div",nd,[t(G,{prop:`outVariables.${re}.source`,rules:{required:!0,message:"\u53D8\u91CF\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}},{default:a(()=>[t(B,{class:"w-200px!",modelValue:V.source,"onUpdate:modelValue":ne=>V.source=ne},{default:a(()=>[(l(!0),i(L,null,j(e(H),(ne,xe)=>(l(),c(W,{key:xe,label:ne.title,value:ne.field},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"])]),o("div",id,[t(G,{prop:`outVariables.${re}.target`,rules:{required:!0,message:"\u53D8\u91CF\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}},{default:a(()=>[t(B,{class:"w-200px!",modelValue:V.target,"onUpdate:modelValue":ne=>V.target=ne},{default:a(()=>[(l(!0),i(L,null,j(e(A),(ne,xe)=>(l(),c(W,{key:xe,label:ne.title,value:ne.field},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"])]),o("div",dd,[t(O,{icon:"ep:delete",size:18,onClick:ne=>K(re,e(s).outVariables)},null,8,["onClick"])])]))),128)),t(ee,{type:"primary",text:"",onClick:b[7]||(b[7]=V=>z(e(s).outVariables))},{default:a(()=>[t(O,{icon:"ep:plus",class:"mr-5px"}),b[27]||(b[27]=P("\u6DFB\u52A0\u4E00\u884C "))]),_:1})]),_:1})):N("",!0),t(G,{label:"\u5B50\u6D41\u7A0B\u53D1\u8D77\u4EBA",prop:"startUserType"},{default:a(()=>[t(q,{modelValue:e(s).startUserType,"onUpdate:modelValue":b[8]||(b[8]=V=>e(s).startUserType=V)},{default:a(()=>[(l(!0),i(L,null,j(e(At),V=>(l(),c(J,{key:V.value,value:V.value},{default:a(()=>[P(M(V.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(s).startUserType===e(nt).FROM_FORM?(l(),c(G,{key:1,label:"\u5F53\u5B50\u6D41\u7A0B\u53D1\u8D77\u4EBA\u4E3A\u7A7A\u65F6",prop:"startUserType"},{default:a(()=>[t(q,{modelValue:e(s).startUserEmptyType,"onUpdate:modelValue":b[9]||(b[9]=V=>e(s).startUserEmptyType=V)},{default:a(()=>[(l(!0),i(L,null,j(e(Pt),V=>(l(),c(J,{key:V.value,value:V.value},{default:a(()=>[P(M(V.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1})):N("",!0),e(s).startUserType===2?(l(),c(G,{key:2,label:"\u53D1\u8D77\u4EBA\u8868\u5355",prop:"startUserFormField"},{default:a(()=>[t(B,{class:"w-200px!",modelValue:e(s).startUserFormField,"onUpdate:modelValue":b[10]||(b[10]=V=>e(s).startUserFormField=V)},{default:a(()=>[(l(!0),i(L,null,j(e(A),(V,re)=>(l(),c(W,{key:re,label:V.title,value:V.field},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):N("",!0),t(u,{"content-position":"left"},{default:a(()=>b[28]||(b[28]=[P("\u8D85\u65F6\u8BBE\u7F6E")])),_:1}),t(G,{label:"\u542F\u7528\u5F00\u5173",prop:"timeoutEnable"},{default:a(()=>[t(m,{modelValue:e(s).timeoutEnable,"onUpdate:modelValue":b[11]||(b[11]=V=>e(s).timeoutEnable=V),"active-text":"\u5F00\u542F","inactive-text":"\u5173\u95ED"},null,8,["modelValue"])]),_:1}),e(s).timeoutEnable?(l(),i("div",sd,[t(G,{prop:"timeoutType"},{default:a(()=>[t(q,{modelValue:e(s).timeoutType,"onUpdate:modelValue":b[12]||(b[12]=V=>e(s).timeoutType=V)},{default:a(()=>[(l(!0),i(L,null,j(e(at),V=>(l(),c(qe,{key:V.value,label:V.label,value:V.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(s).timeoutType===e(Ve).FIXED_TIME_DURATION?(l(),c(G,{key:0},{default:a(()=>[t(G,{prop:"timeDuration"},{default:a(()=>[t(Ge,{class:"mr-2",style:{width:"100px"},modelValue:e(s).timeDuration,"onUpdate:modelValue":b[13]||(b[13]=V=>e(s).timeDuration=V),min:1,"controls-position":"right"},null,8,["modelValue"])]),_:1}),t(B,{modelValue:e(s).timeUnit,"onUpdate:modelValue":b[14]||(b[14]=V=>e(s).timeUnit=V),class:"mr-2",style:{width:"100px"}},{default:a(()=>[(l(!0),i(L,null,j(e(Dl),V=>(l(),c(W,{key:V.value,label:V.label,value:V.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),t(ge,null,{default:a(()=>b[29]||(b[29]=[P("\u540E\u8FDB\u5165\u4E0B\u4E00\u8282\u70B9")])),_:1})]),_:1})):N("",!0),e(s).timeoutType===e(Ve).FIXED_DATE_TIME?(l(),c(G,{key:1,prop:"dateTime"},{default:a(()=>[t(Oe,{class:"mr-2",modelValue:e(s).dateTime,"onUpdate:modelValue":b[15]||(b[15]=V=>e(s).dateTime=V),type:"datetime",placeholder:"\u8BF7\u9009\u62E9\u65E5\u671F\u548C\u65F6\u95F4","value-format":"YYYY-MM-DDTHH:mm:ss"},null,8,["modelValue"]),t(ge,null,{default:a(()=>b[30]||(b[30]=[P("\u540E\u8FDB\u5165\u4E0B\u4E00\u8282\u70B9")])),_:1})]),_:1})):N("",!0)])):N("",!0),t(u,{"content-position":"left"},{default:a(()=>b[31]||(b[31]=[P("\u591A\u5B9E\u4F8B\u8BBE\u7F6E")])),_:1}),t(G,{label:"\u542F\u7528\u5F00\u5173",prop:"multiInstanceEnable"},{default:a(()=>[t(m,{modelValue:e(s).multiInstanceEnable,"onUpdate:modelValue":b[16]||(b[16]=V=>e(s).multiInstanceEnable=V),"active-text":"\u5F00\u542F","inactive-text":"\u5173\u95ED"},null,8,["modelValue"])]),_:1}),e(s).multiInstanceEnable?(l(),i("div",rd,[t(G,{prop:"sequential"},{default:a(()=>[t(m,{modelValue:e(s).sequential,"onUpdate:modelValue":b[17]||(b[17]=V=>e(s).sequential=V),"active-text":"\u4E32\u884C","inactive-text":"\u5E76\u884C"},null,8,["modelValue"])]),_:1}),t(G,{prop:"approveRatio"},{default:a(()=>[t(ge,null,{default:a(()=>b[32]||(b[32]=[P("\u5B8C\u6210\u6BD4\u4F8B(%)")])),_:1}),t(Ge,{class:"ml-10px",modelValue:e(s).approveRatio,"onUpdate:modelValue":b[18]||(b[18]=V=>e(s).approveRatio=V),min:10,max:100,step:10},null,8,["modelValue"])]),_:1}),t(G,{prop:"multiInstanceSourceType"},{default:a(()=>[t(ge,null,{default:a(()=>b[33]||(b[33]=[P("\u591A\u5B9E\u4F8B\u6765\u6E90")])),_:1}),t(B,{class:"ml-10px w-200px!",modelValue:e(s).multiInstanceSourceType,"onUpdate:modelValue":b[19]||(b[19]=V=>e(s).multiInstanceSourceType=V),onChange:F},{default:a(()=>[(l(!0),i(L,null,j(e(Ft),V=>(l(),c(W,{key:V.value,label:V.label,value:V.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(s).multiInstanceSourceType===e(hl).FIXED_QUANTITY?(l(),c(G,{key:0},{default:a(()=>[t(Ge,{modelValue:e(s).multiInstanceSource,"onUpdate:modelValue":b[20]||(b[20]=V=>e(s).multiInstanceSource=V),min:1},null,8,["modelValue"])]),_:1})):N("",!0),e(s).multiInstanceSourceType===e(hl).NUMBER_FORM?(l(),c(G,{key:1},{default:a(()=>[t(B,{class:"w-200px!",modelValue:e(s).multiInstanceSource,"onUpdate:modelValue":b[21]||(b[21]=V=>e(s).multiInstanceSource=V)},{default:a(()=>[(l(!0),i(L,null,j(e(p),(V,re)=>(l(),c(W,{key:re,label:V.title,value:V.field},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):N("",!0),e(s).multiInstanceSourceType===e(hl).MULTIPLE_FORM?(l(),c(G,{key:2},{default:a(()=>[t(B,{class:"w-200px!",modelValue:e(s).multiInstanceSource,"onUpdate:modelValue":b[22]||(b[22]=V=>e(s).multiInstanceSource=V)},{default:a(()=>[(l(!0),i(L,null,j(e(_),(V,re)=>(l(),c(W,{key:re,label:V.title,value:V.field},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):N("",!0)])):N("",!0)]),_:1},8,["model","rules"])])]),_:1})]),_:1},8,["modelValue"])]),_:1},8,["modelValue"])}}}),cd={class:"node-wrapper"},pd={class:"node-container"},md={class:"node-title-container"},vd=["placeholder"],fd=["title"],yd={key:1,class:"node-text"},gd={key:0,class:"node-toolbar"},hd={class:"toolbar-icon"},_d=pe({name:"ChildProcessNode",__name:"ChildProcessNode",props:{flowNode:{type:Object,required:!0}},emits:["update:flowNode"],setup(Q,{emit:le}){const X=Q,U=le,v=ve("readonly"),n=Ce(X),{showInput:d,blurEvent:x,clickTitle:I}=dl(n,w.CHILD_PROCESS_NODE),h=$(),R=()=>{v||(h.value.showChildProcessNodeConfig(n.value),h.value.openDrawer())},S=()=>{U("update:flowNode",n.value.childNode)};return(k,r)=>{var A,p,_;const s=me,f=ke("mountedFocus");return l(),i("div",cd,[o("div",pd,[o("div",{class:Se(["node-box",[{"node-config-error":!e(n).showText},`${e(Ie)((A=e(n))==null?void 0:A.activityStatus)}`]])},[o("div",md,[o("div",{class:Se("node-title-icon "+(((p=e(n).childProcessSetting)==null?void 0:p.async)===!0?"async-child-process":"child-process"))},[o("span",{class:Se("iconfont "+(((_=e(n).childProcessSetting)==null?void 0:_.async)===!0?"icon-async-child-process":"icon-child-process"))},null,2)],2),!e(v)&&e(d)?Ee((l(),i("input",{key:0,type:"text",class:"editable-title-input",onBlur:r[0]||(r[0]=H=>e(x)()),"onUpdate:modelValue":r[1]||(r[1]=H=>e(n).name=H),placeholder:e(n).name},null,40,vd)),[[f],[Ne,e(n).name]]):(l(),i("div",{key:1,class:"node-title",onClick:r[2]||(r[2]=(...H)=>e(I)&&e(I)(...H))},M(e(n).name),1))]),o("div",{class:"node-content",onClick:R},[e(n).showText?(l(),i("div",{key:0,class:"node-text",title:e(n).showText},M(e(n).showText),9,fd)):(l(),i("div",yd,M(e(Be).get(e(w).CHILD_PROCESS_NODE)),1)),e(v)?N("",!0):(l(),c(s,{key:2,icon:"ep:arrow-right-bold"}))]),e(v)?N("",!0):(l(),i("div",gd,[o("div",hd,[t(s,{color:"#0089ff",icon:"ep:circle-close-filled",size:18,onClick:S})])]))],2),e(n)?(l(),c(He,{key:0,"child-node":e(n).childNode,"onUpdate:childNode":r[3]||(r[3]=H=>e(n).childNode=H),"current-node":e(n)},null,8,["child-node","current-node"])):N("",!0)]),!e(v)&&e(n)?(l(),c(ud,{key:0,ref_key:"nodeSetting",ref:h,"flow-node":e(n)},null,8,["flow-node"])):N("",!0)])}}}),Pl=pe({name:"ProcessNodeTree",__name:"ProcessNodeTree",props:{parentNode:{type:Object,default:()=>null},flowNode:{type:Object,default:()=>null}},emits:["update:flowNode","find:recursiveFindParentNode"],setup(Q,{emit:le}){const X=Q,U=le,v=Ce(X),n=I=>{U("update:flowNode",I)},d=(I,h)=>{U("find:recursiveFindParentNode",I,X.parentNode,h)},x=(I,h,R)=>{h&&(h.type!==w.START_USER_NODE?(h.type===R&&I.push(h),U("find:recursiveFindParentNode",I,X.parentNode,R)):I.push(h))};return(I,h)=>{const R=bt("ProcessNodeTree",!0);return l(),i(L,null,[e(v)&&e(v).type===e(w).START_USER_NODE?(l(),c(Ua,{key:0,"flow-node":e(v)},null,8,["flow-node"])):N("",!0),!e(v)||e(v).type!==e(w).USER_TASK_NODE&&e(v).type!==e(w).TRANSACTOR_NODE?N("",!0):(l(),c(po,{key:1,"flow-node":e(v),"onUpdate:flowNode":n,"onFind:parentNode":d},null,8,["flow-node"])),e(v)&&e(v).type===e(w).COPY_TASK_NODE?(l(),c(xo,{key:2,"flow-node":e(v),"onUpdate:flowNode":n},null,8,["flow-node"])):N("",!0),e(v)&&e(v).type===e(w).CONDITION_BRANCH_NODE?(l(),c(pn,{key:3,"flow-node":e(v),"onUpdate:modelValue":n,"onFind:parentNode":d},null,8,["flow-node"])):N("",!0),e(v)&&e(v).type===e(w).PARALLEL_BRANCH_NODE?(l(),c(Un,{key:4,"flow-node":e(v),"onUpdate:modelValue":n,"onFind:parentNode":d},null,8,["flow-node"])):N("",!0),e(v)&&e(v).type===e(w).INCLUSIVE_BRANCH_NODE?(l(),c(Gn,{key:5,"flow-node":e(v),"onUpdate:modelValue":n,"onFind:parentNode":d},null,8,["flow-node"])):N("",!0),e(v)&&e(v).type===e(w).DELAY_TIMER_NODE?(l(),c(li,{key:6,"flow-node":e(v),"onUpdate:flowNode":n},null,8,["flow-node"])):N("",!0),e(v)&&e(v).type===e(w).ROUTER_BRANCH_NODE?(l(),c(fi,{key:7,"flow-node":e(v),"onUpdate:flowNode":n},null,8,["flow-node"])):N("",!0),e(v)&&e(v).type===e(w).TRIGGER_NODE?(l(),c(Ji,{key:8,"flow-node":e(v),"onUpdate:flowNode":n},null,8,["flow-node"])):N("",!0),e(v)&&e(v).type===e(w).CHILD_PROCESS_NODE?(l(),c(_d,{key:9,"flow-node":e(v),"onUpdate:flowNode":n},null,8,["flow-node"])):N("",!0),e(v)&&e(v).childNode?(l(),c(R,{key:10,"flow-node":e(v).childNode,"onUpdate:flowNode":h[0]||(h[0]=S=>e(v).childNode=S),"parent-node":e(v),"onFind:recursiveFindParentNode":x},null,8,["flow-node","parent-node"])):N("",!0),e(v)&&e(v).type===e(w).END_EVENT_NODE?(l(),c(Va,{key:11,"flow-node":e(v)},null,8,["flow-node"])):N("",!0)],64)}}}),bd={class:"simple-process-model-container position-relative"},Ed={class:"position-absolute top-0px right-0px bg-#fff z-index-button-group"},Nd=yl(pe({name:"SimpleProcessModel",__name:"SimpleProcessModel",props:{flowNode:{type:Object,required:!0},readonly:{type:Boolean,required:!1,default:!0}},emits:["save"],setup(Q,{expose:le,emit:X}){const U=Q,v=X,n=Ce(U);Et("readonly",U.readonly);let d=$(100);const x=$(!1),I=$(0),h=$(0),R=$(0),S=$(0),k=$(0),r=$(0),s=()=>{document.body.style.cursor="grab"},f=F=>{x.value=!0,I.value=F.clientX-R.value,h.value=F.clientY-S.value,s()},A=F=>{x.value&&(F.preventDefault(),requestAnimationFrame(()=>{R.value=F.clientX-I.value,S.value=F.clientY-h.value}))},p=()=>{x.value=!1,document.body.style.cursor="default"},_=()=>{R.value=k.value,S.value=r.value},H=$(!1);let D=[];const z=(F,T)=>{if(F){const{type:b,showText:O,conditionNodes:m}=F;if(b==w.END_EVENT_NODE)return;b==w.START_USER_NODE&&z(F.childNode,T),b!==w.USER_TASK_NODE&&b!==w.COPY_TASK_NODE&&b!==w.CONDITION_NODE||(O||T.push(F),z(F.childNode,T)),b!=w.CONDITION_BRANCH_NODE&&b!=w.PARALLEL_BRANCH_NODE&&b!=w.INCLUSIVE_BRANCH_NODE||(m==null||m.forEach(G=>{z(G,T)}),z(F.childNode,T))}};le({getCurrentFlowData:async()=>{try{return D=[],z(n.value,D),D.length>0?void(H.value=!0):n.value}catch{return}}});const K=()=>{ta.json(new Blob([JSON.stringify(n.value)]),"model.json")},Y=$(),C=()=>{Y.value.click()},Z=()=>{const F=Y.value.files[0],T=new FileReader;T.readAsText(F),T.onload=function(){_t(this.result)&&(n.value=JSON.parse(this.result),v("save",n.value))}};return Wl(()=>{k.value=R.value,r.value=S.value}),(F,T)=>{const b=me,O=Le,m=Bt,G=bl,W=Xl;return l(),i(L,null,[o("div",bd,[o("div",Ed,[t(G,{type:"flex",justify:"end"},{default:a(()=>[t(m,{key:"scale-control",size:"default"},{default:a(()=>[Q.readonly?N("",!0):(l(),c(O,{key:0,size:"default",onClick:K},{default:a(()=>[t(b,{icon:"ep:download"}),T[6]||(T[6]=P(" \u5BFC\u51FA "))]),_:1})),Q.readonly?N("",!0):(l(),c(O,{key:1,size:"default",onClick:C},{default:a(()=>[t(b,{icon:"ep:upload"}),T[7]||(T[7]=P("\u5BFC\u5165 "))]),_:1})),Q.readonly?N("",!0):(l(),i("input",{key:2,type:"file",id:"files",ref_key:"refFile",ref:Y,style:{display:"none"},accept:".json",onChange:Z},null,544)),t(O,{size:"default",icon:e($t),onClick:T[0]||(T[0]=B=>{d.value=100})},null,8,["icon"]),t(O,{size:"default",plain:!0,icon:e(jt),onClick:T[1]||(T[1]=B=>{d.value!=50&&(d.value-=10)})},null,8,["icon"]),t(O,{size:"default",class:"w-80px"},{default:a(()=>[P(M(e(d))+"% ",1)]),_:1}),t(O,{size:"default",plain:!0,icon:e(Kt),onClick:T[2]||(T[2]=B=>{d.value!=200&&(d.value+=10)})},null,8,["icon"]),t(O,{size:"default",onClick:_},{default:a(()=>T[8]||(T[8]=[P("\u91CD\u7F6E")])),_:1})]),_:1})]),_:1})]),o("div",{class:"simple-process-model",style:Nt(`transform: translate(${e(R)}px, ${e(S)}px) scale(${e(d)/100});`),onMousedown:f,onMousemove:A,onMouseup:p,onMouseleave:p,onMouseenter:s},[e(n)?(l(),c(Pl,{key:0,"flow-node":e(n),"onUpdate:flowNode":T[3]||(T[3]=B=>ie(n)?n.value=B:null)},null,8,["flow-node"])):N("",!0)],36)]),t(W,{modelValue:e(H),"onUpdate:modelValue":T[5]||(T[5]=B=>ie(H)?H.value=B:null),title:"\u4FDD\u5B58\u5931\u8D25",width:"400",fullscreen:!1},{footer:a(()=>[t(O,{type:"primary",onClick:T[4]||(T[4]=B=>H.value=!1)},{default:a(()=>T[9]||(T[9]=[P("\u77E5\u9053\u4E86")])),_:1})]),default:a(()=>[T[10]||(T[10]=o("div",{class:"mb-2"},"\u4EE5\u4E0B\u8282\u70B9\u5185\u5BB9\u4E0D\u5B8C\u5584\uFF0C\u8BF7\u4FEE\u6539\u540E\u4FDD\u5B58",-1)),(l(!0),i(L,null,j(e(D),(B,ee)=>(l(),i("div",{class:"mb-3 b-rounded-1 bg-gray-100 p-2 line-height-normal",key:ee},M(B.name)+" : "+M(e(Be).get(B.type)),1))),128))]),_:1},8,["modelValue"])],64)}}}),[["__scopeId","data-v-a192ec29"]]);export{Nd as _};
