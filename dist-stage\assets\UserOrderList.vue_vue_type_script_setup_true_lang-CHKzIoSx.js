import{u as j,h as _,D as f,au as G,_ as Q}from"./index-BeQABqnP.js";import{_ as X}from"./index.vue_vue_type_script_setup_true_lang-BjqH9dXd.js";import{_ as $}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CxohUsTD.js";import{k as B,r as v,P as J,e as W,l as r,m as t,G as n,H as o,z as d,u as l,y as s,C as g,$ as y,Z as ee,A as O,I as le,E as x}from"./form-create-B86qX0W_.js";import{e as ae}from"./index-Bd-WZRER.js";import{a as ue}from"./index-DjQP-OUe.js";import{g as oe}from"./index-B8d8POev.js";import de from"./OrderTableColumn-CTVtPbPF.js";import{D as I}from"./constants-C3gLHYOK.js";import{h as te,aj as pe,x as se,Q as ie,F as re,k as ne,f as me,Z as ce,ak as ve}from"./form-designer-DQFPUccF.js";const ye=B({__name:"UserOrderList",props:{userId:{}},setup(E){const{push:q}=j(),V=v(!0),P=v(0),k=v([]),h=v([]),D=v([]),R=v(),u=v({pageNo:1,pageSize:10,userId:E.userId,status:void 0,payChannelCode:void 0,createTime:void 0,terminal:void 0,type:void 0,deliveryType:void 0,logisticsId:void 0,pickUpStoreId:void 0,pickUpVerifyCode:void 0}),b=J({queryParam:""}),S=v([{value:"no",label:"\u8BA2\u5355\u53F7"},{value:"userNickname",label:"\u7528\u6237\u6635\u79F0"},{value:"userMobile",label:"\u7528\u6237\u7535\u8BDD"}]),z=m=>{var a;(a=S.value.filter(p=>p.value!==m))==null||a.forEach(p=>{u.value.hasOwnProperty(p.value)&&delete u.value[p.value]})},U=async()=>{u.value.pageNo=1,await w()},H=()=>{var m;(m=R.value)==null||m.resetFields(),u.value.userId=E.userId,U()},w=async()=>{V.value=!0;try{const m=await ae(u.value);k.value=m.list,P.value=m.total}finally{V.value=!1}};return W(async()=>{await w(),h.value=await ue(),D.value=await oe()}),(m,a)=>{const p=ie,c=se,i=pe,L=re,A=ne,C=Q,T=me,M=te,N=$,F=ce,K=X,Z=ve;return t(),r(n,null,[o(N,null,{default:d(()=>[o(M,{ref_key:"queryFormRef",ref:R,inline:!0,model:l(u),class:"-mb-15px","label-width":"68px"},{default:d(()=>[o(i,{label:"\u8BA2\u5355\u72B6\u6001",prop:"status"},{default:d(()=>[o(c,{modelValue:l(u).status,"onUpdate:modelValue":a[0]||(a[0]=e=>l(u).status=e),class:"!w-280px",clearable:"",placeholder:"\u5168\u90E8"},{default:d(()=>[(t(!0),r(n,null,y(l(_)(l(f).TRADE_ORDER_STATUS),e=>(t(),s(p,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(i,{label:"\u652F\u4ED8\u65B9\u5F0F",prop:"payChannelCode"},{default:d(()=>[o(c,{modelValue:l(u).payChannelCode,"onUpdate:modelValue":a[1]||(a[1]=e=>l(u).payChannelCode=e),class:"!w-280px",clearable:"",placeholder:"\u5168\u90E8"},{default:d(()=>[(t(!0),r(n,null,y(l(G)(l(f).PAY_CHANNEL_CODE),e=>(t(),s(p,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(i,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime"},{default:d(()=>[o(L,{modelValue:l(u).createTime,"onUpdate:modelValue":a[2]||(a[2]=e=>l(u).createTime=e),"default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-280px","end-placeholder":"\u81EA\u5B9A\u4E49\u65F6\u95F4","start-placeholder":"\u81EA\u5B9A\u4E49\u65F6\u95F4",type:"daterange","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue","default-time"])]),_:1}),o(i,{label:"\u8BA2\u5355\u6765\u6E90",prop:"terminal"},{default:d(()=>[o(c,{modelValue:l(u).terminal,"onUpdate:modelValue":a[3]||(a[3]=e=>l(u).terminal=e),class:"!w-280px",clearable:"",placeholder:"\u5168\u90E8"},{default:d(()=>[(t(!0),r(n,null,y(l(_)(l(f).TERMINAL),e=>(t(),s(p,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(i,{label:"\u8BA2\u5355\u7C7B\u578B",prop:"type"},{default:d(()=>[o(c,{modelValue:l(u).type,"onUpdate:modelValue":a[4]||(a[4]=e=>l(u).type=e),class:"!w-280px",clearable:"",placeholder:"\u5168\u90E8"},{default:d(()=>[(t(!0),r(n,null,y(l(_)(l(f).TRADE_ORDER_TYPE),e=>(t(),s(p,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(i,{label:"\u914D\u9001\u65B9\u5F0F",prop:"deliveryType"},{default:d(()=>[o(c,{modelValue:l(u).deliveryType,"onUpdate:modelValue":a[5]||(a[5]=e=>l(u).deliveryType=e),class:"!w-280px",clearable:"",placeholder:"\u5168\u90E8"},{default:d(()=>[(t(!0),r(n,null,y(l(_)(l(f).TRADE_DELIVERY_TYPE),e=>(t(),s(p,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(u).deliveryType===l(I).EXPRESS.type?(t(),s(i,{key:0,label:"\u5FEB\u9012\u516C\u53F8",prop:"logisticsId"},{default:d(()=>[o(c,{modelValue:l(u).logisticsId,"onUpdate:modelValue":a[6]||(a[6]=e=>l(u).logisticsId=e),class:"!w-280px",clearable:"",placeholder:"\u5168\u90E8"},{default:d(()=>[(t(!0),r(n,null,y(l(D),e=>(t(),s(p,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):g("",!0),l(u).deliveryType===l(I).PICK_UP.type?(t(),s(i,{key:1,label:"\u81EA\u63D0\u95E8\u5E97",prop:"pickUpStoreId"},{default:d(()=>[o(c,{modelValue:l(u).pickUpStoreId,"onUpdate:modelValue":a[7]||(a[7]=e=>l(u).pickUpStoreId=e),class:"!w-280px",clearable:"",multiple:"",placeholder:"\u5168\u90E8"},{default:d(()=>[(t(!0),r(n,null,y(l(h),e=>(t(),s(p,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):g("",!0),l(u).deliveryType===l(I).PICK_UP.type?(t(),s(i,{key:2,label:"\u6838\u9500\u7801",prop:"pickUpVerifyCode"},{default:d(()=>[o(A,{modelValue:l(u).pickUpVerifyCode,"onUpdate:modelValue":a[8]||(a[8]=e=>l(u).pickUpVerifyCode=e),class:"!w-280px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u81EA\u63D0\u6838\u9500\u7801",onKeyup:ee(U,["enter"])},null,8,["modelValue"])]),_:1})):g("",!0),o(i,{label:"\u805A\u5408\u641C\u7D22"},{default:d(()=>[O(o(A,{modelValue:l(u)[l(b).queryParam],"onUpdate:modelValue":a[10]||(a[10]=e=>l(u)[l(b).queryParam]=e),class:"!w-280px",clearable:"",placeholder:"\u8BF7\u8F93\u5165"},{prepend:d(()=>[o(c,{modelValue:l(b).queryParam,"onUpdate:modelValue":a[9]||(a[9]=e=>l(b).queryParam=e),class:"!w-110px",clearable:"",placeholder:"\u5168\u90E8",onChange:z},{default:d(()=>[(t(!0),r(n,null,y(l(S),e=>(t(),s(p,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["modelValue"]),[[le,!0]])]),_:1}),o(i,null,{default:d(()=>[o(T,{onClick:U},{default:d(()=>[o(C,{class:"mr-5px",icon:"ep:search"}),a[13]||(a[13]=x(" \u641C\u7D22 "))]),_:1}),o(T,{onClick:H},{default:d(()=>[o(C,{class:"mr-5px",icon:"ep:refresh"}),a[14]||(a[14]=x(" \u91CD\u7F6E "))]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),o(N,null,{default:d(()=>[O((t(),s(F,{data:l(k),"row-key":"id"},{default:d(()=>[o(l(de),{list:l(k),"pick-up-store-list":l(h)},{default:d(({row:e})=>[o(T,{link:"",type:"primary",onClick:fe=>{return Y=e.id,void q({name:"TradeOrderDetail",params:{id:Y}});var Y}},{default:d(()=>[o(C,{icon:"ep:notification"}),a[15]||(a[15]=x(" \u8BE6\u60C5 "))]),_:2},1032,["onClick"])]),_:1},8,["list","pick-up-store-list"])]),_:1},8,["data"])),[[Z,l(V)]]),o(K,{limit:l(u).pageSize,"onUpdate:limit":a[11]||(a[11]=e=>l(u).pageSize=e),page:l(u).pageNo,"onUpdate:page":a[12]||(a[12]=e=>l(u).pageNo=e),total:l(P),onPagination:w},null,8,["limit","page","total"])]),_:1})],64)}}});export{ye as _};
