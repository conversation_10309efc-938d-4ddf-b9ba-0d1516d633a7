import{_ as V}from"./Form-Dh0DF4BH.js";import{p as h,a as j,a0 as W,_ as E}from"./index-BeQABqnP.js";import{r as v,n as N,u as n,k as G,c as z,l as T,m as w,G as H,H as P,C as x,_ as I,$ as K,z as b,q as M,y as k,E as L,F as _,N as J,P as Q,b as $}from"./form-create-B86qX0W_.js";import{K as U,f as X,O as q,M as B}from"./form-designer-DQFPUccF.js";import{d as Y}from"./download-oWiM5xVU.js";const Z={key:0},ee=G({name:"Search",__name:"Search",props:{schema:{type:Array,default:()=>[]},isCol:h.bool.def(!1),labelWidth:h.oneOfType([String,Number]).def("auto"),layout:h.string.validate(e=>["inline","bottom"].includes(e)).def("inline"),buttomPosition:h.string.validate(e=>["left","center","right"].includes(e)).def("center"),showSearch:h.bool.def(!0),showReset:h.bool.def(!0),expand:h.bool.def(!1),expandField:h.string.def(""),inline:h.bool.def(!0),model:{type:Object,default:()=>({})}},emits:["search","reset"],setup(e,{emit:a}){const{t:g}=j(),d=e,C=a,m=v(!0),F=z(()=>{let s=U(d.schema);if(d.expand&&d.expandField&&!n(m)){const p=W(s,r=>r.field===d.expandField);if(p>-1){const r=s.length;s.splice(p+1,r)}}return d.layout==="inline"&&(s=s.concat([{field:"action",formItemProps:{labelWidth:"0px"}}])),s}),{register:y,elFormRef:t,methods:i}=(s=>{const p=v(),r=v(),l=async()=>(await N(),n(p)),A={setProps:async(c={})=>{const o=await l();o==null||o.setProps(c),c.model&&(o==null||o.setValues(c.model))},setValues:async c=>{const o=await l();o==null||o.setValues(c)},setSchema:async c=>{const o=await l();o==null||o.setSchema(c)},addSchema:async(c,o)=>{const R=await l();R==null||R.addSchema(c,o)},delSchema:async c=>{const o=await l();o==null||o.delSchema(c)},getFormData:async()=>{const c=await l();return c==null?void 0:c.formModel}};return s&&A.setProps(s),{register:(c,o)=>{p.value=c,r.value=o},elFormRef:r,methods:A}})({model:d.model||{}}),f=async()=>{var s;await((s=n(t))==null?void 0:s.validate(async p=>{if(p){const{getFormData:r}=i,l=await r();C("search",l)}}))},S=async()=>{var r;(r=n(t))==null||r.resetFields();const{getFormData:s}=i,p=await s();C("reset",p)},D=z(()=>({textAlign:d.buttomPosition})),O=()=>{var s;(s=n(t))==null||s.resetFields(),m.value=!n(m)};return(s,p)=>{const r=E,l=X,A=V;return w(),T(H,null,[P(A,{inline:e.inline,"is-col":e.isCol,"is-custom":!1,"label-width":e.labelWidth,schema:n(F),class:"-mb-15px","hide-required-asterisk":"",onRegister:n(y)},I({action:b(()=>[e.layout==="inline"?(w(),T("div",Z,[e.showSearch?(w(),k(l,{key:0,onClick:f},{default:b(()=>[P(r,{class:"mr-5px",icon:"ep:search"}),L(" "+_(n(g)("common.query")),1)]),_:1})):x("",!0),e.showReset?(w(),k(l,{key:1,onClick:S},{default:b(()=>[P(r,{class:"mr-5px",icon:"ep:refresh"}),L(" "+_(n(g)("common.reset")),1)]),_:1})):x("",!0),e.expand?(w(),k(l,{key:2,text:"",onClick:O},{default:b(()=>[L(_(n(g)(n(m)?"common.shrink":"common.expand"))+" ",1),P(r,{icon:n(m)?"ep:arrow-up":"ep:arrow-down"},null,8,["icon"])]),_:1})):x("",!0),M(s.$slots,"actionMore")])):x("",!0)]),_:2},[K(Object.keys(s.$slots),c=>({name:c,fn:b(()=>[M(s.$slots,c)])}))]),1032,["inline","is-col","label-width","schema","onRegister"]),e.layout==="bottom"?(w(),T("div",{key:0,style:J(n(D))},[e.showSearch?(w(),k(l,{key:0,type:"primary",onClick:f},{default:b(()=>[P(r,{class:"mr-5px",icon:"ep:search"}),L(" "+_(n(g)("common.query")),1)]),_:1})):x("",!0),e.showReset?(w(),k(l,{key:1,onClick:S},{default:b(()=>[P(r,{class:"mr-5px",icon:"ep:refresh-right"}),L(" "+_(n(g)("common.reset")),1)]),_:1})):x("",!0),e.expand?(w(),k(l,{key:2,text:"",onClick:O},{default:b(()=>[L(_(n(g)(n(m)?"common.shrink":"common.expand"))+" ",1),P(r,{icon:n(m)?"ep:arrow-up":"ep:arrow-down"},null,8,["icon"])]),_:1})):x("",!0),M(s.$slots,"actionMore")],4)):x("",!0)],64)}}}),{t:u}=j(),te=e=>{const a=Q({pageSize:10,currentPage:1,total:10,tableList:[],params:{...(e==null?void 0:e.defaultParams)||{}},loading:!0,exportLoading:!1,currentRow:null}),g=z(()=>({...a.params,pageSize:a.pageSize,pageNo:a.currentPage}));$(()=>a.currentPage,()=>{y.getList()}),$(()=>a.pageSize,()=>{a.currentPage===1||(a.currentPage=1),y.getList()});const d=v(),C=v(),m=async()=>(await N(),n(d)),F=async t=>{let i=1;t instanceof Array?(i=t.length,await Promise.all(t.map(async f=>{await((e==null?void 0:e.delListApi)&&(e==null?void 0:e.delListApi(f)))}))):await((e==null?void 0:e.delListApi)&&(e==null?void 0:e.delListApi(t))),B.success(u("common.delSuccess")),a.currentPage=(a.total%a.pageSize===i||a.pageSize===1)&&a.currentPage>1?a.currentPage-1:a.currentPage,await y.getList()},y={getList:async()=>{a.loading=!0;const t=await(e==null?void 0:e.getListApi(n(g)).finally(()=>{a.loading=!1}));t&&(a.tableList=t.list,a.total=t.total??0)},setProps:async(t={})=>{const i=await m();i==null||i.setProps(t)},setColumn:async t=>{const i=await m();i==null||i.setColumn(t)},getSelections:async()=>{const t=await m();return(t==null?void 0:t.selections)||[]},setSearchParams:t=>{a.params=Object.assign(a.params,{pageSize:a.pageSize,pageNo:1,...t}),a.currentPage!==1?a.currentPage=1:y.getList()},delList:async(t,i,f=!0)=>{const S=await m();!i||S!=null&&S.selections.length?f?q.confirm(u("common.delMessage"),u("common.confirmTitle"),{confirmButtonText:u("common.ok"),cancelButtonText:u("common.cancel"),type:"warning"}).then(async()=>{await F(t)}):await F(t):B.warning(u("common.delNoData"))},exportList:async t=>{a.exportLoading=!0,q.confirm(u("common.exportMessage"),u("common.confirmTitle"),{confirmButtonText:u("common.ok"),cancelButtonText:u("common.cancel"),type:"warning"}).then(async()=>{var f;const i=await((f=e==null?void 0:e.exportListApi)==null?void 0:f.call(e,n(g)));i&&Y.excel(i,t)}).finally(()=>{a.exportLoading=!1})}};return e!=null&&e.props&&y.setProps(e.props),{register:(t,i)=>{d.value=t,C.value=i},elTableRef:C,tableObject:a,methods:y,tableMethods:y}};export{ee as _,te as u};
