import{W as b,a as te,d as de,au as U,D as S}from"./index-BeQABqnP.js";import{_ as ue}from"./Dialog.vue_vue_type_style_index_0_lang-DpoeWsKb.js";import{M as D}from"./index-D4qK--X-.js";import{U as x}from"./index-BDU5cx5r.js";import{g as re}from"./index-CxjbpoB6.js";import{h as ie}from"./tree-COGD3qag.js";import{_ as oe}from"./index-CZs2S1Cj.js";import{h as me,i as ne,j as pe,aj as se,k as ce,F as ve,x as ye,Q as fe,a1 as he,ak as be,f as _e}from"./form-designer-DQFPUccF.js";import{k as Ve,r as s,P as ge,b as Ie,e as qe,y as c,m as n,u as d,h as we,z as r,A as Ne,H as l,l as q,G as w,$ as N,E as j,n as F}from"./form-create-B86qX0W_.js";const C={getRequirementPage:async p=>await b.get({url:"/scm/purchase/requirement/page",params:p}),getRequirement:async p=>await b.get({url:"/scm/purchase/requirement/get?id="+p}),createRequirement:async p=>await b.post({url:"/scm/purchase/requirement/create",data:p}),updateRequirement:async p=>await b.put({url:"/scm/purchase/requirement/update",data:p}),deleteRequirement:async p=>await b.delete({url:"/scm/purchase/requirement/delete?id="+p}),exportRequirement:async p=>await b.download({url:"/scm/purchase/requirement/export-excel",params:p})},Te=Ve({name:"RequirementForm",__name:"RequirementForm",emits:["success"],setup(p,{expose:H,emit:Q}){const{t:k}=te(),z=de(),v=s(!1),O=s(""),y=s(!1),P=s(""),a=s({id:void 0,requestNo:void 0,materialId:void 0,materialName:void 0,requirementDate:void 0,materialCode:void 0,materialSpec:void 0,materialType:void 0,departmentId:void 0,quantity:void 0,unitPrice:void 0,unit:void 0,amount:void 0,expectedDeliveryDate:void 0,status:void 0,totalAmount:void 0,sourceType:void 0,sourceId:void 0,sourceNo:void 0,sourceDetailId:void 0,bizOrderId:void 0,bizOrderNo:void 0,bizOrderType:void 0,remark:void 0,note:void 0,purchaseNo:void 0,detailId:void 0}),Y=ge({materialId:[{required:!0,message:"\u7269\u6599\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],requirementDate:[{required:!0,message:"\u9700\u6C42\u65E5\u671F\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],quantity:[{required:!0,message:"\u7269\u6599\u6570\u91CF\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),R=s(),T=s({}),f=s({}),_=s([]),$={children:"children",label:"name",value:"id"};Ie(_,m=>{m.length>0&&a.value.departmentId&&F(()=>{})},{deep:!0});const B=m=>m?[m.fullCode||"",m.name||"",m.spec||""].filter(e=>e&&e.trim()).join(" - "):"",G=async m=>{try{const e=await D.getSimpleMaterialPage({pageNo:m.pageNo||1,pageSize:m.pageSize||20,name:m.name||void 0});return{list:e.list||[],total:e.total||0}}catch{return{list:[],total:0}}},V=s([]),E=async()=>{try{const m=await x.getUnitPage({pageNo:1,pageSize:100});V.value=m.list||[]}catch{V.value=[]}},L=m=>{if(m){const e=V.value.find(i=>i.id.toString()===m);e&&(f.value={id:e.id,name:e.name})}else f.value={}},W=async(m,e)=>{if(m&&e){if(a.value.materialName=e.name,a.value.materialCode=e.fullCode,a.value.materialSpec=e.spec,a.value.materialType=e.type,a.value.unitPrice=e.purchasePrice,e.purchaseUnit)try{const i=await x.getUnit(e.purchaseUnit);i&&(a.value.unit=i.id.toString(),f.value={id:i.id,name:i.name})}catch{a.value.unit=e.purchaseUnit.toString()}}else a.value.materialName=void 0,a.value.materialCode=void 0,a.value.materialSpec=void 0,a.value.materialType=void 0,a.value.unit=void 0,a.value.unitPrice=void 0,f.value={}};H({open:async(m,e,i)=>{if(v.value=!0,O.value=k("action."+m),P.value=m,K(),V.value.length===0&&await E(),await(async()=>{try{const u=await re();_.value=ie(u)}catch{_.value=[]}})(),e){y.value=!0;try{const u=await C.getRequirement(e);if(u.departmentId&&typeof u.departmentId=="string"&&(u.departmentId=parseInt(u.departmentId,10)),a.value=u,u.materialId&&u.materialName&&(T.value={id:u.materialId,name:u.materialName}),u.departmentId&&await F(),u.materialId&&(!u.materialType||u.materialType===void 0||u.materialType===null))try{const o=await D.getMaterial(u.materialId);o&&(o.type&&(a.value.materialType=o.type),!u.materialCode&&o.fullCode&&(a.value.materialCode=o.fullCode),!u.materialSpec&&o.spec&&(a.value.materialSpec=o.spec),!u.unitPrice&&o.purchasePrice&&(a.value.unitPrice=o.purchasePrice))}catch{}if(u.unit)try{const o=await x.getUnit(u.unit);o&&(f.value={id:o.id,name:o.name},a.value.unit=o.id.toString())}catch{}}catch{}finally{y.value=!1}}if(i){if(i.departmentId&&typeof i.departmentId=="string"&&(i.departmentId=parseInt(i.departmentId,10)),Object.assign(a.value,i),i.materialId&&i.materialName&&(T.value={id:i.materialId,name:i.materialName},!i.materialType||i.materialType===void 0||i.materialType===null))try{const u=await D.getMaterial(i.materialId);u&&(u.type&&(a.value.materialType=u.type),!i.materialCode&&u.fullCode&&(a.value.materialCode=u.fullCode),!i.materialSpec&&u.spec&&(a.value.materialSpec=u.spec),!i.unitPrice&&u.purchasePrice&&(a.value.unitPrice=u.purchasePrice))}catch{}i.unit&&typeof i.unit=="number"&&(a.value.unit=i.unit.toString())}}});const Z=Q,J=async()=>{await R.value.validate(),y.value=!0;try{const m=a.value;P.value==="create"||P.value==="transferPurchase"?(await C.createRequirement(m),z.success(k("common.createSuccess"))):(await C.updateRequirement(m),z.success(k("common.updateSuccess"))),v.value=!1,Z("success")}finally{y.value=!1}},K=()=>{var m;a.value={id:void 0,requestNo:void 0,materialId:void 0,materialName:void 0,requirementDate:void 0,materialCode:void 0,materialSpec:void 0,materialType:void 0,departmentId:void 0,quantity:void 0,unitPrice:void 0,unit:void 0,amount:void 0,expectedDeliveryDate:void 0,status:void 0,totalAmount:void 0,sourceType:void 0,sourceNo:void 0,bizOrderId:void 0,bizOrderNo:void 0,bizOrderType:void 0,remark:void 0,note:void 0,purchaseNo:void 0,detailId:void 0},(m=R.value)==null||m.resetFields(),T.value={},f.value={}};return qe(async()=>{await E()}),(m,e)=>{const i=ce,u=se,o=pe,A=ve,h=ne,g=fe,I=ye,X=he,ee=me,M=_e,ae=ue,le=be;return n(),c(ae,{title:d(O),modelValue:d(v),"onUpdate:modelValue":e[19]||(e[19]=t=>we(v)?v.value=t:null),width:"65%"},{footer:r(()=>[l(M,{onClick:J,type:"primary",disabled:d(y)},{default:r(()=>e[20]||(e[20]=[j("\u786E \u5B9A")])),_:1},8,["disabled"]),l(M,{onClick:e[18]||(e[18]=t=>v.value=!1)},{default:r(()=>e[21]||(e[21]=[j("\u53D6 \u6D88")])),_:1})]),default:r(()=>[Ne((n(),c(ee,{ref_key:"formRef",ref:R,model:d(a),rules:d(Y),"label-width":"100px"},{default:r(()=>[l(h,{gutter:16},{default:r(()=>[l(o,{span:8},{default:r(()=>[l(u,{label:"\u9700\u6C42\u7F16\u53F7",prop:"requestNo"},{default:r(()=>[l(i,{modelValue:d(a).requestNo,"onUpdate:modelValue":e[0]||(e[0]=t=>d(a).requestNo=t),placeholder:"\u4FDD\u5B58\u65F6\u81EA\u52A8\u751F\u6210",disabled:""},null,8,["modelValue"])]),_:1})]),_:1}),l(o,{span:8},{default:r(()=>[l(u,{label:"\u7269\u6599",prop:"materialId"},{default:r(()=>[l(oe,{modelValue:d(a).materialId,"onUpdate:modelValue":e[1]||(e[1]=t=>d(a).materialId=t),"load-method":G,"label-key":B,"value-key":"id","query-key":"name","default-value":d(T),onChange:W,placeholder:"\u8BF7\u9009\u62E9\u7269\u6599",style:{width:"100%"},disabled:""},null,8,["modelValue","default-value"])]),_:1})]),_:1}),l(o,{span:8},{default:r(()=>[l(u,{label:"\u9700\u6C42\u65E5\u671F",prop:"requirementDate"},{default:r(()=>[l(A,{modelValue:d(a).requirementDate,"onUpdate:modelValue":e[2]||(e[2]=t=>d(a).requirementDate=t),type:"date","value-format":"x",placeholder:"\u9009\u62E9\u9700\u6C42\u65E5\u671F",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(h,{gutter:16},{default:r(()=>[l(o,{span:8},{default:r(()=>[l(u,{label:"\u7269\u6599\u7F16\u53F7",prop:"materialCode"},{default:r(()=>[l(i,{modelValue:d(a).materialCode,"onUpdate:modelValue":e[3]||(e[3]=t=>d(a).materialCode=t),placeholder:"\u8BF7\u8F93\u5165\u7269\u6599\u7F16\u53F7",disabled:""},null,8,["modelValue"])]),_:1})]),_:1}),l(o,{span:8},{default:r(()=>[l(u,{label:"\u7269\u6599\u89C4\u683C",prop:"materialSpec"},{default:r(()=>[l(i,{modelValue:d(a).materialSpec,"onUpdate:modelValue":e[4]||(e[4]=t=>d(a).materialSpec=t),placeholder:"\u8BF7\u8F93\u5165\u7269\u6599\u89C4\u683C",disabled:""},null,8,["modelValue"])]),_:1})]),_:1}),l(o,{span:8},{default:r(()=>[l(u,{label:"\u7269\u6599\u7C7B\u578B",prop:"materialType"},{default:r(()=>[l(I,{modelValue:d(a).materialType,"onUpdate:modelValue":e[5]||(e[5]=t=>d(a).materialType=t),placeholder:"\u8BF7\u9009\u62E9\u7269\u6599\u7C7B\u578B",style:{width:"100%"},disabled:""},{default:r(()=>[(n(!0),q(w,null,N(d(U)(d(S).MATERIAL_TYPE),t=>(n(),c(g,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(h,{gutter:16},{default:r(()=>[l(o,{span:8},{default:r(()=>[l(u,{label:"\u9700\u6C42\u90E8\u95E8",prop:"departmentId"},{default:r(()=>[(n(),c(X,{modelValue:d(a).departmentId,"onUpdate:modelValue":e[6]||(e[6]=t=>d(a).departmentId=t),data:d(_),props:$,placeholder:"\u8BF7\u9009\u62E9\u9700\u6C42\u90E8\u95E8",style:{width:"100%"},clearable:"",filterable:"","check-strictly":"",key:`dept-tree-${d(_).length}`,"node-key":"id","default-expanded-keys":d(a).departmentId?[d(a).departmentId]:[]},null,8,["modelValue","data","default-expanded-keys"]))]),_:1})]),_:1}),l(o,{span:8},{default:r(()=>[l(u,{label:"\u9700\u6C42\u6570\u91CF",prop:"quantity"},{default:r(()=>[l(i,{modelValue:d(a).quantity,"onUpdate:modelValue":e[8]||(e[8]=t=>d(a).quantity=t),placeholder:"\u8BF7\u8F93\u5165\u7269\u6599\u6570\u91CF"},{append:r(()=>[l(I,{modelValue:d(a).unit,"onUpdate:modelValue":e[7]||(e[7]=t=>d(a).unit=t),placeholder:"\u8BF7\u9009\u62E9\u5355\u4F4D",style:{width:"100px"},onChange:L},{default:r(()=>[(n(!0),q(w,null,N(d(V),t=>(n(),c(g,{key:t.id,label:t.name,value:t.id.toString()},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(o,{span:8},{default:r(()=>[l(u,{label:"\u671F\u671B\u4EA4\u8D27\u65E5\u671F",prop:"expectedDeliveryDate"},{default:r(()=>[l(A,{modelValue:d(a).expectedDeliveryDate,"onUpdate:modelValue":e[9]||(e[9]=t=>d(a).expectedDeliveryDate=t),type:"date","value-format":"x",placeholder:"\u9009\u62E9\u671F\u671B\u4EA4\u8D27\u65E5\u671F",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(h,{gutter:16},{default:r(()=>[l(o,{span:8},{default:r(()=>[l(u,{label:"\u72B6\u6001",prop:"status"},{default:r(()=>[l(I,{modelValue:d(a).status,"onUpdate:modelValue":e[10]||(e[10]=t=>d(a).status=t),placeholder:"\u8BF7\u9009\u62E9\u72B6\u6001",style:{width:"100%"},disabled:""},{default:r(()=>[(n(!0),q(w,null,N(d(U)(d(S).PURCHASE_REQ_STATUS),t=>(n(),c(g,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(o,{span:8},{default:r(()=>[l(u,{label:"\u9700\u6C42\u6765\u6E90\u7C7B\u578B",prop:"sourceType"},{default:r(()=>[l(I,{modelValue:d(a).sourceType,"onUpdate:modelValue":e[11]||(e[11]=t=>d(a).sourceType=t),placeholder:"\u8BF7\u9009\u62E9\u9700\u6C42\u6765\u6E90\u7C7B\u578B",style:{width:"100%"},disabled:""},{default:r(()=>[(n(!0),q(w,null,N(d(U)(d(S).PURCHASE_REQ_SOURCE),t=>(n(),c(g,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(o,{span:8},{default:r(()=>[l(u,{label:"\u6765\u6E90\u5355\u53F7",prop:"sourceNo"},{default:r(()=>[l(i,{modelValue:d(a).sourceNo,"onUpdate:modelValue":e[12]||(e[12]=t=>d(a).sourceNo=t),placeholder:"\u8BF7\u8F93\u5165\u6765\u6E90\u5355\u53F7",disabled:""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(h,{gutter:16},{default:r(()=>[l(o,{span:8},{default:r(()=>[l(u,{label:"\u8BA2\u5355\u7C7B\u578B",prop:"bizOrderType"},{default:r(()=>[l(I,{modelValue:d(a).bizOrderType,"onUpdate:modelValue":e[13]||(e[13]=t=>d(a).bizOrderType=t),placeholder:"\u8BF7\u9009\u62E9\u9700\u6C42\u6765\u6E90\u7C7B\u578B",style:{width:"100%"}},{default:r(()=>[(n(!0),q(w,null,N(d(U)(d(S).SCM_BIZ_TYPE),t=>(n(),c(g,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(o,{span:8},{default:r(()=>[l(u,{label:"\u8BA2\u5355\u7F16\u53F7",prop:"bizOrderNo"},{default:r(()=>[l(i,{modelValue:d(a).bizOrderNo,"onUpdate:modelValue":e[14]||(e[14]=t=>d(a).bizOrderNo=t),placeholder:"\u8BF7\u8F93\u5165\u4E1A\u52A1\u8BA2\u5355\u7F16\u53F7"},null,8,["modelValue"])]),_:1})]),_:1}),l(o,{span:8},{default:r(()=>[l(u,{label:"\u91C7\u8D2D\u8BA2\u5355",prop:"purchaseNo"},{default:r(()=>[l(i,{modelValue:d(a).purchaseNo,"onUpdate:modelValue":e[15]||(e[15]=t=>d(a).purchaseNo=t),placeholder:"\u8BF7\u8F93\u5165\u91C7\u8D2D\u8BA2\u5355",disabled:""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(h,{gutter:16},{default:r(()=>[l(o,{span:8},{default:r(()=>[l(u,{label:"\u5907\u6CE8",prop:"remark"},{default:r(()=>[l(i,{modelValue:d(a).remark,"onUpdate:modelValue":e[16]||(e[16]=t=>d(a).remark=t),placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue"])]),_:1})]),_:1}),l(o,{span:8},{default:r(()=>[l(u,{label:"\u6458\u8981",prop:"note"},{default:r(()=>[l(i,{modelValue:d(a).note,"onUpdate:modelValue":e[17]||(e[17]=t=>d(a).note=t),placeholder:"\u8BF7\u8F93\u5165\u6458\u8981"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])),[[le,d(y)]])]),_:1},8,["title","modelValue"])}}});export{C as R,Te as _};
