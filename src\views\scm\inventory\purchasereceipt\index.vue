<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="auto"
    >
      <!-- 基础搜索项 - 始终显示 -->
      <el-form-item label="单号" prop="orderNo">
        <el-input
          v-model="queryParams.orderNo"
          placeholder="请输入单号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="物料名称" prop="materialName">
        <el-input
          v-model="queryParams.materialName"
          placeholder="请输入物料名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="物料编码" prop="materialCode">
        <el-input
          v-model="queryParams.materialCode"
          placeholder="请输入物料编码"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <!-- 高级搜索项 - 可展开收起 -->
      <template v-if="isExpanded">
        <el-form-item label="来源类型" prop="sourceType">
          <el-select
            v-model="queryParams.sourceType"
            placeholder="请选择来源类型"
            clearable
            class="!w-240px"
          >
            <el-option
              v-for="item in material_source"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="供应商名称" prop="objectName">
          <el-input
            v-model="queryParams.objectName"
            placeholder="请输入供应商名称"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="业务类型" prop="bizType">
          <el-select
            v-model="queryParams.bizType"
            placeholder="请选择业务类型"
            clearable
            class="!w-240px"
          >
            <el-option
              v-for="item in inventory_transaction_type"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="来源单编号" prop="sourceNo">
          <el-input
            v-model="queryParams.sourceNo"
            placeholder="请输入来源单编号"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="交易对象订单号" prop="objectOrderNo">
          <el-input
            v-model="queryParams.objectOrderNo"
            placeholder="请输入交易对象订单号"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="交易日期" prop="date">
          <el-date-picker
            v-model="queryParams.date"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-240px"
          />
        </el-form-item>
        <!-- <el-form-item label="摘要" prop="note">
          <el-input
            v-model="queryParams.note"
            placeholder="请输入摘要"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="queryParams.remark"
            placeholder="请输入备注"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item> -->
        <el-form-item label="创建时间" prop="createTime">
          <el-date-picker
            v-model="queryParams.createTime"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="审批状态" prop="approveStatus">
          <el-select
            v-model="queryParams.approveStatus"
            placeholder="请选择审批状态"
            clearable
            class="!w-240px"
          >
            <el-option
            v-for="item in approve_status"
            :key="item.value"
            :label="item.label"
            :value="item.value"
            />
          </el-select>
        </el-form-item>

      </template>

      <!-- 操作按钮行 -->
      <el-form-item>
        <el-button @click="handleQuery" class="ml-4"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['inventory:purchase-receipt:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['inventory:purchase-receipt:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
        <el-button
          type="primary"
          plain
          @click="handleApproval"
          :loading="exportLoading"
          v-hasPermi="['inventory:purchase-receipt:approve']"
        >
          <Icon icon="ep:check" class="mr-5px" /> 审核
        </el-button>
        <el-button
          type="text"
          @click="toggleExpanded"
          class="ml-2"
        >
          {{ isExpanded ? '收起' : '展开' }}
          <Icon :icon="isExpanded ? 'ep:arrow-up' : 'ep:arrow-down'" class="ml-1" />
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table
      v-loading="loading"
      :data="flattenedList"
      :stripe="true"
      border
      :show-overflow-tooltip="true"
      highlight-current-row
      show-summary
      :summary-method="summaryMethod"
      @current-change="handleCurrentChange"
      @selection-change="handleSelectionChange"
      :span-method="objectSpanMethod"
      max-height="600"
      style="width: 100%"
    >
      <el-table-column type="selection" width="60" fixed="left" />
      <el-table-column label="单号" align="left" prop="orderNo" width="220px" fixed="left">
        <template #default="scope">
          <div class="order-no-container">
            <div class="order-no-cell">
              <div class="order-no-content">
                <el-link
                  type="primary"
                  @click="openDetail(scope.row.id, scope.row.orderNo)"
                  v-hasPermi="['inventory:purchase-receipt:query']"
                >
                  {{ scope.row.orderNo }}
                </el-link>
              </div>
              <el-button
                link
                type="info"
                @click="copyOrderNo(scope.row.orderNo)"
                class="copy-btn copy-btn-fixed"
                size="small"
              >
                <Icon icon="ep:copy-document" :size="12"/>
              </el-button>
            </div>
            <dict-tag
              v-if="scope.row.approveStatus"
              :type="DICT_TYPE.APPROVE_STATUS"
              :value="scope.row.approveStatus"
              class="status-tag"
            />
          </div>
        </template>
      </el-table-column>
<!--      <el-table-column label="业务类型" align="left" prop="bizType" width="100px" fixed="left">-->
<!--        <template #default="scope">-->
<!--          {{ getDictLabel('INVENTORY_TRANSACTION_TYPE', scope.row.bizType) }}-->
<!--        </template>-->
<!--      </el-table-column>-->

      <!-- 明细信息列 - 按重要性排序 -->
<!--      <el-table-column label="序号" align="center" prop="detail.num" width="60px" />-->
      <el-table-column label="物料编号" align="left" prop="detail.materialCode" width="120px" fixed="left"/>
      <el-table-column label="物料名称" align="left" prop="detail.materialName" width="180px" fixed="left"/>
      <!-- <el-table-column label="物料编号" align="left" prop="detail.materialCode" width="120px" /> -->
      <el-table-column label="实收数量" align="right" prop="detail.fulfilledQuantity" width="140px">
        <template #default="scope">
          {{ formatQuantity(scope.row.detail.fulfilledQuantity) }}{{ getUnitName(scope.row.detail.unit) }}
        </template>
      </el-table-column>
      <el-table-column label="应收数量" align="right" prop="detail.plannedQuantity" width="140px">
        <template #default="scope">
          {{ formatQuantity(scope.row.detail.plannedQuantity) }}{{ getUnitName(scope.row.detail.unit) }}
        </template>
      </el-table-column>
      <el-table-column label="单价" align="right" prop="detail.unitPrice" width="120px">
        <template #default="scope">
          {{ formatAmount(scope.row.detail.unitPrice) }}
        </template>
      </el-table-column>
      <el-table-column label="金额" align="right" prop="detail.amount" width="120px">
        <template #default="scope">
          {{ formatAmount(scope.row.detail.amount) }}
        </template>
      </el-table-column>
      <el-table-column label="含税单价" align="right" prop="detail.taxPrice" width="100px">
        <template #default="scope">
          {{ formatAmount(scope.row.detail.taxPrice) }}
        </template>
      </el-table-column>
      <el-table-column label="含税金额" align="right" prop="detail.taxAmount" width="110px">
        <template #default="scope">
          {{ formatAmount(scope.row.detail.taxAmount) }}
        </template>
      </el-table-column>
      <el-table-column label="批号" align="left" prop="detail.batchNo" width="100px"/>



      <!-- 重要的主单据业务信息 -->
      <el-table-column label="来源类型" align="center" prop="sourceType" width="100px">
        <template #default="scope">
          {{ getDictLabel(DICT_TYPE.SCM_BIZ_TYPE, scope.row.sourceType) }}
        </template>
      </el-table-column>
      <el-table-column label="来源单编号" align="left" prop="sourceNo" width="120px">
        <template #default="scope">
          <div class="order-no-cell" v-if="scope.row.sourceNo">
            <div class="order-no-content">
              <span>{{ scope.row.sourceNo }}</span>
            </div>
            <el-button
              link
              type="info"
              @click="copyOrderNo(scope.row.sourceNo)"
              class="copy-btn copy-btn-fixed"
              size="small"
            >
              <Icon icon="ep:copy-document" :size="12"/>
            </el-button>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="交易对象名称" align="left" prop="objectName" width="150px"/>
      <el-table-column
        label="交易日期"
        align="center"
        prop="date"
        :formatter="dateFormatter"
        width="100px"
      />

      <!-- 基本单位和开票信息 -->
      <!-- <el-table-column label="基本单位" align="center" prop="detail.standardUnit" width="100px">
        <template #default="scope">
          {{ getUnitName(scope.row.detail.standardUnit) }}
        </template>
      </el-table-column>
      <el-table-column label="基本单位实收数量" align="right" prop="detail.standardFulfilledQuantity" width="140px">
        <template #default="scope">
          {{ formatQuantity(scope.row.detail.standardFulfilledQuantity) }}
        </template>
      </el-table-column>
      <el-table-column label="基本单位应收数量" align="right" prop="detail.standardPlannedQuantity" width="140px">
        <template #default="scope">
          {{ formatQuantity(scope.row.detail.standardPlannedQuantity) }}
        </template>
      </el-table-column> -->
      <!-- <el-table-column label="开票数量" align="right" prop="detail.invoiceQuantity" width="100px">
        <template #default="scope">
          {{ formatQuantity(scope.row.detail.invoiceQuantity) }}
        </template>
      </el-table-column>
      <el-table-column label="开票金额" align="right" prop="detail.invoiceAmount" width="100px">
        <template #default="scope">
          {{ formatAmount(scope.row.detail.invoiceAmount) }}
        </template>
      </el-table-column>
      <el-table-column label="开票基本数量" align="right" prop="detail.standardInvoiceQuantity" width="120px">
        <template #default="scope">
          {{ formatQuantity(scope.row.detail.standardInvoiceQuantity) }}
        </template>
      </el-table-column> -->
      <el-table-column
        label="生产日期"
        align="center"
        prop="detail.effictiveDate"
        :formatter="dateFormatter"
        width="100px"
      />
      <el-table-column
        label="失效日期"
        align="center"
        prop="detail.expiryDate"
        :formatter="dateFormatter"
        width="100px"
      />
      <!-- <el-table-column label="明细单号" align="left" prop="detail.bizOrderNo" width="120px" /> -->
      <!-- <el-table-column label="源单单号" align="left" prop="detail.sourceNo" width="120px"/> -->
      <!-- <el-table-column label="成本对象名称" align="left" prop="detail.costObjectName" width="120px"/>
      <el-table-column label="记账凭证号" align="left" prop="detail.accountingVoucherNumber" width="120px"/> -->
      <el-table-column label="说明" align="left" prop="detail.note" width="120px"/>
      <el-table-column label="明细备注" align="left" prop="detail.remark" width="120px"/>
      <el-table-column
        label="明细创建时间"
        align="center"
        prop="detail.createTime"
        :formatter="dateFormatter"
        width="180px"
      />

      <!-- 其他主单据信息 -->
      <el-table-column label="交易对象订单号" align="left" prop="objectOrderNo" width="140px">
        <template #default="scope">
          <div class="order-no-cell" v-if="scope.row.objectOrderNo">
            <div class="order-no-content">
              <span>{{ scope.row.objectOrderNo }}</span>
            </div>
            <el-button
              link
              type="info"
              @click="copyOrderNo(scope.row.objectOrderNo)"
              class="copy-btn copy-btn-fixed"
              size="small"
            >
              <Icon icon="ep:copy-document" :size="12"/>
            </el-button>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="摘要" align="left" prop="note" width="120px"/>
      <el-table-column label="备注" align="left" prop="remark" width="120px"/>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" min-width="200px" fixed="right" prop="operation">
        <template #default="scope">

          <el-button
            link
            type="primary"
            @click="openInspectionForm(scope.row)"
            v-hasPermi="['inventory:purchase-receipt:update']"
          >
            质检
          </el-button>
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['inventory:purchase-receipt:update']"
            v-if="scope.row.approveStatus !== 3"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['inventory:purchase-receipt:delete']"
            v-if="scope.row.approveStatus !== 3"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <PurchaseReceiptForm ref="formRef" @success="getList" />

  <!-- 审核表单 -->
  <ApproveInfoForm ref="approveInfoFormRef" @success="getList" :biz-id="currentRow?.id" biz-type="purchase_receipt" :biz-no="currentRow?.orderNo"/>
  <!-- 质检表单 -->
  <InspectionForm ref="inspectionFormRef" @success="getList" />
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { PurchaseReceiptApi, PurchaseReceiptVO } from '@/api/scm/inventory/purchasereceipt'
import { UnitApi } from '@/api/scm/base/unit'
import PurchaseReceiptForm from './PurchaseReceiptForm.vue'

import ApproveInfoForm from '../../base/approveinfo/ApproveInfoForm.vue'
import InspectionForm from '../../quality/inspection/InspectionForm.vue'
import { DICT_TYPE,getStrDictOptions,getDictLabel } from '@/utils/dict'
import { useClipboard } from '@vueuse/core'
import { formatAmount, formatQuantity } from '@/utils/formatter'
import { getRemoteUnit } from '@/utils/commonBiz'
/** 采购入库 列表 */
defineOptions({ name: 'PurchaseReceipt' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const { push } = useRouter() // 路由
const { copy } = useClipboard() // 复制功能

/** 复制订单单号 */
const copyOrderNo = async (orderNo: string) => {
  try {
    await copy(orderNo)
    message.success('单号复制成功')
  } catch (error) {
    message.error('复制失败')
  }
}

const loading = ref(true) // 列表的加载中
const list = ref<(PurchaseReceiptVO & { purchaseReceiptDetails?: any[], details?: any[] })[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const isExpanded = ref(false) // 搜索表单展开状态
const unitMap = ref<Map<number, string>>(new Map()) // 单位映射
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  orderNo: undefined,
  bizType: undefined,
  sourceType: undefined,
  sourceId: undefined,
  sourceNo: undefined,
  objectId: undefined,
  objectName: undefined,
  objectOrderNo: undefined,
  date: [],
  warehouseId: undefined,
  accountId: undefined,
  note: undefined,
  remark: undefined,
  createTime: [],
  approveStatus: undefined,
  deptId: undefined,
  empId: undefined,
  managerId: undefined,
  manger1Id: undefined,
  accountantId: undefined,
  checkerId: undefined,
  detail: true,
  materialName: undefined,
  materialCode: undefined,
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const selectedRows = ref<PurchaseReceiptVO[]>([]) // 选中的行
const approveInfoFormRef = ref()
const inspectionFormRef = ref() // 质检表单引用
const material_source = getStrDictOptions(DICT_TYPE.MATERIAL_SOURCE)
const approve_status = getStrDictOptions(DICT_TYPE.APPROVE_STATUS)
const inventory_transaction_type = getStrDictOptions(DICT_TYPE.INVENTORY_TRANSACTION_TYPE)
/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await PurchaseReceiptApi.getPurchaseReceiptPage(queryParams)
    list.value = data.list
    total.value = data.total

    // 直接使用列表中的 details 字段作为明细数据
    list.value.forEach(order => {
      // 将 details 字段赋值给 purchaseReceiptDetails，保持原有的属性名
      order.purchaseReceiptDetails = order.details || []
    })
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 切换展开/收起状态 */
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 打开详情页面 */
const openDetail = (id: number, orderNo?: string) => {
  push({
    name: 'PurchaseReceiptDetail',
    params: { id },
    query: {
      title: orderNo || '采购收货详情' // 传递单号作为标题
    }
  })
}



/** 打开质检表单 */
const openInspectionForm = async (row: any) => {
  try {
    // 获取采购入库单的详细信息
    const receiptData = await PurchaseReceiptApi.getPurchaseReceipt(row.id)

    // 获取采购入库明细数据
    let detailsData = []
    try {
      // 优先使用专门的明细API
      detailsData = await PurchaseReceiptApi.getPurchaseReceiptDetailListByBizOrderId(row.id)
    } catch (error) {
      // 如果明细API失败，从主数据中获取
      if (receiptData.purchaseReceiptDetails && receiptData.purchaseReceiptDetails.length > 0) {
        detailsData = receiptData.purchaseReceiptDetails
      } else if (receiptData.details && receiptData.details.length > 0) {
        detailsData = receiptData.details
      }
    }

    // 处理明细数据，确保包含单位名称
    const processedDetails = await Promise.all(detailsData.map(async (detail: any) => {
      const processedDetail = { ...detail }

      // 如果有unit字段但没有unitName，则获取单位名称
      if (processedDetail.unit && !processedDetail.unitName) {
        try {
          const unitId = typeof processedDetail.unit === 'string' ? parseInt(processedDetail.unit) : processedDetail.unit
          if (!isNaN(unitId)) {
            const unitInfo = await UnitApi.getUnit(unitId)
            if (unitInfo) {
              processedDetail.unitName = unitInfo.name
            }
          }
        } catch (error) {
          console.error('获取单位信息失败:', error)
        }
      }

      return processedDetail
    }))

    // 构造质检表单需要的数据
    const inspectionData = {
      id: row.id,
      orderNo: row.orderNo,
      sourceType: 'purchase_receipt',
      sourceNo: row.sourceNo,
      objectName: row.objectName,
      date: row.date,
      details: processedDetails,
      // 添加其他需要的字段
      warehouseId: row.warehouseId,
      remark: row.remark
    }

    // 打开质检表单并传入数据
    inspectionFormRef.value.open('create', undefined, inspectionData)
  } catch (error) {
    console.error('获取采购入库单详情失败:', error)
    message.error('获取采购入库单详情失败，请重试')
  }
}



/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await PurchaseReceiptApi.deletePurchaseReceipt(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await PurchaseReceiptApi.exportPurchaseReceipt(queryParams)
    download.excel(data, '采购入库.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 选择变化处理 */
const handleSelectionChange = (selection: PurchaseReceiptVO[]) => {
  selectedRows.value = selection
}

/** 审核按钮操作 */
const handleApproval = async () => {
  console.log('handleApproval', selectedRows.value)
  if(!selectedRows.value || selectedRows.value.length === 0) {
    message.error('请选择要审核的采购收货单！')
    return
  }

  // 如果选中了多个入库单，提示用户
  if(selectedRows.value.length > 1) {
    message.warning('当前只支持单个采购收货单审核，请选择一个收货单进行审核')
    return
  }

  // 设置当前行为选中的第一个入库单
  const selectedReceipt = selectedRows.value[0]
  currentRow.value = selectedReceipt

  // 检查审核状态，如果已经审核通过，则不允许再次审核
  if (selectedReceipt.approveStatus?.toString() === '3') {
    message.warning(`采购收货单 "${selectedReceipt.orderNo}" 已经审核通过，不能重复审核！`)
    return
  }

  // 发起审批，传入业务数据
  approveInfoFormRef.value.open('approve', undefined, {
    bizId: selectedReceipt.id,
    bizNo: selectedReceipt.orderNo,
    bizType: 'purchase_receipt'
  })
}

/** 选中行操作 */
const currentRow = ref<PurchaseReceiptVO>({} as PurchaseReceiptVO) // 选中行
const handleCurrentChange = (row: any) => {
  currentRow.value = row
}

// 定义合并行数存储数组
const spanArr = ref<number[]>([])

const flattenedList = computed(() => {
  const result: any[] = []
  spanArr.value = [] // 每次重新计算时清空旧数据

  list.value.forEach(order => {
    const details = order.purchaseReceiptDetails?.length ? order.purchaseReceiptDetails : [{}] // 确保无明细时也有占位行
    const detailCount = details.length

    details.forEach((detail: any, index: number) => {
      result.push({ ...order, detail })
      // 主信息列只在第一个明细行合并，合并行数=明细数量
      spanArr.value.push(index === 0 ? detailCount : 0)
    })
  })

  return result
})

// 需要合并的主信息列（必须与el-table-column的prop严格匹配）
const mergeFields = [
  'orderNo', 'bizType', 'sourceType', 'sourceNo', 'objectName', 'objectOrderNo',
  'date', 'note', 'remark', 'createTime', 'approveStatus', 'operation'
]

const objectSpanMethod = ({ column, rowIndex }: { row: any, column: any, rowIndex: number }) => {
  // 处理选择列和主信息列合并
  if (column.type === 'selection' || mergeFields.includes(column.property)) {
    const span = spanArr.value[rowIndex]
    return {
      rowspan: span, // 合并行数
      colspan: span > 0 ? 1 : 0 // 0表示隐藏单元格
    }
  }
  // 明细列不合并
  return { rowspan: 1, colspan: 1 }
}

/** 表格汇总方法 */
const summaryMethod = ({ columns, data }: { columns: any[], data: any[] }) => {
  const sums: any[] = []

  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }

    // 需要汇总的数量字段
    const quantityFields = ['detail.fulfilledQuantity', 'detail.plannedQuantity',
                           'detail.standardFulfilledQuantity', 'detail.standardPlannedQuantity',
                           'detail.invoiceQuantity', 'detail.standardInvoiceQuantity']

    // 需要汇总的金额字段
    const amountFields = ['detail.unitPrice', 'detail.amount', 'detail.taxPrice',
                         'detail.taxAmount', 'detail.invoiceAmount']

    if (quantityFields.includes(column.property)) {
      // 数量字段汇总
      const values = data.map(item => {
        const value = column.property.split('.').reduce((obj, key) => obj?.[key], item)
        return Number(value) || 0
      })
      const total = values.reduce((prev, curr) => prev + curr, 0)
      sums[index] = formatQuantity(total)
    } else if (amountFields.includes(column.property)) {
      // 金额字段汇总
      const values = data.map(item => {
        const value = column.property.split('.').reduce((obj, key) => obj?.[key], item)
        return Number(value) || 0
      })
      const total = values.reduce((prev, curr) => prev + curr, 0)
      sums[index] = formatAmount(total)
    } else {
      // 其他字段不汇总
      sums[index] = ''
    }
  })

  return sums
}

/** 加载单位数据 */
const loadUnits = async () => {
  try {
    const units = await getRemoteUnit()
    if (!units || units.length === 0) return

    units.forEach((unit: any) => {
      if (unit && unit.id && unit.name) {
        unitMap.value.set(unit.id, unit.name)
      }
    })
  } catch (error) {
    console.error('加载单位数据失败:', error)
  }
}

/** 获取单位名称 */
const getUnitName = (unitId: number | string) => {
  if (!unitId) return ''
  const id = typeof unitId === 'string' ? parseInt(unitId) : unitId
  const unitName = unitMap.value.get(id)
  return unitName || unitId.toString()
}

/** 初始化 **/
onMounted(async () => {
  await loadUnits()
  getList()
})
</script>

<style scoped lang="scss">
/* 单号容器样式 */
.order-no-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
  padding: 4px 0;
}

/* 订单号行布局 */
.order-no-cell {
  display: flex;
  align-items: center;
  width: 100%;
  min-height: 20px;
  gap: 5px; /* 文字和图标之间保持一点点间隔 */
}

.order-no-content {
  flex: 0 1 auto; /* 改为自适应宽度，不占满剩余空间 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: calc(100% - 16px); /* 为图标预留空间 */
}

/* 复制按钮样式 */
.copy-btn {
  padding: 2px !important;
  height: 20px !important;
  min-height: 20px !important;
  width: 20px !important;
  font-size: 12px;
  opacity: 0.6;
  transition: opacity 0.2s;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}



.copy-btn:hover {
  opacity: 1;
}

/* 状态标签样式 */
.status-tag :deep(.el-tag) {
  font-size: 9px !important;
  padding: 1px 4px !important;
  height: 16px !important;
  line-height: 14px !important;
  border-radius: 6px !important;
  font-weight: 500 !important;
  flex-shrink: 0;
}
</style>
