import{aw as y}from"./index-BeQABqnP.js";import{_ as v}from"./Echart.vue_vue_type_script_setup_true_lang-DR6A3B70.js";import{S as w}from"./rank-CagTCAJY.js";import{ad as _,ag as k,ak as P,_ as R,Z as A,bh as q}from"./form-designer-DQFPUccF.js";import{k as G,r as d,P as I,e as Z,l as j,m as p,G as z,H as a,z as r,u as e,A as D,y as H}from"./form-create-B86qX0W_.js";const L=G({name:"ReceivablePriceRank",__name:"ReceivablePriceRank",props:{queryParams:{}},setup(c,{expose:u}){const g=c,t=d(!1),i=d([]),s=I({dataset:{dimensions:["nickname","count"],source:[]},grid:{left:20,right:20,bottom:20,containLabel:!0},legend:{top:50},series:[{name:"\u56DE\u6B3E\u91D1\u989D\u6392\u884C",type:"bar"}],toolbox:{feature:{dataZoom:{yAxisIndex:!1},brush:{type:["lineX","clear"]},saveAsImage:{show:!0,name:"\u56DE\u6B3E\u91D1\u989D\u6392\u884C"}}},tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},xAxis:{type:"value",name:"\u56DE\u6B3E\u91D1\u989D\uFF08\u5143\uFF09"},yAxis:{type:"category",name:"\u7B7E\u8BA2\u4EBA",nameGap:30}}),l=async()=>{t.value=!0;const o=await w.getReceivablePriceRank(g.queryParams);s.dataset&&s.dataset.source&&(s.dataset.source=q(o).reverse()),i.value=o,t.value=!1};return u({loadData:l}),Z(()=>{l()}),(o,N)=>{const b=v,f=k,m=_,n=R,h=A,x=P;return p(),j(z,null,[a(m,{shadow:"never"},{default:r(()=>[a(f,{loading:e(t),animated:""},{default:r(()=>[a(b,{height:500,options:e(s)},null,8,["options"])]),_:1},8,["loading"])]),_:1}),a(m,{shadow:"never",class:"mt-16px"},{default:r(()=>[D((p(),H(h,{data:e(i)},{default:r(()=>[a(n,{label:"\u516C\u53F8\u6392\u540D",align:"center",type:"index",width:"80"}),a(n,{label:"\u7B7E\u8BA2\u4EBA",align:"center",prop:"nickname","min-width":"200"}),a(n,{label:"\u90E8\u95E8",align:"center",prop:"deptName","min-width":"200"}),a(n,{label:"\u56DE\u6B3E\u91D1\u989D\uFF08\u5143\uFF09",align:"center",prop:"count","min-width":"200",formatter:e(y)},null,8,["formatter"])]),_:1},8,["data"])),[[x,e(t)]])]),_:1})],64)}}});export{L as _};
