import{aC as c,M as _,c as V}from"./index-BeQABqnP.js";import R from"./ThingModelInputOutputParam-CGyBtxFk.js";import{i as a,h as T}from"./config-BXOiFKPF.js";import{aj as b,s as g,u as E}from"./form-designer-DQFPUccF.js";import{k as y,b as O,l as h,m as F,G as I,H as l,z as u,u as e,E as r,F as d}from"./form-create-B86qX0W_.js";import"./Dialog.vue_vue_type_style_index_0_lang-DpoeWsKb.js";import"./ThingModelProperty-BG6FrGUo.js";import"./ThingModelEnumDataSpecs-B1l6ptpN.js";import"./ThingModelNumberDataSpecs-CDIi9w7g.js";const U=V(y({name:"ThingModelEvent",__name:"ThingModelEvent",props:{modelValue:{},isStructDataSpecs:{type:Boolean}},emits:["update:modelValue"],setup(n,{emit:v}){const t=c(n,"modelValue",v);return O(()=>t.value.type,p=>_(p)&&(t.value.type=a.INFO.value),{immediate:!0}),(p,o)=>{const s=E,f=g,i=b;return F(),h(I,null,[l(i,{rules:[{required:!0,message:"\u8BF7\u9009\u62E9\u4E8B\u4EF6\u7C7B\u578B",trigger:"change"}],label:"\u4E8B\u4EF6\u7C7B\u578B",prop:"event.type"},{default:u(()=>[l(f,{modelValue:e(t).type,"onUpdate:modelValue":o[0]||(o[0]=m=>e(t).type=m)},{default:u(()=>[l(s,{value:e(a).INFO.value},{default:u(()=>[r(d(e(a).INFO.label),1)]),_:1},8,["value"]),l(s,{value:e(a).ALERT.value},{default:u(()=>[r(d(e(a).ALERT.label),1)]),_:1},8,["value"]),l(s,{value:e(a).ERROR.value},{default:u(()=>[r(d(e(a).ERROR.label),1)]),_:1},8,["value"])]),_:1},8,["modelValue"])]),_:1}),l(i,{label:"\u8F93\u51FA\u53C2\u6570"},{default:u(()=>[l(R,{modelValue:e(t).outputParams,"onUpdate:modelValue":o[1]||(o[1]=m=>e(t).outputParams=m),direction:e(T).OUTPUT},null,8,["modelValue","direction"])]),_:1})],64)}}}),[["__scopeId","data-v-3feb9458"]]);export{U as default};
