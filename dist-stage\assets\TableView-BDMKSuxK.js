import{W as G,u as je,_ as f,c as Be}from"./index-BeQABqnP.js";import{f as Ue}from"./formatTime-CN67D7Gb.js";import{f as H}from"./dateUtil-ofH3oaca.js";import{f as Ye,ak as Fe,_ as Ee,P as Ve,a6 as We,Z as Ae,ay as Ge,M as x,O as He}from"./form-designer-DQFPUccF.js";import{k as Ze,r as C,c as Z,e as Je,l as J,m as B,G as Ke,v as t,A as Le,C as Re,H as r,z as o,E as d,u,y as Xe,F as l,N as es,B as g}from"./form-create-B86qX0W_.js";const K={getFulfillmentPage:async Q=>await G.get({url:"/scm/sale/fulfillment/page",params:Q}),initToMongo:async()=>await G.put({url:"/scm/sale/fulfillment/init-to-mongo"})},ss={class:"table-header"},as={class:"header-content"},ts={class:"header-actions"},ls={class:"order-cell"},rs={class:"order-no"},is={class:"customer-name"},cs={class:"customer-name"},os={class:"customer-name"},ns={class:"customer-name"},ds={class:"product-cell"},us={class:"product-name"},ps={class:"product-code"},vs={class:"product-info"},fs={class:"product-info"},gs={class:"quantity-cell"},ys={class:"main-quantity"},ms={class:"completed-quantity"},hs={class:"stock-cell"},ws={class:"stock-progress"},bs={class:"progress-bar"},_s={class:"progress-text"},Ds={class:"stock-info"},zs={class:"process-cell"},Is={class:"process-info"},Os={class:"process-progress"},ks={class:"process-detail"},qs={class:"process-detail"},Ts={class:"process-cell"},xs={class:"process-info"},Ns={class:"process-progress"},Cs={class:"process-detail"},Ss={class:"process-detail"},Qs={class:"process-cell"},Ms={class:"process-info"},$s={class:"process-progress"},Ps={class:"process-detail"},js={class:"process-detail"},Bs={class:"process-cell"},Us={class:"process-info"},Ys={class:"process-progress"},Fs={class:"process-detail"},Es={class:"process-detail"},Vs={class:"process-cell"},Ws={class:"process-info"},As={class:"process-progress"},Gs={class:"process-detail"},Hs={class:"process-detail"},Zs={class:"process-cell"},Js={class:"process-info"},Ks={class:"process-progress"},Ls={class:"process-detail"},Rs={class:"process-detail"},Xs={class:"process-cell"},ea={class:"process-info"},sa={class:"process-progress"},aa={class:"process-detail"},ta={class:"process-detail"},la={class:"pagination-container"},ra={key:0,class:"batch-actions"},ia={class:"batch-content"},ca={class:"batch-info"},oa={class:"batch-buttons"},L=Be(Ze({__name:"TableView",props:{queryParams:{},loading:{type:Boolean,default:!1},tableData:{default:()=>[]},orderTotals:{default:()=>({saleTotal:0,purchaseTotal:0,workTotal:0,total:0})}},emits:["update:query-params","order-detail","page-change","order-type-change"],setup(Q,{emit:R}){const N=Q,M=R,X=je(),h=C([]),ee=C(""),se=C(""),w=C(1),T=C(10),U=Z(()=>(N.tableData||[]).filter(e=>e.orderType==="sale"||!e.orderType)),ae=Z(()=>{var e;return((e=N.orderTotals)==null?void 0:e.saleTotal)||U.value.length||0}),te=({row:e})=>e.isException?"exception-row":"",le=()=>({backgroundColor:"#ffffff",color:"#374151",fontWeight:"600",fontSize:"13px",padding:"12px 8px",borderBottom:"2px solid #e5e7eb"}),re=()=>({padding:"12px 8px",fontSize:"13px"}),b=e=>e?Ue(e,"YYYY-MM-DD"):"-",Y=e=>{var a;return((a=e.orderDetails)==null?void 0:a[0])||{}},ie=e=>{const a=Y(e),i=a.quantity||e.quantity||0;if(a.specQuantityTotal)return a.specQuantityTotal;const n=a.specUnit||a.unit||a.spec||e.unit||"";return i?`${i} ${n}`:"-"},y=(e,a)=>e?`${e}${a}`:"-",ce=e=>{const a=e.completedQuantity||e.finishedQuantity||0,i=e.unit||e.unitName||"";return y(a,i)},$=e=>e.stockProgress||e.inventoryProgress||e.progress||0,oe=e=>{const a=$(e);return a>=80?"progress-green":a>=60?"progress-yellow":"progress-red"},ne=e=>{const a=e.stockQuantity||e.inventoryQuantity||0,i=e.unit||e.unitName||"";return y(a,i)},v="pending",_="in_progress",m="completed",de="failed",c=(e,a)=>{const i=e.progress||0;return{plan:(e.approvalStatus||"0")==="1"?m:v,purchase:i>20?m:i>0?_:v,production:i>60?m:i>30?_:v,quality:i>80?m:i>50?_:v,warehouseIn:i>85?m:i>60?_:v,delivery:e.deliveryStatus==="1"?m:i>70?_:v,warehouseOut:i>=100?m:i>80?_:v}[a]||v},D=(e,a)=>{const i=e.progress||0;switch(a){case"plan":return i>0?100:0;case"purchase":return Math.min(.3*i,100);case"production":return Math.min(.8*i,100);case"quality":return Math.min(.9*i,100);case"warehouseIn":return Math.min(.95*i,100);case"delivery":return Math.min(.98*i,100);case"warehouseOut":return i;default:return 0}},F={[m]:{type:"success",class:"status-completed",icon:"ep:check",label:"\u5B8C\u6210"},[_]:{type:"primary",class:"status-in-progress",icon:"ep:loading",label:"\u8FDB\u884C\u4E2D"},[v]:{type:"warning",class:"status-pending",icon:"ep:clock",label:"\u672A\u5F00\u59CB"},[de]:{type:"danger",class:"status-failed",icon:"ep:close",label:"\u5931\u8D25"}},S=e=>F[e]||F[v],z=e=>S(e).type,I=e=>S(e).class,O=e=>S(e).icon,k=e=>S(e).label,ue=(e,a)=>e.estimatedDeliveryDate||e.deliveryDate||new Date,E=(e,a)=>e.completedDate||e.estimatedDeliveryDate,V=(e,a)=>a==="warehouseIn"?e.completedDate||e.estimatedDeliveryDate:a==="delivery"?e.deliveryDate||e.estimatedDeliveryDate:e.completedDate||e.estimatedDeliveryDate,W=(e,a)=>e.approvalStatus||"0",pe=(e,a)=>!e||!a?"-":`${b(e).substring(5)}\u81F3${b(a).substring(5)}`,ve=e=>{const a=Y(e);return`${a.quantity||e.quantity||0}${a.specUnit||a.unit||e.unit||e.unitName||""}`},fe=e=>{const a=e.unit||e.unitName||"";return y(e.purchaseQuantity,a)},ge=e=>{const a=e.unit||e.unitName||"";return y(e.productionQuantity,a)},ye=e=>{const a=e.unit||e.unitName||"";return y(e.qualityQuantity,a)},me=e=>{const a=e.unit||e.unitName||"";return y(e.inboundQuantity,a)},he=e=>{switch(e){case"approved":case"passed":case"1":return"approval-success";case"rejected":case"failed":case"-1":return"approval-danger";default:return"approval-pending"}},we=e=>{switch(e){case"approved":case"passed":case"1":return"\u901A\u8FC7";case"rejected":case"failed":case"-1":return"\u62D2\u7EDD";default:return"\u5F85\u5BA1\u6838"}},be=e=>{const a=e.unit||e.unitName||"";return y(e.noticeQuantity,a)},_e=e=>{const a=e.unit||e.unitName||"";return y(e.outboundQuantity,a)},De=({prop:e,order:a})=>{ee.value=e,se.value=a,P()},ze=e=>{h.value=e},Ie=e=>{T.value=e,w.value=1,M("page-change",{pageNo:w.value,pageSize:T.value,orderType:"sale"})},Oe=e=>{w.value=e,M("page-change",{pageNo:w.value,pageSize:T.value,orderType:"sale"})},ke=()=>{P()},qe=()=>{x.info("\u5BFC\u51FA\u529F\u80FD\u5F00\u53D1\u4E2D...")},Te=()=>{x.info(`\u6279\u91CF\u5BFC\u51FA ${h.value.length} \u6761\u8BB0\u5F55`)},xe=()=>{x.info(`\u6279\u91CF\u6253\u5370 ${h.value.length} \u6761\u8BB0\u5F55`)},Ne=async()=>{try{await He.confirm(`\u786E\u5B9A\u8981\u53D6\u6D88\u9009\u4E2D\u7684 ${h.value.length} \u4E2A\u8BA2\u5355\u5417\uFF1F`,"\u6279\u91CF\u53D6\u6D88\u786E\u8BA4",{confirmButtonText:"\u786E\u5B9A",cancelButtonText:"\u53D6\u6D88",type:"warning"}),x.success("\u6279\u91CF\u53D6\u6D88\u6210\u529F"),h.value=[],P()}catch{}},P=async()=>{},Ce=async()=>{await K.initToMongo(),x.success("\u521D\u59CB\u5316\u6210\u529F")};return Je(()=>{N.tableData&&N.tableData.length!==0||M("page-change",{pageNo:w.value,pageSize:T.value,orderType:"sale"})}),(e,a)=>{const i=Ye,n=Ee,Se=Ve,q=We,Qe=Ae,Me=Ge,$e=Fe;return B(),J(Ke,null,[t("div",ss,[t("div",as,[a[5]||(a[5]=t("div",{class:"header-title"},[t("h3",{class:"title-text"},"\u9500\u552E\u8BA2\u5355\u5C65\u7EA6\u8DDF\u8E2A"),t("div",{class:"title-subtitle"},"\u5B9E\u65F6\u76D1\u63A7\u9500\u552E\u8BA2\u5355\u4ECE\u521B\u5EFA\u5230\u4EA4\u4ED8\u7684\u5168\u8FC7\u7A0B")],-1)),t("div",ts,[r(i,{size:"default",onClick:qe,type:"success"},{default:o(()=>[r(u(f),{icon:"ep:download",class:"btn-icon"}),a[2]||(a[2]=d(" \u5BFC\u51FA "))]),_:1}),r(i,{size:"default",onClick:ke,type:"info"},{default:o(()=>[r(u(f),{icon:"ep:refresh",class:"btn-icon"}),a[3]||(a[3]=d(" \u5237\u65B0 "))]),_:1}),r(i,{size:"default",onClick:Ce,type:"primary"},{default:o(()=>[r(u(f),{icon:"ep:upload",class:"btn-icon"}),a[4]||(a[4]=d(" \u521D\u59CB\u5316 "))]),_:1})])])]),Le((B(),Xe(Qe,{data:U.value,border:"",stripe:"","show-overflow-tooltip":!0,"highlight-current-row":"",onSortChange:De,onSelectionChange:ze,"row-class-name":te,"header-cell-style":le,"cell-style":re,"max-height":600},{default:o(()=>[r(n,{type:"selection",width:"50",align:"center"}),r(n,{label:"\u8BA2\u5355\u4FE1\u606F",width:"200",fixed:"left",sortable:"custom",prop:"orderNo"},{default:o(({row:s})=>[t("div",ls,[t("div",rs,[r(Se,{type:"primary",onClick:p=>(j=>{const A=j.id||j.orderId;if(!A)return void x.warning("\u8BA2\u5355ID\u4E0D\u5B58\u5728\uFF0C\u65E0\u6CD5\u8DF3\u8F6C");const Pe=j.orderNo;X.push({name:"SaleOrderProcessDetail",params:{id:A.toString()},query:{title:Pe||"\u8BA2\u5355\u5C65\u7EA6\u8BE6\u60C5"}})})(s),class:"order-link"},{default:o(()=>[d(l(s.salesOrderInfo.orderNo),1)]),_:2},1032,["onClick"])]),t("div",is,l(s.salesOrderInfo.customer.name||"-"),1),t("div",cs,"\u4E0B\u5355\u65F6\u95F4\uFF1A"+l(u(H)(s.salesOrderInfo.orderDate)||"-"),1),t("div",os,"\u53D1\u8D27\u65F6\u95F4\uFF1A"+l(u(H)(s.salesOrderInfo.deliveryTime)||"-"),1),t("div",ns,"\u8981\u6C42: "+l(s.salesOrderInfo.requirement||s.salesOrderInfo.remark||"-"),1)])]),_:1}),r(n,{label:"\u4EA7\u54C1\u4FE1\u606F","show-overflow-tooltip":""},{default:o(({row:s})=>[t("div",ds,[t("div",us,l(s.salesOrderInfo.product.name),1),t("div",ps,l(s.salesOrderInfo.product.fullCode),1),t("div",vs,l(s.salesOrderInfo.product.spec),1),t("div",fs,l(s.salesOrderInfo.product.quantity+" "+s.salesOrderInfo.product.unitName),1)])]),_:1}),r(n,{label:"\u6570\u91CF",width:"100",align:"center",sortable:"custom"},{default:o(({row:s})=>[t("div",gs,[t("div",ys,l(ie(s)),1),t("div",ms,"\u5DF2\u5B8C\u6210\uFF1A"+l(ce(s)),1)])]),_:1}),r(n,{label:"\u5E93\u5B58\u72B6\u6001",width:"130",align:"center","class-name":"hidden-on-mobile"},{default:o(({row:s})=>[t("div",hs,[t("div",ws,[t("div",bs,[t("div",{class:g(["progress-fill",oe(s)]),style:es({width:$(s)+"%"})},null,6)]),t("span",_s,l($(s))+"%",1)]),t("div",Ds,"\u5E93\u5B58\uFF1A"+l(ne(s)),1)])]),_:1}),r(n,{label:"\u8BA1\u5212",width:"120",align:"center"},{default:o(({row:s})=>{return[t("div",zs,[r(q,{type:z(c(s,"plan")),size:"small",class:g(["process-tag",I(c(s,"plan"))]),effect:"plain"},{default:o(()=>[r(u(f),{icon:O(c(s,"plan")),class:"tag-icon"},null,8,["icon"]),d(" "+l(k(c(s,"plan"))),1)]),_:2},1032,["type","class"]),t("div",Is,[t("div",Os,l(D(s,"plan"))+"%",1),a[6]||(a[6]=t("div",{class:"process-divider"},null,-1)),t("div",ks,l(pe((p=s,p.orderDate||new Date),ue(s))),1),t("div",qs,"\u8BA1\u5212\uFF1A"+l(ve(s)),1)])])];var p}),_:1}),r(n,{label:"\u91C7\u8D2D",width:"120",align:"center"},{default:o(({row:s})=>{return[t("div",Ts,[r(q,{type:z(c(s,"purchase")),size:"small",class:g(["process-tag",I(c(s,"purchase"))]),effect:"plain"},{default:o(()=>[r(u(f),{icon:O(c(s,"purchase")),class:"tag-icon"},null,8,["icon"]),d(" "+l(k(c(s,"purchase"))),1)]),_:2},1032,["type","class"]),t("div",xs,[t("div",Ns,l(D(s,"purchase"))+"%",1),a[7]||(a[7]=t("div",{class:"process-divider"},null,-1)),t("div",Cs,"\u91C7\u8D2D\uFF1A"+l(fe(s)),1),t("div",Ss,l(b((p=s,p.completedDate||p.estimatedDeliveryDate))),1)])])];var p}),_:1}),r(n,{label:"\u751F\u4EA7",width:"120",align:"center"},{default:o(({row:s})=>[t("div",Qs,[r(q,{type:z(c(s,"production")),size:"small",class:g(["process-tag",I(c(s,"production"))]),effect:"plain"},{default:o(()=>[r(u(f),{icon:O(c(s,"production")),class:"tag-icon"},null,8,["icon"]),d(" "+l(k(c(s,"production"))),1)]),_:2},1032,["type","class"]),t("div",Ms,[t("div",$s,l(D(s,"production"))+"%",1),a[8]||(a[8]=t("div",{class:"process-divider"},null,-1)),t("div",Ps,"\u62A5\u5DE5\uFF1A"+l(ge(s)),1),t("div",js,l(b(E(s))),1)])])]),_:1}),r(n,{label:"\u8D28\u68C0",width:"120",align:"center"},{default:o(({row:s})=>{return[t("div",Bs,[r(q,{type:z(c(s,"quality")),size:"small",class:g(["process-tag",I(c(s,"quality"))]),effect:"plain"},{default:o(()=>[r(u(f),{icon:O(c(s,"quality")),class:"tag-icon"},null,8,["icon"]),d(" "+l(k(c(s,"quality"))),1)]),_:2},1032,["type","class"]),t("div",Us,[t("div",Ys,l(D(s,"quality"))+"%",1),a[9]||(a[9]=t("div",{class:"process-divider"},null,-1)),t("div",Fs,"\u5408\u683C\uFF1A"+l(ye(s)),1),t("div",Es,l((p=s,p.qualityInspector||p.inspector||"\u5F20\u5DE5")),1)])])];var p}),_:1}),r(n,{label:"\u5165\u5E93",width:"120",align:"center","class-name":"hidden-on-tablet"},{default:o(({row:s})=>[t("div",Vs,[r(q,{type:z(c(s,"warehouseIn")),size:"small",class:g(["process-tag",I(c(s,"warehouseIn"))]),effect:"plain"},{default:o(()=>[r(u(f),{icon:O(c(s,"warehouseIn")),class:"tag-icon"},null,8,["icon"]),d(" "+l(k(c(s,"warehouseIn"))),1)]),_:2},1032,["type","class"]),t("div",Ws,[t("div",As,l(D(s,"warehouseIn"))+"%",1),a[10]||(a[10]=t("div",{class:"process-divider"},null,-1)),t("div",Gs,"\u5165\u5E93\uFF1A"+l(me(s)),1),t("div",Hs,l(b(V(s,"warehouseIn"))),1),t("div",{class:g(["process-detail approval-status",he(W(s))])}," \u5BA1\u6838\uFF1A"+l(we(W(s))),3)])])]),_:1}),r(n,{label:"\u53D1\u8D27",align:"center","class-name":"hidden-on-mobile"},{default:o(({row:s})=>[t("div",Zs,[r(q,{type:z(c(s,"delivery")),size:"small",class:g(["process-tag",I(c(s,"delivery"))]),effect:"plain"},{default:o(()=>[r(u(f),{icon:O(c(s,"delivery")),class:"tag-icon"},null,8,["icon"]),d(" "+l(k(c(s,"delivery"))),1)]),_:2},1032,["type","class"]),t("div",Js,[t("div",Ks,l(D(s,"delivery"))+"%",1),a[11]||(a[11]=t("div",{class:"process-divider"},null,-1)),t("div",Ls,"\u901A\u77E5\uFF1A"+l(be(s)),1),t("div",Rs,l(b(V(s,"delivery"))),1)])])]),_:1}),r(n,{label:"\u51FA\u5E93",align:"center"},{default:o(({row:s})=>[t("div",Xs,[r(q,{type:z(c(s,"warehouseOut")),size:"small",class:g(["process-tag",I(c(s,"warehouseOut"))]),effect:"plain"},{default:o(()=>[r(u(f),{icon:O(c(s,"warehouseOut")),class:"tag-icon"},null,8,["icon"]),d(" "+l(k(c(s,"warehouseOut"))),1)]),_:2},1032,["type","class"]),t("div",ea,[t("div",sa,l(D(s,"warehouseOut"))+"%",1),a[12]||(a[12]=t("div",{class:"process-divider"},null,-1)),t("div",aa,"\u51FA\u5E93\uFF1A"+l(_e(s)),1),t("div",ta,l(b(E(s))),1)])])]),_:1})]),_:1},8,["data"])),[[$e,N.loading]]),t("div",la,[r(Me,{"current-page":w.value,"onUpdate:currentPage":a[0]||(a[0]=s=>w.value=s),"page-size":T.value,"onUpdate:pageSize":a[1]||(a[1]=s=>T.value=s),"page-sizes":[10,20,50,100],total:ae.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:Ie,onCurrentChange:Oe,class:"custom-pagination"},null,8,["current-page","page-size","total"])]),h.value.length>0?(B(),J("div",ra,[t("div",ia,[t("span",ca," \u5DF2\u9009\u62E9 "+l(h.value.length)+" \u6761\u8BB0\u5F55 ",1),t("div",oa,[r(i,{size:"small",onClick:Te,class:"batch-btn"},{default:o(()=>a[13]||(a[13]=[d(" \u6279\u91CF\u5BFC\u51FA ")])),_:1}),r(i,{size:"small",onClick:xe,class:"batch-btn"},{default:o(()=>a[14]||(a[14]=[d(" \u6279\u91CF\u6253\u5370 ")])),_:1}),r(i,{size:"small",type:"danger",onClick:Ne,class:"batch-btn batch-btn-danger"},{default:o(()=>a[15]||(a[15]=[d(" \u6279\u91CF\u53D6\u6D88 ")])),_:1})])])])):Re("",!0)],64)}}}),[["__scopeId","data-v-3d9a665c"]]),na=Object.freeze(Object.defineProperty({__proto__:null,default:L},Symbol.toStringTag,{value:"Module"}));export{K as O,L as T,na as a};
