import{a as C,a3 as S,a0 as T,h as D,g as E}from"./index-BeQABqnP.js";import{e as y,b as Y,f as I}from"./tree-COGD3qag.js";import{_ as A}from"./DictTag.vue_vue_type_script_lang-B0tTMPJ6.js";import{bj as w}from"./form-designer-DQFPUccF.js";import{P as H,a5 as M}from"./form-create-B86qX0W_.js";const{t:F}=C(),g=s=>{const a=H({searchSchema:[],tableColumns:[],formSchema:[],detailSchema:[]}),t=j(s,a);a.searchSchema=t||[];const l=x(s);a.tableColumns=l||[];const o=N(s,a);a.formSchema=o;const i=_(s);return a.detailSchema=i,{allSchemas:a}},j=(s,a)=>{const t=[],l=[];y(s,o=>{var i,m,d,p;if(o!=null&&o.isSearch||(i=o.search)!=null&&i.show){let u=((m=o==null?void 0:o.search)==null?void 0:m.component)||"Input";const h=[];let e={};if(o.dictType){const r={label:"\u5168\u90E8",value:""};h.push(r),S(o.dictType).forEach(c=>{h.push(c)}),e={options:h},(d=o.search)!=null&&d.component||(u="Select")}const f=w({component:u,...o.search,field:o.field,label:((p=o.search)==null?void 0:p.label)||o.label},{componentProps:e});f.api&&l.push(async()=>{var c;const r=await f.api();if(r){const n=T(a.searchSchema,b=>b.field===f.field);n!==-1&&(a.searchSchema[n].componentProps.options=v(r,(c=f.componentProps.optionsAlias)==null?void 0:c.labelField))}}),delete f.show,t.push(f)}});for(const o of l)o();return t},x=s=>{const a=Y(s,{conversion:t=>{var l;if((t==null?void 0:t.isTable)!==!1&&((l=t==null?void 0:t.table)==null?void 0:l.show)!==!1)return!t.formatter&&t.dictType&&(t.formatter=(o,i,m)=>M(A,{type:t.dictType,value:m})),{...t.table,...t}}});return I(a,t=>(t.children===void 0&&delete t.children,!!t.field))},N=(s,a)=>{const t=[],l=[];y(s,o=>{var i,m,d,p,u;if((o==null?void 0:o.isForm)!==!1&&((i=o==null?void 0:o.form)==null?void 0:i.show)!==!1){let h=((m=o==null?void 0:o.form)==null?void 0:m.component)||"Input",e="";(d=o.form)!=null&&d.value?e=(p=o.form)==null?void 0:p.value:h==="InputNumber"&&(e=0);let f={};if(o.dictType){const c=[];o.dictClass&&o.dictClass==="number"?D(o.dictType).forEach(n=>{c.push(n)}):o.dictClass&&o.dictClass==="boolean"?E(o.dictType).forEach(n=>{c.push(n)}):S(o.dictType).forEach(n=>{c.push(n)}),f={options:c},o.form&&o.form.component||(h="Select")}const r=w({component:h,value:e,...o.form,field:o.field,label:((u=o.form)==null?void 0:u.label)||o.label},{componentProps:f});r.api&&l.push(async()=>{var n;const c=await r.api();if(c){const b=T(a.formSchema,P=>P.field===r.field);b!==-1&&(a.formSchema[b].componentProps.options=v(c,(n=r.componentProps.optionsAlias)==null?void 0:n.labelField))}}),delete r.show,t.push(r)}});for(const o of l)o();return t},_=s=>{const a=[];return y(s,t=>{var l,o,i,m,d;if((t==null?void 0:t.isDetail)!==!1&&((l=t.detail)==null?void 0:l.show)!==!1){const p={...t.detail,field:t.field,label:((o=t.detail)==null?void 0:o.label)||t.label};t.dictType&&(p.dictType=t.dictType),((i=t.detail)!=null&&i.dateFormat||t.formatter=="formatDate")&&(p.dateFormat=(m=t==null?void 0:t.detail)!=null&&m.dateFormat?(d=t==null?void 0:t.detail)==null?void 0:d.dateFormat:"YYYY-MM-DD HH:mm:ss"),delete p.show,a.push(p)}}),a},v=(s,a)=>s==null?void 0:s.map(t=>(a?t.labelField=F(t.labelField):t.label=F(t.label),t));export{g as u};
