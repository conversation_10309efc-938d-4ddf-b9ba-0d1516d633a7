import{W as f,D as b}from"./index-BeQABqnP.js";import{_ as w}from"./DictTag.vue_vue_type_script_lang-B0tTMPJ6.js";import{d as g}from"./formatTime-CN67D7Gb.js";import{_ as y,Z as _,ak as h}from"./form-designer-DQFPUccF.js";import{k as x,r as l,e as v,A,y as I,m as S,z as o,H as a,u as r}from"./form-create-B86qX0W_.js";const N=x({__name:"UserAddressList",props:{userId:{type:Number,required:!0}},setup(i){const t=l(!0);l(0);const s=l([]),n=async()=>{t.value=!0;try{s.value=await(async p=>await f.get({url:"/member/address/list",params:p}))({userId:i.userId})}finally{t.value=!1}};return v(()=>{n()}),(p,T)=>{const e=y,d=w,u=_,m=h;return A((S(),I(u,{data:r(s),stripe:!0,"show-overflow-tooltip":!0},{default:o(()=>[a(e,{label:"\u5730\u5740\u7F16\u53F7",align:"center",prop:"id",width:"150px"}),a(e,{label:"\u6536\u4EF6\u4EBA\u540D\u79F0",align:"center",prop:"name",width:"150px"}),a(e,{label:"\u624B\u673A\u53F7",align:"center",prop:"mobile",width:"150px"}),a(e,{label:"\u5730\u533A\u7F16\u7801",align:"center",prop:"areaId",width:"150px"}),a(e,{label:"\u6536\u4EF6\u8BE6\u7EC6\u5730\u5740",align:"center",prop:"detailAddress"}),a(e,{label:"\u662F\u5426\u9ED8\u8BA4",align:"center",prop:"defaultStatus",width:"150px"},{default:o(c=>[a(d,{type:r(b).COMMON_STATUS,value:Number(c.row.defaultStatus)},null,8,["type","value"])]),_:1}),a(e,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",formatter:r(g),width:"180px"},null,8,["formatter"])]),_:1},8,["data"])),[[m,r(t)]])}}});export{N as _};
