import{_ as aa,c as ea}from"./index-BeQABqnP.js";import{R as la}from"./index-Ufxn2eGP.js";import{B as ta}from"./index-CH8pxbb6.js";import{g as na,a as ia}from"./commonBiz-Dnw63na0.js";import{f as oa,p as da,g as S,c as x,B as ua,a as ra,C as sa}from"./bomCalculation-DYv5_fDl.js";import{h as pa,_ as ca,aj as ma,k as ya,x as fa,Q as va,Z as Qa,ak as ba,f as ha,i as wa}from"./form-designer-DQFPUccF.js";import{k as _a,r as Q,P as Va,b as C,e as Ia,n as O,l as T,m as h,G as B,A as ga,H as i,y as q,u as c,z as d,v as f,F as E,$ as Sa,E as j}from"./form-create-B86qX0W_.js";import"./index-D4qK--X-.js";import"./index-BDU5cx5r.js";import"./index-CASHthoJ.js";import"./index-BTKSadna.js";const xa={class:"material-warehouse-cell"},Oa={class:"info-row"},Ua={class:"info-value"},za={class:"info-row"},$a={class:"info-value"},ka=ea(_a({__name:"RequestOrderDetailForm",props:{bizOrderId:{},bizOrderNo:{},bomId:{},slotQuantity:{},orderQuantity:{},orderSpecQuantity:{}},setup(D,{expose:L}){const s=D,w=Q(!1),u=Q([]),y=Va({}),U=Q(),z=async(a,e)=>ta.getBomMaterialListByBomId(a,e);C(()=>({orderId:s.bizOrderId,bomId:s.bomId}),async({orderId:a,bomId:e})=>{if(u.value=[],a)try{if(w.value=!0,u.value=await la.getRequestOrderDetailListByBizOrderId(a),u.value.length<=0&&e){const t=await z(e,"");u.value=t.map(l=>({...l,bizOrderId:s.bizOrderId,bizOrderNo:s.bizOrderNo,unit:l.materialUnit,slotQuantity:l.quantity||0,plannedQuantity:v({slotQuantity:l.quantity||0})}))}$(),await O(),_()}finally{w.value=!1}else if(e){const t=await z(e,"");u.value=t.map(l=>({...l,bizOrderId:s.bizOrderId,bizOrderNo:s.bizOrderNo,unit:l.materialUnit,slotQuantity:l.quantity||0,plannedQuantity:v({slotQuantity:l.quantity||0})})),O(()=>{_()})}},{immediate:!0});const F=()=>{const a={id:void 0,num:(u.value.length+1).toString(),bizOrderId:s.bizOrderId,bizOrderNo:s.bizOrderNo,warehouseId:void 0,locationId:void 0,materialId:void 0,materialName:void 0,materialCode:void 0,spec:void 0,unit:void 0,unitPrice:void 0,amount:void 0,remark:void 0,fulfilledQuantity:void 0,note:void 0,batchNo:void 0,lossRate:void 0,lossQuantity:void 0,readyStatus:void 0,readyQuantity:void 0,lockedQuantity:void 0,slotQuantity:0,slotSpecQuantity:0,plannedQuantity:v(0),plannedSpecQuantity:0};a.bizOrderId=s.bizOrderId,u.value.push(a)},$=()=>{u.value.forEach((a,e)=>{a.num=(e+1).toString()})},P=a=>{if(!a)return"-";const e=a.materialCode||"",t=a.materialName||"",l=[];return e&&l.push(e),t&&l.push(t),l.length>0?l.join(" - "):"-"},Y=a=>{if(!a)return"-";const e=N.value.find(t=>t.id==a);return e?e.name:"-"},k=()=>{u.value&&Array.isArray(u.value)&&u.value.forEach(a=>{a&&(a.slotSpecQuantity=g(a),a.plannedQuantity=v(a),a.plannedSpecQuantity=b(a))})},_=()=>{k()};L({validate:()=>U.value.validate(),getData:()=>u.value,recalculateAll:k});const m=Q([]),N=Q([]);Ia(async()=>{m.value=await na(),N.value=await ia(),await O(),_()});const R=()=>{const a=s.orderQuantity||0,e=s.slotQuantity&&s.slotQuantity>0?s.slotQuantity:ua.DEFAULT_SLOT_QUANTITY;return ra(a,e)},v=a=>{const e=typeof a=="object"?a.slotQuantity||0:a||0,t=R();return oa(e*t,sa.QUANTITY_PRECISION)},V=da,I=a=>{if(!a)return"\u4E2A";if(!m.value||m.value.length===0)return A(a);const e=m.value.find(l=>l.id===a);return(e?e.name:"")||A(a)},A=a=>{if(m.value&&m.value.length>0){const e=m.value.find(t=>t.id===a);if(e&&e.name)return e.name}return"\u4E2A"},g=a=>{const e=V(a.spec||""),t=a.slotQuantity||0;let l="";if(m.value&&m.value.length>0&&a.unit)l=I(a.unit);else{if(a.unit)return t||1;l="\u4E2A"}const p=S(l);return!e.value||e.value<=0?t||1:x(t,e,p,l).displayText},b=a=>{const e=R();if(a.slotSpecQuantity!==void 0&&a.slotSpecQuantity!==null&&a.slotSpecQuantity!==0||(a.slotSpecQuantity=g(a)),typeof a.slotSpecQuantity=="string"){const t=V(a.spec||""),l=I(a.unit||0),p=S(l);return x((a.slotQuantity||0)*e,t,p,l).displayText}{const t=V(a.spec||""),l=I(a.unit||0),p=S(l);return x((a.slotQuantity||0)*e,t,p,l).displayText}};return C(()=>[s.slotQuantity,s.orderQuantity,s.orderSpecQuantity],()=>{u.value&&Array.isArray(u.value)&&u.value.forEach(a=>{a&&(a.plannedQuantity=v(a),a.plannedSpecQuantity=b(a))})},{immediate:!0}),(a,e)=>{const t=ca,l=ya,p=ma,M=va,G=fa,H=aa,Z=Qa,J=pa,K=ha,W=wa,X=ba;return h(),T(B,null,[ga((h(),q(J,{ref_key:"formRef",ref:U,model:c(u),rules:c(y),"label-width":"0px","inline-message":!0},{default:d(()=>[i(Z,{data:c(u),class:"-mt-10px",border:""},{default:d(()=>[i(t,{label:"\u5E8F\u53F7","min-width":"60",align:"center",prop:"num"}),i(t,{label:"\u7269\u6599\u4FE1\u606F","min-width":"320",align:"center"},{default:d(({row:o})=>[f("div",xa,[f("div",Oa,[e[0]||(e[0]=f("span",{class:"info-label"},"\u7269\u6599:",-1)),f("span",Ua,E(P(o)),1)]),f("div",za,[e[1]||(e[1]=f("span",{class:"info-label"},"\u4ED3\u5E93:",-1)),f("span",$a,E(Y(o.warehouseId)),1)])])]),_:1}),i(t,{label:"\u89C4\u683C","min-width":"150",align:"center"},{default:d(({row:o,$index:r})=>[i(p,{prop:`${r}.spec`,rules:c(y).spec,class:"mb-0px!"},{default:d(()=>[i(l,{modelValue:o.spec,"onUpdate:modelValue":n=>o.spec=n,placeholder:"\u8BF7\u8F93\u5165\u89C4\u683C",disabled:""},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])]),_:1}),i(t,{label:"\u5355\u4F4D","min-width":"100",align:"center"},{default:d(({row:o,$index:r})=>[i(p,{prop:`${r}.unit`,rules:c(y).unit,class:"mb-0px!"},{default:d(()=>[i(G,{modelValue:o.unit,"onUpdate:modelValue":n=>o.unit=n,placeholder:"\u8BF7\u9009\u62E9\u8BA2\u5355\u5355\u4F4D",class:"!w-80px",disabled:""},{default:d(()=>[(h(!0),T(B,null,Sa(c(m),n=>(h(),q(M,{key:n.id,label:n.name,value:n.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])]),_:1}),i(t,{label:"\u6BCF\u69FD\u7528\u91CF","min-width":"150",align:"center"},{default:d(({row:o})=>[i(p,{class:"mb-0px!"},{default:d(()=>[i(l,{modelValue:o.slotQuantity,"onUpdate:modelValue":r=>o.slotQuantity=r,modelModifiers:{number:!0},onChange:r=>(n=>{n.plannedQuantity=v(n),(n.slotSpecQuantity===void 0||n.slotSpecQuantity===null||n.slotSpecQuantity===0)&&(n.slotSpecQuantity=g(n)),n.plannedSpecQuantity=b(n)})(o),placeholder:"\u6BCF\u69FD\u7528\u91CF"},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:2},1024)]),_:1}),i(t,{label:"\u603B\u7528\u91CF","min-width":"150",align:"center"},{default:d(({row:o,$index:r})=>[i(p,{prop:`${r}.plannedQuantity`,rules:c(y).plannedQuantity,class:"mb-0px!"},{default:d(()=>[i(l,{modelValue:o.plannedQuantity,"onUpdate:modelValue":n=>o.plannedQuantity=n,placeholder:"\u8BF7\u8F93\u5165\u603B\u7528\u91CF"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])]),_:1}),i(t,{label:"\u603B\u89C4\u683C\u7528\u91CF","min-width":"150",align:"center"},{default:d(({row:o,$index:r})=>[i(p,{prop:`${r}.plannedSpecQuantity`,rules:c(y).plannedSpecQuantity,class:"mb-0px!"},{default:d(()=>[i(l,{modelValue:o.plannedSpecQuantity,"onUpdate:modelValue":n=>o.plannedSpecQuantity=n,placeholder:"\u8BF7\u8F93\u5165\u603B\u89C4\u683C\u7528\u91CF"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])]),_:1}),i(t,{label:"\u635F\u8017\u7387","min-width":"150",align:"center"},{default:d(({row:o,$index:r})=>[i(p,{prop:`${r}.lossRate`,rules:c(y).lossRate,class:"mb-0px!"},{default:d(()=>[i(l,{modelValue:o.lossRate,"onUpdate:modelValue":n=>o.lossRate=n,placeholder:"\u8BF7\u8F93\u5165\u635F\u8017\u7387",type:"number"},{append:d(()=>e[2]||(e[2]=[j(" % ")])),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])]),_:1}),i(t,{label:"\u5907\u6CE8","min-width":"150",align:"center"},{default:d(({row:o,$index:r})=>[i(p,{prop:`${r}.remark`,rules:c(y).remark,class:"mb-0px!"},{default:d(()=>[i(l,{modelValue:o.remark,"onUpdate:modelValue":n=>o.remark=n,placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])]),_:1}),i(t,{label:"\u5E93\u4F4D","min-width":"150",align:"center"},{default:d(({row:o,$index:r})=>[i(p,{prop:`${r}.locationId`,rules:c(y).locationId,class:"mb-0px!"},{default:d(()=>[i(l,{modelValue:o.locationId,"onUpdate:modelValue":n=>o.locationId=n,placeholder:"\u8BF7\u8F93\u5165\u5E93\u4F4D"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])]),_:1}),i(t,{align:"center",fixed:"right",label:"\u64CD\u4F5C",width:"60"},{default:d(({$index:o})=>[i(H,{icon:"ep:delete",color:"#f56c6c",onClick:r=>{return n=o,u.value.splice(n,1),void $();var n}},null,8,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1},8,["model","rules"])),[[X,c(w)]]),i(W,{justify:"center",class:"mt-3"},{default:d(()=>[i(K,{onClick:F,round:""},{default:d(()=>e[3]||(e[3]=[j("+ \u6DFB\u52A0\u751F\u4EA7\u8BA2\u5355\u660E\u7EC6")])),_:1})]),_:1})],64)}}}),[["__scopeId","data-v-3cffc040"]]);export{ka as default};
