import{u as B,a3 as g,D as m,_ as J,H as X,L as ee}from"./index-BeQABqnP.js";import{_ as le}from"./index.vue_vue_type_script_setup_true_lang-BjqH9dXd.js";import{_ as ae}from"./DictTag.vue_vue_type_script_lang-B0tTMPJ6.js";import{_ as te}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CxohUsTD.js";import{k as ue,r as c,e as re,l as _,m as s,G as f,H as l,z as u,u as a,Z as S,$ as w,y as b,E as T,A as oe,F as v,v as k}from"./form-create-B86qX0W_.js";import{e as se}from"./index-CMwfoluH.js";import{f as pe}from"./formatTime-CN67D7Gb.js";import{h as de,aj as ne,k as ie,x as me,Q as ce,F as _e,f as fe,a0 as be,$ as ve,_ as ye,al as we,a6 as Te,Z as Ae,ak as Ve,K as he}from"./form-designer-DQFPUccF.js";const ge={class:"flex items-center"},ke={class:"mr-10px"},xe=ue({name:"UserAfterSaleList",__name:"UserAftersaleList",props:{userId:{}},setup(P){const{push:U}=B(),D=P,x=c(!0),R=c(0),F=c([]),L=c([{label:"\u5168\u90E8",value:"0"}]),C=c(),r=c({pageNo:1,pageSize:10,no:null,status:"0",orderNo:null,spuName:null,createTime:[],way:null,type:null,userId:null}),A=async()=>{x.value=!0;try{const o=he(r.value);o.status==="0"&&delete o.status,D.userId&&(o.userId=D.userId);const t=await se(o);F.value=t.list,R.value=t.total}finally{x.value=!1}},y=async()=>{r.value.pageNo=1,await A()},z=()=>{var o;(o=C.value)==null||o.resetFields(),y()},K=async o=>{r.value.status=o.paneName,await A()};return re(async()=>{await A();for(const o of g(m.TRADE_AFTER_SALE_STATUS))L.value.push({label:o.label,value:o.value})}),(o,t)=>{const E=ie,p=ne,V=ce,N=me,M=_e,I=J,h=fe,$=de,Y=te,W=ve,Z=be,d=ye,j=we,q=Te,H=ae,G=Ae,O=le,Q=Ve;return s(),_(f,null,[l(Y,null,{default:u(()=>[l($,{ref_key:"queryFormRef",ref:C,inline:!0,model:a(r),"label-width":"68px"},{default:u(()=>[l(p,{label:"\u5546\u54C1\u540D\u79F0",prop:"spuName"},{default:u(()=>[l(E,{modelValue:a(r).spuName,"onUpdate:modelValue":t[0]||(t[0]=e=>a(r).spuName=e),class:"!w-280px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u5546\u54C1 SPU \u540D\u79F0",onKeyup:S(y,["enter"])},null,8,["modelValue"])]),_:1}),l(p,{label:"\u9000\u6B3E\u7F16\u53F7",prop:"no"},{default:u(()=>[l(E,{modelValue:a(r).no,"onUpdate:modelValue":t[1]||(t[1]=e=>a(r).no=e),class:"!w-280px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u9000\u6B3E\u7F16\u53F7",onKeyup:S(y,["enter"])},null,8,["modelValue"])]),_:1}),l(p,{label:"\u8BA2\u5355\u7F16\u53F7",prop:"orderNo"},{default:u(()=>[l(E,{modelValue:a(r).orderNo,"onUpdate:modelValue":t[2]||(t[2]=e=>a(r).orderNo=e),class:"!w-280px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u8BA2\u5355\u7F16\u53F7",onKeyup:S(y,["enter"])},null,8,["modelValue"])]),_:1}),l(p,{label:"\u552E\u540E\u72B6\u6001",prop:"status"},{default:u(()=>[l(N,{modelValue:a(r).status,"onUpdate:modelValue":t[3]||(t[3]=e=>a(r).status=e),class:"!w-280px",clearable:"",placeholder:"\u8BF7\u9009\u62E9\u552E\u540E\u72B6\u6001"},{default:u(()=>[l(V,{label:"\u5168\u90E8",value:"0"}),(s(!0),_(f,null,w(a(g)(a(m).TRADE_AFTER_SALE_STATUS),e=>(s(),b(V,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(p,{label:"\u552E\u540E\u65B9\u5F0F",prop:"way"},{default:u(()=>[l(N,{modelValue:a(r).way,"onUpdate:modelValue":t[4]||(t[4]=e=>a(r).way=e),class:"!w-280px",clearable:"",placeholder:"\u8BF7\u9009\u62E9\u552E\u540E\u65B9\u5F0F"},{default:u(()=>[(s(!0),_(f,null,w(a(g)(a(m).TRADE_AFTER_SALE_WAY),e=>(s(),b(V,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(p,{label:"\u552E\u540E\u7C7B\u578B",prop:"type"},{default:u(()=>[l(N,{modelValue:a(r).type,"onUpdate:modelValue":t[5]||(t[5]=e=>a(r).type=e),class:"!w-280px",clearable:"",placeholder:"\u8BF7\u9009\u62E9\u552E\u540E\u7C7B\u578B"},{default:u(()=>[(s(!0),_(f,null,w(a(g)(a(m).TRADE_AFTER_SALE_TYPE),e=>(s(),b(V,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(p,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime"},{default:u(()=>[l(M,{modelValue:a(r).createTime,"onUpdate:modelValue":t[6]||(t[6]=e=>a(r).createTime=e),"default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-280px","end-placeholder":"\u81EA\u5B9A\u4E49\u65F6\u95F4","start-placeholder":"\u81EA\u5B9A\u4E49\u65F6\u95F4",type:"daterange","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue","default-time"])]),_:1}),l(p,null,{default:u(()=>[l(h,{onClick:y},{default:u(()=>[l(I,{class:"mr-5px",icon:"ep:search"}),t[10]||(t[10]=T(" \u641C\u7D22 "))]),_:1}),l(h,{onClick:z},{default:u(()=>[l(I,{class:"mr-5px",icon:"ep:refresh"}),t[11]||(t[11]=T(" \u91CD\u7F6E "))]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),l(Y,null,{default:u(()=>[l(Z,{modelValue:a(r).status,"onUpdate:modelValue":t[7]||(t[7]=e=>a(r).status=e),onTabClick:K},{default:u(()=>[(s(!0),_(f,null,w(a(L),e=>(s(),b(W,{key:e.label,label:e.label,name:e.value},null,8,["label","name"]))),128))]),_:1},8,["modelValue"]),oe((s(),b(G,{data:a(F)},{default:u(()=>[l(d,{align:"center",label:"\u9000\u6B3E\u7F16\u53F7","min-width":"200",prop:"no"}),l(d,{align:"center",label:"\u8BA2\u5355\u7F16\u53F7","min-width":"200",prop:"orderNo"},{default:u(({row:e})=>[l(h,{link:"",type:"primary",onClick:i=>{return n=e.orderId,void U({name:"TradeOrderDetail",params:{id:n}});var n}},{default:u(()=>[T(v(e.orderNo),1)]),_:2},1032,["onClick"])]),_:1}),l(d,{label:"\u5546\u54C1\u4FE1\u606F","min-width":"600",prop:"spuName"},{default:u(({row:e})=>[k("div",ge,[l(j,{src:e.picUrl,class:"mr-10px h-30px w-30px",onClick:i=>{return n=e.picUrl,void ee({urlList:[n]});var n}},null,8,["src","onClick"]),k("span",ke,v(e.spuName),1),(s(!0),_(f,null,w(e.properties,i=>(s(),b(q,{key:i.propertyId,class:"mr-10px"},{default:u(()=>[T(v(i.propertyName)+": "+v(i.valueName),1)]),_:2},1024))),128))])]),_:1}),l(d,{align:"center",label:"\u8BA2\u5355\u91D1\u989D","min-width":"120",prop:"refundPrice"},{default:u(e=>[k("span",null,v(a(X)(e.row.refundPrice))+" \u5143",1)]),_:1}),l(d,{align:"center",label:"\u7533\u8BF7\u65F6\u95F4",prop:"createTime",width:"180"},{default:u(e=>[k("span",null,v(a(pe)(e.row.createTime)),1)]),_:1}),l(d,{align:"center",label:"\u552E\u540E\u72B6\u6001",width:"100"},{default:u(e=>[l(H,{type:a(m).TRADE_AFTER_SALE_STATUS,value:e.row.status},null,8,["type","value"])]),_:1}),l(d,{align:"center",label:"\u552E\u540E\u65B9\u5F0F"},{default:u(e=>[l(H,{type:a(m).TRADE_AFTER_SALE_WAY,value:e.row.way},null,8,["type","value"])]),_:1}),l(d,{align:"center",fixed:"right",label:"\u64CD\u4F5C",width:"120"},{default:u(({row:e})=>[l(h,{link:"",type:"primary",onClick:i=>{return n=e.id,void U({name:"TradeAfterSaleDetail",params:{id:n}});var n}},{default:u(()=>t[12]||(t[12]=[T("\u5904\u7406\u9000\u6B3E")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[Q,a(x)]]),l(O,{limit:a(r).pageSize,"onUpdate:limit":t[8]||(t[8]=e=>a(r).pageSize=e),page:a(r).pageNo,"onUpdate:page":t[9]||(t[9]=e=>a(r).pageNo=e),total:a(R),onPagination:A},null,8,["limit","page","total"])]),_:1})],64)}}});export{xe as _};
