import{N as E,__tla as j}from"./index-BeQABqnP.js";import{_ as P}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CxohUsTD.js";import{W}from"./index-B3LrXjfm.js";import{f as u}from"./formatter-BLTmz7GT.js";import{g as F,d as z}from"./commonBiz-Dnw63na0.js";import{Z as D,_ as L,ak as M}from"./form-designer-DQFPUccF.js";import{k as B,r as y,b as T,y as v,m as N,z as i,A as U,u as p,H as r,E as s,F as d}from"./form-create-B86qX0W_.js";import"./index-D4qK--X-.js";import"./index-BDU5cx5r.js";import"./index-CASHthoJ.js";import"./index-BTKSadna.js";let I,C=Promise.all([(()=>{try{return j}catch{}})()]).then(async()=>{I=B({__name:"WorkOrderDetailTable",props:{workOrderId:{}},setup(O,{expose:k}){const w=O,g=y(!1),c=y([]),h=y(new Map),m=y(new Map),_=t=>{if(!t)return"";if(typeof t=="string"&&isNaN(Number(t)))return t;const l=typeof t=="string"?parseInt(t):t;return h.value.get(l)||t.toString()},S=t=>{if(!t)return"";if(typeof t=="string"&&isNaN(Number(t)))return t;const l=typeof t=="string"?parseInt(t):t;return m.value.get(l)||`\u4ED3\u5E93${t}`},Q=async()=>{if(w.workOrderId){g.value=!0;try{const t=await W.getWorkOrderDetailListByBizOrderId(w.workOrderId);Array.isArray(t)?c.value=t:c.value=[]}catch{c.value=[]}finally{g.value=!1}}},x=t=>{const{columns:l,data:a}=t,n=[];return l.forEach((o,f)=>{if(f===0)return void(n[f]="\u5408\u8BA1");if(["plannedQuantity","fulfilledQuantity","standardPlannedQuantity","standardFulfilledQuantity","plannedSpecQuantity","fulfilledSpecQuantity"].includes(o.property)){const e=a.map(b=>Number(b[o.property])||0).reduce((b,A)=>b+A,0);n[f]=e>0?u(e):""}else n[f]=""}),n};return T(()=>w.workOrderId,async t=>{t?(await(async()=>{try{const l=await F();if(!l||l.length===0)return;l.forEach(a=>{a&&a.id&&a.name&&h.value.set(a.id,a.name)})}catch{}})(),await(async()=>{try{const l=await z();if(l&&typeof l=="object")Object.keys(l).forEach(a=>{const n=l[a];n&&n.name&&m.value.set(parseInt(a),n.name)});else{const{WarehouseApi:a}=await E(()=>import("./index-CASHthoJ.js"),__vite__mapDeps([0,1,2,3,4])),n=await a.getWarehouseList({pageNo:1,pageSize:100});Array.isArray(n)&&n.forEach(o=>{o&&o.id&&o.name&&m.value.set(o.id,o.name)})}}catch{m.value||(m.value=new Map)}})(),await Q()):c.value=[]},{immediate:!0}),k({refresh:Q}),(t,l)=>{const a=L,n=D,o=P,f=M;return N(),v(o,null,{default:i(()=>[U((N(),v(n,{data:p(c),stripe:!0,"show-overflow-tooltip":!0,"show-summary":"","summary-method":x,border:"","max-height":p(c).length>0?600:200,style:{width:"100%"}},{default:i(()=>[r(a,{label:"\u5E8F\u53F7",align:"center",prop:"num",width:"70"}),r(a,{label:"\u4ED3\u5E93",align:"center",width:"120px"},{default:i(e=>[s(d(S(e.row.warehouseId)||"-"),1)]),_:1}),r(a,{label:"\u7269\u6599\u540D\u79F0",align:"left",prop:"materialName"}),r(a,{label:"\u7269\u6599\u7F16\u53F7",align:"center",prop:"materialCode"}),r(a,{label:"\u89C4\u683C",align:"center",prop:"spec"}),r(a,{label:"\u5355\u4F4D",align:"center",width:"80px"},{default:i(e=>[s(d(_(e.row.unit)||e.row.unit||""),1)]),_:1}),r(a,{label:"\u5907\u6CE8",align:"center",prop:"remark"}),r(a,{label:"\u8BA1\u5212\u6570\u91CF",align:"center",prop:"plannedQuantity"},{default:i(e=>[s(d(p(u)(e.row.plannedQuantity)),1)]),_:1}),r(a,{label:"\u5C65\u7EA6\u6570\u91CF",align:"center",prop:"fulfilledQuantity"},{default:i(e=>[s(d(p(u)(e.row.fulfilledQuantity)),1)]),_:1}),r(a,{label:"\u57FA\u672C\u5355\u4F4D\u8BA1\u5212\u6570\u91CF",align:"center",prop:"standardPlannedQuantity"},{default:i(e=>[s(d(p(u)(e.row.standardPlannedQuantity)),1)]),_:1}),r(a,{label:"\u57FA\u672C\u5355\u4F4D\u5C65\u7EA6\u6570\u91CF",align:"center",prop:"standardFulfilledQuantity"},{default:i(e=>[s(d(p(u)(e.row.standardFulfilledQuantity)),1)]),_:1}),r(a,{label:"\u57FA\u672C\u5355\u4F4D",align:"center",width:"100px"},{default:i(e=>[s(d(_(e.row.standardUnit)||e.row.standardUnit||""),1)]),_:1}),r(a,{label:"\u89C4\u683C\u6570\u91CF",align:"center",prop:"plannedSpecQuantity"},{default:i(e=>[s(d(p(u)(e.row.plannedSpecQuantity)),1)]),_:1}),r(a,{label:"\u5C65\u7EA6\u89C4\u683C\u6570\u91CF",align:"center",prop:"fulfilledSpecQuantity"},{default:i(e=>[s(d(p(u)(e.row.fulfilledSpecQuantity)),1)]),_:1}),r(a,{label:"\u8BF4\u660E",align:"center",prop:"note"}),r(a,{label:"\u6279\u53F7",align:"center",prop:"batchNo"}),r(a,{label:"\u6210\u672C\u5BF9\u8C61\u7F16\u7801",align:"center",prop:"costObjectId"}),r(a,{label:"\u6210\u672C\u5BF9\u8C61\u540D\u79F0",align:"center",prop:"costObjectName"})]),_:1},8,["data","max-height"])),[[f,p(g)]])]),_:1})}}})});export{C as __tla,I as default};
function __vite__mapDeps(indexes) {
  if (!__vite__mapDeps.viteFileDeps) {
    __vite__mapDeps.viteFileDeps = ["assets/index-CASHthoJ.js","assets/index-BeQABqnP.js","assets/form-designer-DQFPUccF.js","assets/form-create-B86qX0W_.js","assets/index-nemfNeNP.css"]
  }
  return indexes.map((i) => __vite__mapDeps.viteFileDeps[i])
}
