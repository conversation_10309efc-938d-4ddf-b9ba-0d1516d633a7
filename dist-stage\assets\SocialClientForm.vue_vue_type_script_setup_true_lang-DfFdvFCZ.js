import{W as v,a as R,d as z,h as T,D as S}from"./index-BeQABqnP.js";import{_ as W}from"./Dialog.vue_vue_type_style_index_0_lang-DpoeWsKb.js";import{h as G,aj as H,k as K,s as L,u as N,ak as $,f as B}from"./form-designer-DQFPUccF.js";import{k as J,r as m,P as Q,y as p,m as o,z as s,A as X,u as a,H as u,C as Z,l as I,G as k,$ as w,E as y,F as U,h as ee}from"./form-create-B86qX0W_.js";const le=async f=>await v.get({url:"/system/social-client/page",params:f}),ae=async f=>await v.delete({url:"/system/social-client/delete?id="+f}),te=J({__name:"SocialClientForm",emits:["success"],setup(f,{expose:x,emit:A}){const{t:_}=R(),h=z(),r=m(!1),C=m(""),c=m(!1),q=m(""),t=m({id:void 0,name:void 0,socialType:void 0,userType:void 0,clientId:void 0,clientSecret:void 0,agentId:void 0,status:0}),F=Q({name:[{required:!0,message:"\u5E94\u7528\u540D\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],socialType:[{required:!0,message:"\u793E\u4EA4\u5E73\u53F0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],userType:[{required:!0,message:"\u7528\u6237\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],clientId:[{required:!0,message:"\u5BA2\u6237\u7AEF\u7F16\u53F7\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],clientSecret:[{required:!0,message:"\u5BA2\u6237\u7AEF\u5BC6\u94A5\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],status:[{required:!0,message:"\u72B6\u6001\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),g=m();x({open:async(d,e)=>{if(r.value=!0,C.value=_("action."+d),q.value=d,P(),e){c.value=!0;try{t.value=await(async n=>await v.get({url:"/system/social-client/get?id="+n}))(e)}finally{c.value=!1}}}});const M=A,O=async()=>{if(g&&await g.value.validate()){c.value=!0;try{const d=t.value;q.value==="create"?(await(async e=>await v.post({url:"/system/social-client/create",data:e}))(d),h.success(_("common.createSuccess"))):(await(async e=>await v.put({url:"/system/social-client/update",data:e}))(d),h.success(_("common.updateSuccess"))),r.value=!1,M("success")}finally{c.value=!1}}},P=()=>{var d;t.value={id:void 0,name:void 0,socialType:void 0,userType:void 0,clientId:void 0,clientSecret:void 0,agentId:void 0,status:0},(d=g.value)==null||d.resetFields()};return(d,e)=>{const n=K,i=H,V=N,b=L,Y=G,E=B,j=W,D=$;return o(),p(j,{modelValue:a(r),"onUpdate:modelValue":e[8]||(e[8]=l=>ee(r)?r.value=l:null),title:a(C)},{footer:s(()=>[u(E,{disabled:a(c),type:"primary",onClick:O},{default:s(()=>e[9]||(e[9]=[y("\u786E \u5B9A")])),_:1},8,["disabled"]),u(E,{onClick:e[7]||(e[7]=l=>r.value=!1)},{default:s(()=>e[10]||(e[10]=[y("\u53D6 \u6D88")])),_:1})]),default:s(()=>[X((o(),p(Y,{ref_key:"formRef",ref:g,model:a(t),rules:a(F),"label-width":"120px"},{default:s(()=>[u(i,{label:"\u5E94\u7528\u540D",prop:"name"},{default:s(()=>[u(n,{modelValue:a(t).name,"onUpdate:modelValue":e[0]||(e[0]=l=>a(t).name=l),placeholder:"\u8BF7\u8F93\u5165\u5E94\u7528\u540D"},null,8,["modelValue"])]),_:1}),u(i,{label:"\u793E\u4EA4\u5E73\u53F0",prop:"socialType"},{default:s(()=>[u(b,{modelValue:a(t).socialType,"onUpdate:modelValue":e[1]||(e[1]=l=>a(t).socialType=l)},{default:s(()=>[(o(!0),I(k,null,w(a(T)(a(S).SYSTEM_SOCIAL_TYPE),l=>(o(),p(V,{key:l.value,value:l.value},{default:s(()=>[y(U(l.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),u(i,{label:"\u7528\u6237\u7C7B\u578B",prop:"userType"},{default:s(()=>[u(b,{modelValue:a(t).userType,"onUpdate:modelValue":e[2]||(e[2]=l=>a(t).userType=l)},{default:s(()=>[(o(!0),I(k,null,w(a(T)(a(S).USER_TYPE),l=>(o(),p(V,{key:l.value,value:l.value},{default:s(()=>[y(U(l.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),u(i,{label:"\u5BA2\u6237\u7AEF\u7F16\u53F7",prop:"clientId"},{default:s(()=>[u(n,{modelValue:a(t).clientId,"onUpdate:modelValue":e[3]||(e[3]=l=>a(t).clientId=l),placeholder:"\u8BF7\u8F93\u5165\u5BA2\u6237\u7AEF\u7F16\u53F7,\u5BF9\u5E94\u5404\u5E73\u53F0\u7684appKey"},null,8,["modelValue"])]),_:1}),u(i,{label:"\u5BA2\u6237\u7AEF\u5BC6\u94A5",prop:"clientSecret"},{default:s(()=>[u(n,{modelValue:a(t).clientSecret,"onUpdate:modelValue":e[4]||(e[4]=l=>a(t).clientSecret=l),placeholder:"\u8BF7\u8F93\u5165\u5BA2\u6237\u7AEF\u5BC6\u94A5,\u5BF9\u5E94\u5404\u5E73\u53F0\u7684appSecret"},null,8,["modelValue"])]),_:1}),a(t).socialType===30?(o(),p(i,{key:0,label:"agentId",prop:"agentId"},{default:s(()=>[u(n,{modelValue:a(t).agentId,"onUpdate:modelValue":e[5]||(e[5]=l=>a(t).agentId=l),placeholder:"\u6388\u6743\u65B9\u7684\u7F51\u9875\u5E94\u7528 ID\uFF0C\u6709\u5219\u586B"},null,8,["modelValue"])]),_:1})):Z("",!0),u(i,{label:"\u72B6\u6001",prop:"status"},{default:s(()=>[u(b,{modelValue:a(t).status,"onUpdate:modelValue":e[6]||(e[6]=l=>a(t).status=l)},{default:s(()=>[(o(!0),I(k,null,w(a(T)(a(S).COMMON_STATUS),l=>(o(),p(V,{key:l.value,value:l.value},{default:s(()=>[y(U(l.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[D,a(c)]])]),_:1},8,["modelValue","title"])}}});export{te as _,ae as d,le as g};
