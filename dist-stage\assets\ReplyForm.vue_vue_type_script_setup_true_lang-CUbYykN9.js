import{a3 as x,D as b,h as R}from"./index-BeQABqnP.js";import F from"./main-DqTRH_Q7.js";import{M as v}from"./types-CAO1T7C7.js";import{h as P,x as A,Q as S,aj as j,k as C}from"./form-designer-DQFPUccF.js";import{k as G,c as h,r as H,l as p,m as u,H as t,u as l,z as o,y as d,C as i,G as c,$ as q,h as Q}from"./form-create-B86qX0W_.js";const Y=G({name:"ReplyForm",__name:"ReplyForm",props:{modelValue:{},reply:{},msgType:{}},emits:["update:reply","update:modelValue"],setup(M,{expose:k,emit:T}){const g=M,_=T,n=h({get:()=>g.reply,set:a=>_("update:reply",a)}),r=h({get:()=>g.modelValue,set:a=>_("update:modelValue",a)}),y=H(null),w=["text","image","voice","video","shortvideo","location","link"],E={requestKeyword:[{required:!0,message:"\u8BF7\u6C42\u7684\u5173\u952E\u5B57\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],requestMatch:[{required:!0,message:"\u8BF7\u6C42\u7684\u5173\u952E\u5B57\u7684\u5339\u914D\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]};return k({resetFields:()=>{var a;return(a=y.value)==null?void 0:a.resetFields()},validate:async()=>{var a;return(a=y.value)==null?void 0:a.validate()}}),(a,s)=>{const f=S,V=A,m=j,K=C,U=P;return u(),p("div",null,[t(U,{ref_key:"formRef",ref:y,model:l(r),rules:E,"label-width":"80px"},{default:o(()=>[a.msgType===l(v).Message?(u(),d(m,{key:0,label:"\u6D88\u606F\u7C7B\u578B",prop:"requestMessageType"},{default:o(()=>[t(V,{modelValue:l(r).requestMessageType,"onUpdate:modelValue":s[0]||(s[0]=e=>l(r).requestMessageType=e),placeholder:"\u8BF7\u9009\u62E9"},{default:o(()=>[(u(!0),p(c,null,q(l(x)(l(b).MP_MESSAGE_TYPE),e=>(u(),p(c,{key:e.value},[w.includes(e.value)?(u(),d(f,{key:0,label:e.label,value:e.value},null,8,["label","value"])):i("",!0)],64))),128))]),_:1},8,["modelValue"])]),_:1})):i("",!0),a.msgType===l(v).Keyword?(u(),d(m,{key:1,label:"\u5339\u914D\u7C7B\u578B",prop:"requestMatch"},{default:o(()=>[t(V,{modelValue:l(r).requestMatch,"onUpdate:modelValue":s[1]||(s[1]=e=>l(r).requestMatch=e),placeholder:"\u8BF7\u9009\u62E9\u5339\u914D\u7C7B\u578B",clearable:""},{default:o(()=>[(u(!0),p(c,null,q(l(R)(l(b).MP_AUTO_REPLY_REQUEST_MATCH),e=>(u(),d(f,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):i("",!0),a.msgType===l(v).Keyword?(u(),d(m,{key:2,label:"\u5173\u952E\u8BCD",prop:"requestKeyword"},{default:o(()=>[t(K,{modelValue:l(r).requestKeyword,"onUpdate:modelValue":s[2]||(s[2]=e=>l(r).requestKeyword=e),placeholder:"\u8BF7\u8F93\u5165\u5185\u5BB9",clearable:""},null,8,["modelValue"])]),_:1})):i("",!0),t(m,{label:"\u56DE\u590D\u6D88\u606F"},{default:o(()=>[t(l(F),{modelValue:l(n),"onUpdate:modelValue":s[3]||(s[3]=e=>Q(n)?n.value=e:null)},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])])}}});export{Y as _};
