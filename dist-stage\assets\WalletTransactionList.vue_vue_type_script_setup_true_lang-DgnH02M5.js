import{H as g}from"./index-BeQABqnP.js";import{_ as T}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CxohUsTD.js";import{_ as j}from"./index.vue_vue_type_script_setup_true_lang-BjqH9dXd.js";import{d as x}from"./formatTime-CN67D7Gb.js";import{g as S}from"./index-DrJkUdsU.js";import{g as h}from"./index-DUvbsVxr.js";import{Z as k,_ as q,ak as H}from"./form-designer-DQFPUccF.js";import{k as L,r as m,P,e as U,y as f,m as w,z as i,A as W,H as l,u as a,E as b,F as I}from"./form-create-B86qX0W_.js";const A=L({name:"WalletTransactionList",__name:"WalletTransactionList",props:{walletId:{type:Number,required:!1},userId:{type:Number,required:!1}},setup(_){const p=_,n=m(!0),d=m(0),e=P({pageNo:1,pageSize:10,walletId:null}),u=m([]),c=async()=>{n.value=!0;try{if(p.userId){const r=await h({userId:p.userId});e.walletId=r.id}else e.walletId=p.walletId;const s=await S(e);u.value=s.list,d.value=s.total}finally{n.value=!1}};return U(()=>{c()}),(s,r)=>{const o=q,y=k,v=j,N=T,z=H;return w(),f(N,null,{default:i(()=>[W((w(),f(y,{data:a(u),"show-overflow-tooltip":!0,stripe:!0},{default:i(()=>[l(o,{align:"center",label:"\u7F16\u53F7",prop:"id"}),l(o,{align:"center",label:"\u94B1\u5305\u7F16\u53F7",prop:"walletId"}),l(o,{align:"center",label:"\u5173\u8054\u4E1A\u52A1\u6807\u9898",prop:"title"}),l(o,{align:"center",label:"\u4EA4\u6613\u91D1\u989D",prop:"price"},{default:i(({row:t})=>[b(I(a(g)(t.price))+" \u5143",1)]),_:1}),l(o,{align:"center",label:"\u94B1\u5305\u4F59\u989D",prop:"balance"},{default:i(({row:t})=>[b(I(a(g)(t.balance))+" \u5143",1)]),_:1}),l(o,{formatter:a(x),align:"center",label:"\u4EA4\u6613\u65F6\u95F4",prop:"createTime",width:"180px"},null,8,["formatter"])]),_:1},8,["data"])),[[z,a(n)]]),l(v,{limit:a(e).pageSize,"onUpdate:limit":r[0]||(r[0]=t=>a(e).pageSize=t),page:a(e).pageNo,"onUpdate:page":r[1]||(r[1]=t=>a(e).pageNo=t),total:a(d),onPagination:c},null,8,["limit","page","total"])]),_:1})}}});export{A as _};
