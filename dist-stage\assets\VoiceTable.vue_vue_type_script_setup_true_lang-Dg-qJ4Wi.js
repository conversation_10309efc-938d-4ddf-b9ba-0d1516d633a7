import{_ as y}from"./index-BeQABqnP.js";import C from"./main-BSQmhXUw.js";import{d as h}from"./formatTime-CN67D7Gb.js";import{ak as x,_ as T,f as v,Z as V}from"./form-designer-DQFPUccF.js";import{k as j,af as z,A as p,y as i,m as o,z as l,H as e,C as A,u as c,v as B,F as E,E as f}from"./form-create-B86qX0W_.js";const F=j({__name:"VoiceTable",props:{list:{},loading:{type:Boolean}},emits:["delete"],setup(u,{emit:_}){const n=u,s=_;return(H,t)=>{const r=T,d=y,m=v,g=V,k=z("hasPermi"),w=x;return p((o(),i(g,{data:n.list,stripe:"",border:"",style:{"margin-top":"10px"}},{default:l(()=>[e(r,{label:"\u7F16\u53F7",align:"center",prop:"mediaId"}),e(r,{label:"\u6587\u4EF6\u540D",align:"center",prop:"name"}),e(r,{label:"\u8BED\u97F3",align:"center"},{default:l(a=>[a.row.url?(o(),i(c(C),{key:0,url:a.row.url},null,8,["url"])):A("",!0)]),_:1}),e(r,{label:"\u4E0A\u4F20\u65F6\u95F4",align:"center",prop:"createTime",formatter:c(h),width:"180"},{default:l(a=>[B("span",null,E(a.row.createTime),1)]),_:1},8,["formatter"]),e(r,{label:"\u64CD\u4F5C",align:"center","class-name":"small-padding fixed-width"},{default:l(a=>[e(m,{type:"primary",link:"",onClick:b=>s("delete",a.row.id)},{default:l(()=>[e(d,{icon:"ep:download"}),t[0]||(t[0]=f("\u4E0B\u8F7D "))]),_:2},1032,["onClick"]),p((o(),i(m,{type:"primary",link:"",onClick:b=>s("delete",a.row.id)},{default:l(()=>[e(d,{icon:"ep:delete"}),t[1]||(t[1]=f("\u5220\u9664 "))]),_:2},1032,["onClick"])),[[k,["mp:material:delete"]]])]),_:1})]),_:1},8,["data"])),[[w,n.loading]])}}});export{F as _};
