<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="auto"
    >
      <!-- <el-form-item label="交易编号" prop="bizId">
        <el-input
          v-model="queryParams.bizId"
          placeholder="请输入交易编号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item> -->
      <el-form-item label="交易单号" prop="bizNo">
        <el-input
          v-model="queryParams.bizNo"
          placeholder="请输入交易单号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <!-- <el-form-item label="交易明细ID" prop="bizDetailId">
        <el-input
          v-model="queryParams.bizDetailId"
          placeholder="请输入交易明细ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item> -->
      <el-form-item label="交易类型" prop="transactionType">
        <el-select
          v-model="queryParams.transactionType"
          placeholder="请选择交易类型"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in scm_biz_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="交易方向" prop="transactionDirection" v-show="isExpanded">
        <el-select
          v-model="queryParams.transactionDirection"
          placeholder="请选择交易方向"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in inventory_transaction_direction"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="物料ID" prop="materialId">
        <el-input
          v-model="queryParams.materialId"
          placeholder="请输入物料ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item> -->
      <el-form-item label="物料编码" prop="materialCode" >
        <el-input
          v-model="queryParams.materialCode"
          placeholder="请输入物料编码"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="物料名称" prop="materialName">
        <el-input
          v-model="queryParams.materialName"
          placeholder="请输入物料名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="物料类型" prop="materialType" v-show="isExpanded">
        <el-select
          v-model="queryParams.materialType"
          placeholder="请选择物料类型"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in material_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <!-- 展开后显示的字段 -->
      <el-form-item label="移动类型" prop="moveType" v-show="isExpanded">
        <el-select
          v-model="queryParams.moveType"
          placeholder="请选择移动类型"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in inventory_move_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="移动源仓库ID" prop="fromWarehouseId">
        <el-input
          v-model="queryParams.fromWarehouseId"
          placeholder="请输入移动源仓库ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item> -->
      <el-form-item label="移动源仓库名称" prop="fromWarehouseName" v-show="isExpanded">
        <el-input
          v-model="queryParams.fromWarehouseName"
          placeholder="请输入移动源仓库名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <!-- <el-form-item label="移动源仓位ID" prop="fromLocationId">
        <el-input
          v-model="queryParams.fromLocationId"
          placeholder="请输入移动源仓位ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item> -->
      <el-form-item label="移动源仓位名称" prop="fromLocationName" v-show="isExpanded">
        <el-input
          v-model="queryParams.fromLocationName"
          placeholder="请输入移动源仓位名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <!-- <el-form-item label="移动到仓库ID" prop="toWarehouseId">
        <el-input
          v-model="queryParams.toWarehouseId"
          placeholder="请输入移动到仓库ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item> -->
      <el-form-item label="移动到仓库名称" prop="toWarehouseName" v-show="isExpanded">
        <el-input
          v-model="queryParams.toWarehouseName"
          placeholder="请输入移动到仓库名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <!-- <el-form-item label="移动到仓位ID" prop="toLocationId">
        <el-input
          v-model="queryParams.toLocationId"
          placeholder="请输入移动到仓位ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item> -->
      <el-form-item label="移动到仓位名称" prop="toLocationName" v-show="isExpanded">
        <el-input
          v-model="queryParams.toLocationName"
          placeholder="请输入移动到仓位名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="移动日期" prop="moveDate" v-show="isExpanded">
        <el-date-picker
          v-model="queryParams.moveDate"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <!-- <el-form-item label="来源单号ID" prop="sourceId">
        <el-input
          v-model="queryParams.sourceId"
          placeholder="请输入来源单号ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item> -->
      <el-form-item label="来源单号" prop="sourceNo" v-show="isExpanded">
        <el-input
          v-model="queryParams.sourceNo"
          placeholder="请输入来源单号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <!-- <el-form-item label="库存ID" prop="inventoryId">
        <el-input
          v-model="queryParams.inventoryId"
          placeholder="请输入库存ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item> -->
      <el-form-item label="库存批号" prop="inventoryBatchNo" v-show="isExpanded">
        <el-input
          v-model="queryParams.inventoryBatchNo"
          placeholder="请输入库存批号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="数量" prop="quantity" v-show="isExpanded">
        <el-input
          v-model="queryParams.quantity"
          placeholder="请输入数量"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="单位" prop="quantityUnit" v-show="isExpanded">
        <el-input
          v-model="queryParams.quantityUnit"
          placeholder="请输入单位"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="基本单位数量" prop="auxiliaryQuantity" v-show="isExpanded">
        <el-input
          v-model="queryParams.auxiliaryQuantity"
          placeholder="请输入基本单位数量"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="基本单位" prop="auxiliaryUnit" v-show="isExpanded">
        <el-input
          v-model="queryParams.auxiliaryUnit"
          placeholder="请输入基本单位"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="备注" prop="remark" v-show="isExpanded">
        <el-input
          v-model="queryParams.remark"
          placeholder="请输入备注"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime" v-show="isExpanded">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="出入库前库存数量" prop="beforeQuantity" v-show="isExpanded">
        <el-input
          v-model="queryParams.beforeQuantity"
          placeholder="请输入出入库前库存数量"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="出入库后库存数量" prop="afterQuantity" v-show="isExpanded">
        <el-input
          v-model="queryParams.afterQuantity"
          placeholder="请输入出入库后库存数量"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="成本对象编码" prop="costObjectCode" v-show="isExpanded">
        <el-input
          v-model="queryParams.costObjectCode"
          placeholder="请输入成本对象编码"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="成本对象名称" prop="costObjectName" v-show="isExpanded">
        <el-input
          v-model="queryParams.costObjectName"
          placeholder="请输入成本对象名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="记账凭证号" prop="accountingVoucherNumber" v-show="isExpanded">
        <el-input
          v-model="queryParams.accountingVoucherNumber"
          placeholder="请输入记账凭证号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="租户名称" prop="tenantName" v-show="isExpanded">
        <el-input
          v-model="queryParams.tenantName"
          placeholder="请输入租户名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <!-- <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['inventory:transaction:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button> -->
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['inventory:transaction:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
        <el-button
          type="primary"
          link
          @click="toggleExpanded"
        >
          <Icon :icon="isExpanded ? 'ep:arrow-up' : 'ep:arrow-down'" class="mr-5px" />
          {{ isExpanded ? '收起' : '展开' }}
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true" border show-summary :summary-method="summaryMethod">
      <!-- <el-table-column type="expand">
        <template #default="scope">
          <el-tabs model-value="transactionDetail"/>
        </template>
      </el-table-column> -->
      <el-table-column label="交易单号" align="center" prop="bizNo" width="160">
        <template #default="scope">
          <div class="order-no-cell" v-if="scope.row.bizNo">
            <div class="order-no-content">
              <span>{{ scope.row.bizNo }}</span>
            </div>
            <el-button
              link
              type="info"
              @click="copyOrderNo(scope.row.bizNo)"
              class="copy-btn copy-btn-fixed"
              size="small"
            >
              <Icon icon="ep:copy-document" :size="12"/>
            </el-button>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="交易类型" align="center" prop="transactionType" width="130">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.SCM_BIZ_TYPE" :value="scope.row.transactionType" />
        </template>
      </el-table-column>
      <el-table-column label="交易方向" align="center" prop="transactionDirection" width="120">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.INVENTORY_TRANSACTION_DIRECTION" :value="scope.row.transactionDirection" />
        </template>
      </el-table-column>
      <!-- <el-table-column label="物料ID" align="center" prop="materialId" /> -->
      <el-table-column label="物料编码" align="center" prop="materialCode" width="120"/>
      <el-table-column label="物料名称" align="center" prop="materialName" width="100"/>
      <el-table-column label="物料类型" align="center" prop="materialType" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.MATERIAL_TYPE" :value="scope.row.materialType" />
        </template>
      </el-table-column>
      <el-table-column label="移动前仓库" align="center" prop="fromWarehouseName" width="120"/>
      <el-table-column label="移动后仓库" align="center" prop="toWarehouseName" width="120"/>
      <el-table-column
        label="移动日期"
        align="center"
        prop="moveDate"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="来源单号" align="center" prop="sourceNo" width="150">
        <template #default="scope">
          <div class="order-no-cell" v-if="scope.row.sourceNo">
            <div class="order-no-content">
              <span>{{ scope.row.sourceNo }}</span>
            </div>
            <el-button
              link
              type="info"
              @click="copyOrderNo(scope.row.sourceNo)"
              class="copy-btn copy-btn-fixed"
              size="small"
            >
              <Icon icon="ep:copy-document" :size="12"/>
            </el-button>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="库存批号" align="center" prop="inventoryBatchNo" width="100"/>
      <el-table-column label="数量" align="center" prop="quantity">
        <template #default="scope">
          {{ formatQuantity(scope.row.quantity) }}
        </template>
      </el-table-column>
      <el-table-column label="单位" align="center" prop="quantityUnit">
        <template #default="scope">
          {{ getUnitName(scope.row.quantityUnit) }}
        </template>
      </el-table-column>
      <el-table-column label="基本单位数量" align="center" prop="auxiliaryQuantity" width="120">
        <template #default="scope">
          {{ formatQuantity(scope.row.auxiliaryQuantity) }}
        </template>
      </el-table-column>
      <el-table-column label="基本单位" align="center" prop="auxiliaryUnit" width="100">
        <template #default="scope">
          {{ getUnitName(scope.row.auxiliaryUnit) }}
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="出入库前库存数量" align="center" prop="beforeQuantity" width="140">
        <template #default="scope">
          {{ formatQuantity(scope.row.beforeQuantity) }}
        </template>
      </el-table-column>
      <el-table-column label="出入库前批次数量" align="center" prop="beforeBatchQuantity" width="140">
        <template #default="scope">
          {{ formatQuantity(scope.row.beforeBatchQuantity) }}
        </template>
      </el-table-column>
      <el-table-column label="出入库后库存数量" align="center" prop="afterQuantity" width="140">
        <template #default="scope">
          {{ formatQuantity(scope.row.afterQuantity) }}
        </template>
      </el-table-column>
      <el-table-column label="出入库后批次数量" align="center" prop="afterBatchQuantity" width="140">
        <template #default="scope">
          {{ formatQuantity(scope.row.afterBatchQuantity) }}
        </template>
      </el-table-column>
      <el-table-column label="成本对象编码" align="center" prop="costObjectCode" width="120"/>
      <el-table-column label="成本对象名称" align="center" prop="costObjectName" width="120"/>
      <el-table-column label="记账凭证号" align="center" prop="accountingVoucherNumber" width="100">
        <template #default="scope">
          <div class="order-no-cell" v-if="scope.row.accountingVoucherNumber">
            <div class="order-no-content">
              <span>{{ scope.row.accountingVoucherNumber }}</span>
            </div>
            <el-button
              link
              type="info"
              @click="copyOrderNo(scope.row.accountingVoucherNumber)"
              class="copy-btn copy-btn-fixed"
              size="small"
            >
              <Icon icon="ep:copy-document" :size="12"/>
            </el-button>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="摘要" align="center" prop="summary" />
      <el-table-column label="移动前仓位" align="center" prop="fromLocationName" width="100"/>
      <el-table-column label="移动后仓位" align="center" prop="toLocationName" width="100"/>
      <el-table-column label="操作" align="center" min-width="80px" fixed="right">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['inventory:transaction:update']"
          >
            编辑
          </el-button>
          <!-- <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['inventory:transaction:delete']"
          >
            删除
          </el-button> -->
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <TransactionForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { TransactionApi, TransactionVO } from '@/api/scm/inventory/transaction'
import TransactionForm from './TransactionForm.vue'
import { DICT_TYPE, getStrDictOptions } from '@/utils/dict'
import { getRemoteUnit } from '@/utils/commonBiz'
import { formatQuantity } from '@/utils/formatter'
import { useClipboard } from '@vueuse/core'

/** 库存交易明细 列表 */
defineOptions({ name: 'Transaction' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const { copy } = useClipboard() // 复制功能

/** 复制订单单号 */
const copyOrderNo = async (orderNo: string) => {
  try {
    await copy(orderNo)
    message.success('单号复制成功')
  } catch (error) {
    message.error('复制失败')
  }
}

const loading = ref(true) // 列表的加载中
const list = ref<TransactionVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const isExpanded = ref(false) // 表单展开状态
const unitMap = ref<Map<number, string>>(new Map()) // 单位ID到名称的映射
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  bizId: undefined,
  bizNo: undefined,
  bizDetailId: undefined,
  transactionType: undefined,
  transactionDirection: undefined,
  materialId: undefined,
  materialCode: undefined,
  materialName: undefined,
  materialType: undefined,
  moveType: undefined,
  fromWarehouseId: undefined,
  fromWarehouseName: undefined,
  fromLocationId: undefined,
  fromLocationName: undefined,
  toWarehouseId: undefined,
  toWarehouseName: undefined,
  toLocationId: undefined,
  toLocationName: undefined,
  moveDate: [],
  sourceId: undefined,
  sourceNo: undefined,
  inventoryId: undefined,
  inventoryBatchNo: undefined,
  quantity: undefined,
  quantityUnit: undefined,
  auxiliaryQuantity: undefined,
  auxiliaryUnit: undefined,
  remark: undefined,
  createTime: [],
  beforeQuantity: undefined,
  afterQuantity: undefined,
  costObjectCode: undefined,
  costObjectName: undefined,
  accountingVoucherNumber: undefined,
  tenantName: undefined,
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

// 字典数据
const scm_biz_type = getStrDictOptions(DICT_TYPE.SCM_BIZ_TYPE)
const material_type = getStrDictOptions(DICT_TYPE.MATERIAL_TYPE)
const inventory_move_type = getStrDictOptions(DICT_TYPE.INVENTORY_MOVE_TYPE)
const inventory_transaction_direction = getStrDictOptions(DICT_TYPE.INVENTORY_TRANSACTION_DIRECTION)

/** 加载单位数据 */
const loadUnits = async () => {
  try {
    // 批量获取所有单位信息
    const units = await getRemoteUnit()

    if (!units || units.length === 0) {
      return
    }

    // 建立单位映射
    units.forEach((unit: any) => {
      if (unit && unit.id && unit.name) {
        unitMap.value.set(unit.id, unit.name)
      }
    })
  } catch (error) {
    console.error('加载单位数据失败:', error)
  }
}

/** 获取单位名称 */
const getUnitName = (unitId: number | string) => {
  if (!unitId && unitId !== 0) return ''

  // 处理数字类型
  if (typeof unitId === 'number') {
    const unitName = unitMap.value.get(unitId)
    if (unitName) return unitName
  }

  // 处理字符串类型
  if (typeof unitId === 'string') {
    // 先尝试按字符串查找
    for (const [id, name] of unitMap.value) {
      if (id.toString() === unitId) {
        return name
      }
    }

    // 再尝试转换为数字查找
    const numId = parseInt(unitId)
    if (!isNaN(numId)) {
      const unitName = unitMap.value.get(numId)
      if (unitName) return unitName
    }
  }

  return unitId.toString()
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await TransactionApi.getTransactionPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await TransactionApi.deleteTransaction(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await TransactionApi.exportTransaction(queryParams)
    download.excel(data, '库存交易明细.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 切换表单展开状态 */
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
}

/** 表格汇总方法 */
const summaryMethod = ({ columns, data }: { columns: any[], data: any[] }) => {
  const sums: any[] = []

  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }

    // 需要汇总的数量字段
    const quantityFields = ['quantity', 'auxiliaryQuantity', 'beforeQuantity', 'beforeBatchQuantity',
                           'afterQuantity', 'afterBatchQuantity']

    if (quantityFields.includes(column.property)) {
      // 数量字段汇总
      const values = data.map(item => Number(item[column.property]) || 0)
      const total = values.reduce((prev, curr) => prev + curr, 0)
      sums[index] = formatQuantity(total)
    } else {
      // 其他字段不汇总
      sums[index] = ''
    }
  })

  return sums
}

/** 初始化 **/
onMounted(async () => {
  // 先加载单位数据
  await loadUnits()
  // 再加载列表数据
  getList()
})
</script>

<style scoped>
/* 订单号行布局 */
.order-no-cell {
  display: flex;
  align-items: center;
  width: 100%;
  min-height: 20px;
  gap: 5px; /* 文字和图标之间保持一点点间隔 */
}

.order-no-content {
  flex: 0 1 auto; /* 改为自适应宽度，不占满剩余空间 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: calc(100% - 16px); /* 为图标预留空间 */
}

/* 复制按钮样式 */
.copy-btn {
  padding: 2px !important;
  height: 20px !important;
  min-height: 20px !important;
  width: 20px !important;
  font-size: 12px;
  opacity: 0.6;
  transition: opacity 0.2s;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}



.copy-btn:hover {
  opacity: 1;
}
</style>
