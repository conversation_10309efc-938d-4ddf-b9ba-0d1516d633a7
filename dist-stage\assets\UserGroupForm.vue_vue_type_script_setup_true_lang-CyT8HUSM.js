import{a as T,d as j,h as z,D}from"./index-BeQABqnP.js";import{_ as H}from"./Dialog.vue_vue_type_style_index_0_lang-DpoeWsKb.js";import{C as U}from"./constants-C3gLHYOK.js";import{a as P,c as Q,u as R}from"./index-BQAsUiCJ.js";import{g as W}from"./index-UoUrkNdU.js";import{h as Z,aj as $,k as J,x as K,Q as X,s as Y,u as ee,ak as ae,f as le}from"./form-designer-DQFPUccF.js";import{k as se,r as o,P as ue,y as c,m,z as u,A as te,u as l,H as t,l as h,G as w,$ as I,E as f,F as re,h as oe}from"./form-create-B86qX0W_.js";const de=se({name:"UserGroupForm",__name:"UserGroupForm",emits:["success"],setup(ie,{expose:x,emit:E}){const{t:v}=T(),V=j(),d=o(!1),_=o(""),i=o(!1),b=o(""),s=o({id:void 0,name:void 0,description:void 0,userIds:void 0,status:U.ENABLE}),q=ue({name:[{required:!0,message:"\u7EC4\u540D\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],description:[{required:!0,message:"\u63CF\u8FF0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],userIds:[{required:!0,message:"\u6210\u5458\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],status:[{required:!0,message:"\u72B6\u6001\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),n=o(),g=o([]);x({open:async(r,e)=>{if(d.value=!0,_.value=v("action."+r),b.value=r,G(),e){i.value=!0;try{s.value=await P(e)}finally{i.value=!1}}g.value=await W()}});const A=E,C=async()=>{if(n&&await n.value.validate()){i.value=!0;try{const r=s.value;b.value==="create"?(await Q(r),V.success(v("common.createSuccess"))):(await R(r),V.success(v("common.updateSuccess"))),d.value=!1,A("success")}finally{i.value=!1}}},G=()=>{var r;s.value={id:void 0,name:void 0,description:void 0,userIds:void 0,status:U.ENABLE},(r=n.value)==null||r.resetFields()};return(r,e)=>{const y=J,p=$,S=X,F=K,N=ee,B=Y,L=Z,k=le,M=H,O=ae;return m(),c(M,{modelValue:l(d),"onUpdate:modelValue":e[5]||(e[5]=a=>oe(d)?d.value=a:null),title:l(_)},{footer:u(()=>[t(k,{disabled:l(i),type:"primary",onClick:C},{default:u(()=>e[6]||(e[6]=[f("\u786E \u5B9A")])),_:1},8,["disabled"]),t(k,{onClick:e[4]||(e[4]=a=>d.value=!1)},{default:u(()=>e[7]||(e[7]=[f("\u53D6 \u6D88")])),_:1})]),default:u(()=>[te((m(),c(L,{ref_key:"formRef",ref:n,model:l(s),rules:l(q),"label-width":"100px"},{default:u(()=>[t(p,{label:"\u7EC4\u540D",prop:"name"},{default:u(()=>[t(y,{modelValue:l(s).name,"onUpdate:modelValue":e[0]||(e[0]=a=>l(s).name=a),placeholder:"\u8BF7\u8F93\u5165\u7EC4\u540D"},null,8,["modelValue"])]),_:1}),t(p,{label:"\u63CF\u8FF0"},{default:u(()=>[t(y,{modelValue:l(s).description,"onUpdate:modelValue":e[1]||(e[1]=a=>l(s).description=a),placeholder:"\u8BF7\u8F93\u5165\u63CF\u8FF0",type:"textarea"},null,8,["modelValue"])]),_:1}),t(p,{label:"\u6210\u5458",prop:"userIds"},{default:u(()=>[t(F,{modelValue:l(s).userIds,"onUpdate:modelValue":e[2]||(e[2]=a=>l(s).userIds=a),multiple:"",placeholder:"\u8BF7\u9009\u62E9\u6210\u5458"},{default:u(()=>[(m(!0),h(w,null,I(l(g),a=>(m(),c(S,{key:a.id,label:a.nickname,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(p,{label:"\u72B6\u6001",prop:"status"},{default:u(()=>[t(B,{modelValue:l(s).status,"onUpdate:modelValue":e[3]||(e[3]=a=>l(s).status=a)},{default:u(()=>[(m(!0),h(w,null,I(l(z)(l(D).COMMON_STATUS),a=>(m(),c(N,{key:a.value,value:a.value},{default:u(()=>[f(re(a.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[O,l(i)]])]),_:1},8,["modelValue","title"])}}});export{de as _};
