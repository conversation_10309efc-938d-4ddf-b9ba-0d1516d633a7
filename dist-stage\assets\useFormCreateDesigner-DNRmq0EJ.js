import{ar as n}from"./index-BeQABqnP.js";import{l as u,m as r}from"./index-BCN8BzfC.js";import{g as T}from"./dict.type-CtwWvaCn.js";import{K as g}from"./form-designer-DQFPUccF.js";import{r as S,e as b,n as $}from"./form-create-B86qX0W_.js";const y=[{type:"select",field:"selectType",title:"\u9009\u62E9\u5668\u7C7B\u578B",value:"select",options:[{label:"\u4E0B\u62C9\u6846",value:"select"},{label:"\u5355\u9009\u6846",value:"radio"},{label:"\u591A\u9009\u6846",value:"checkbox"}],control:[{value:"select",condition:"==",method:"hidden",rule:["multiple","clearable","collapseTags","multipleLimit","allowCreate","filterable","noMatchText","remote","remoteMethod","reserveKeyword","defaultFirstOption","automaticDropdown"]}]},{type:"switch",field:"filterable",title:"\u662F\u5426\u53EF\u641C\u7D22"},{type:"switch",field:"multiple",title:"\u662F\u5426\u591A\u9009"},{type:"switch",field:"disabled",title:"\u662F\u5426\u7981\u7528"},{type:"switch",field:"clearable",title:"\u662F\u5426\u53EF\u4EE5\u6E05\u7A7A\u9009\u9879"},{type:"switch",field:"collapseTags",title:"\u591A\u9009\u65F6\u662F\u5426\u5C06\u9009\u4E2D\u503C\u6309\u6587\u5B57\u7684\u5F62\u5F0F\u5C55\u793A"},{type:"inputNumber",field:"multipleLimit",title:"\u591A\u9009\u65F6\u7528\u6237\u6700\u591A\u53EF\u4EE5\u9009\u62E9\u7684\u9879\u76EE\u6570\uFF0C\u4E3A 0 \u5219\u4E0D\u9650\u5236",props:{min:0}},{type:"input",field:"autocomplete",title:"autocomplete \u5C5E\u6027"},{type:"input",field:"placeholder",title:"\u5360\u4F4D\u7B26"},{type:"switch",field:"allowCreate",title:"\u662F\u5426\u5141\u8BB8\u7528\u6237\u521B\u5EFA\u65B0\u6761\u76EE"},{type:"input",field:"noMatchText",title:"\u641C\u7D22\u6761\u4EF6\u65E0\u5339\u914D\u65F6\u663E\u793A\u7684\u6587\u5B57"},{type:"input",field:"noDataText",title:"\u9009\u9879\u4E3A\u7A7A\u65F6\u663E\u793A\u7684\u6587\u5B57"},{type:"switch",field:"reserveKeyword",title:"\u591A\u9009\u4E14\u53EF\u641C\u7D22\u65F6\uFF0C\u662F\u5426\u5728\u9009\u4E2D\u4E00\u4E2A\u9009\u9879\u540E\u4FDD\u7559\u5F53\u524D\u7684\u641C\u7D22\u5173\u952E\u8BCD"},{type:"switch",field:"defaultFirstOption",title:"\u5728\u8F93\u5165\u6846\u6309\u4E0B\u56DE\u8F66\uFF0C\u9009\u62E9\u7B2C\u4E00\u4E2A\u5339\u914D\u9879"},{type:"switch",field:"popperAppendToBody",title:"\u662F\u5426\u5C06\u5F39\u51FA\u6846\u63D2\u5165\u81F3 body \u5143\u7D20",value:!0},{type:"switch",field:"automaticDropdown",title:"\u5BF9\u4E8E\u4E0D\u53EF\u641C\u7D22\u7684 Select\uFF0C\u662F\u5426\u5728\u8F93\u5165\u6846\u83B7\u5F97\u7126\u70B9\u540E\u81EA\u52A8\u5F39\u51FA\u9009\u9879\u83DC\u5355"}],j=[{type:"input",field:"url",title:"url \u5730\u5740",props:{placeholder:"/system/user/simple-list"}},{type:"select",field:"method",title:"\u8BF7\u6C42\u7C7B\u578B",value:"GET",options:[{label:"GET",value:"GET"},{label:"POST",value:"POST"}],control:[{value:"GET",condition:"!=",method:"hidden",rule:[{type:"input",field:"data",title:"\u8BF7\u6C42\u53C2\u6570 JSON \u683C\u5F0F",props:{autosize:!0,type:"textarea",placeholder:'{"type": 1}'}}]}]},{type:"input",field:"labelField",title:"label \u5C5E\u6027",info:"\u53EF\u4EE5\u4F7F\u7528 el \u8868\u8FBE\u5F0F\uFF1A${\u5C5E\u6027}\uFF0C\u6765\u5B9E\u73B0\u590D\u6742\u6570\u636E\u7EC4\u5408\u3002\u5982\uFF1A${nickname}-${id}",props:{placeholder:"nickname"}},{type:"input",field:"valueField",title:"value \u5C5E\u6027",info:"\u53EF\u4EE5\u4F7F\u7528 el \u8868\u8FBE\u5F0F\uFF1A${\u5C5E\u6027}\uFF0C\u6765\u5B9E\u73B0\u590D\u6742\u6570\u636E\u7EC4\u5408\u3002\u5982\uFF1A${nickname}-${id}",props:{placeholder:"id"}},{type:"input",field:"parseFunc",title:"\u9009\u9879\u89E3\u6790\u51FD\u6570",info:`data \u4E3A\u63A5\u53E3\u8FD4\u56DE\u503C,\u9700\u8981\u5199\u4E00\u4E2A\u533F\u540D\u51FD\u6570\u89E3\u6790\u8FD4\u56DE\u503C\u4E3A\u9009\u62E9\u5668 options \u5217\u8868
    (data: any)=>{ label: string; value: any }[]`,props:{autosize:!0,rows:{minRows:2,maxRows:6},type:"textarea",placeholder:`
        function (data) {
            console.log(data)
            return data.list.map(item=> ({label: item.nickname,value: item.id}))
        }`}},{type:"switch",field:"remote",info:"\u662F\u5426\u53EF\u641C\u7D22",title:"\u5176\u4E2D\u7684\u9009\u9879\u662F\u5426\u4ECE\u670D\u52A1\u5668\u8FDC\u7A0B\u52A0\u8F7D"},{type:"input",field:"remoteField",title:"\u8BF7\u6C42\u53C2\u6570",info:"\u8FDC\u7A0B\u8BF7\u6C42\u65F6\u8BF7\u6C42\u643A\u5E26\u7684\u53C2\u6570\u540D\u79F0\uFF0C\u5982\uFF1Aname"}],f=t=>{const m=t.label,d=t.name,s=g(y);return{icon:t.icon,label:m,name:d,event:t.event,rule:()=>({type:d,field:n(),title:m,info:"",$required:!1}),props:(v,{t:c})=>(t.props||(t.props=[]),u(c,d+".props",[r(),...t.props,...s]))}},M=async t=>{const m=(()=>{const l="Editor";return{icon:"icon-editor",label:"\u5BCC\u6587\u672C",name:l,rule:()=>({type:l,field:n(),title:"\u5BCC\u6587\u672C",info:"",$required:!1}),props:(e,{t:p})=>u(p,l+".props",[r(),{type:"input",field:"height",title:"\u9AD8\u5EA6"},{type:"switch",field:"readonly",title:"\u662F\u5426\u53EA\u8BFB"}])}})(),d=(()=>{const l="\u6587\u4EF6\u4E0A\u4F20",e="UploadFile";return{icon:"icon-upload",label:l,name:e,rule:()=>({type:e,field:n(),title:l,info:"",$required:!1}),props:(p,{t:i})=>u(i,e+".props",[r(),{type:"select",field:"fileType",title:"\u6587\u4EF6\u7C7B\u578B",value:["doc","xls","ppt","txt","pdf"],options:[{label:"doc",value:"doc"},{label:"xls",value:"xls"},{label:"ppt",value:"ppt"},{label:"txt",value:"txt"},{label:"pdf",value:"pdf"}],props:{multiple:!0}},{type:"switch",field:"autoUpload",title:"\u662F\u5426\u5728\u9009\u53D6\u6587\u4EF6\u540E\u7ACB\u5373\u8FDB\u884C\u4E0A\u4F20",value:!0},{type:"switch",field:"drag",title:"\u62D6\u62FD\u4E0A\u4F20",value:!1},{type:"switch",field:"isShowTip",title:"\u662F\u5426\u663E\u793A\u63D0\u793A",value:!0},{type:"inputNumber",field:"fileSize",title:"\u5927\u5C0F\u9650\u5236(MB)",value:5,props:{min:0}},{type:"inputNumber",field:"limit",title:"\u6570\u91CF\u9650\u5236",value:5,props:{min:0}},{type:"switch",field:"disabled",title:"\u662F\u5426\u7981\u7528",value:!1}])}})(),s=(()=>{const l="\u5355\u56FE\u4E0A\u4F20",e="UploadImg";return{icon:"icon-upload",label:l,name:e,rule:()=>({type:e,field:n(),title:l,info:"",$required:!1}),props:(p,{t:i})=>u(i,e+".props",[r(),{type:"switch",field:"drag",title:"\u62D6\u62FD\u4E0A\u4F20",value:!1},{type:"select",field:"fileType",title:"\u56FE\u7247\u7C7B\u578B\u9650\u5236",value:["image/jpeg","image/png","image/gif"],options:[{label:"image/apng",value:"image/apng"},{label:"image/bmp",value:"image/bmp"},{label:"image/gif",value:"image/gif"},{label:"image/jpeg",value:"image/jpeg"},{label:"image/pjpeg",value:"image/pjpeg"},{label:"image/svg+xml",value:"image/svg+xml"},{label:"image/tiff",value:"image/tiff"},{label:"image/webp",value:"image/webp"},{label:"image/x-icon",value:"image/x-icon"}],props:{multiple:!0}},{type:"inputNumber",field:"fileSize",title:"\u5927\u5C0F\u9650\u5236(MB)",value:5,props:{min:0}},{type:"input",field:"height",title:"\u7EC4\u4EF6\u9AD8\u5EA6",value:"150px"},{type:"input",field:"width",title:"\u7EC4\u4EF6\u5BBD\u5EA6",value:"150px"},{type:"input",field:"borderradius",title:"\u7EC4\u4EF6\u8FB9\u6846\u5706\u89D2",value:"8px"},{type:"switch",field:"disabled",title:"\u662F\u5426\u663E\u793A\u5220\u9664\u6309\u94AE",value:!0},{type:"switch",field:"showBtnText",title:"\u662F\u5426\u663E\u793A\u6309\u94AE\u6587\u5B57",value:!0}])}})(),v=(()=>{const l="\u591A\u56FE\u4E0A\u4F20",e="UploadImgs";return{icon:"icon-upload",label:l,name:e,rule:()=>({type:e,field:n(),title:l,info:"",$required:!1}),props:(p,{t:i})=>u(i,e+".props",[r(),{type:"switch",field:"drag",title:"\u62D6\u62FD\u4E0A\u4F20",value:!1},{type:"select",field:"fileType",title:"\u56FE\u7247\u7C7B\u578B\u9650\u5236",value:["image/jpeg","image/png","image/gif"],options:[{label:"image/apng",value:"image/apng"},{label:"image/bmp",value:"image/bmp"},{label:"image/gif",value:"image/gif"},{label:"image/jpeg",value:"image/jpeg"},{label:"image/pjpeg",value:"image/pjpeg"},{label:"image/svg+xml",value:"image/svg+xml"},{label:"image/tiff",value:"image/tiff"},{label:"image/webp",value:"image/webp"},{label:"image/x-icon",value:"image/x-icon"}],props:{multiple:!0}},{type:"inputNumber",field:"fileSize",title:"\u5927\u5C0F\u9650\u5236(MB)",value:5,props:{min:0}},{type:"inputNumber",field:"limit",title:"\u6570\u91CF\u9650\u5236",value:5,props:{min:0}},{type:"input",field:"height",title:"\u7EC4\u4EF6\u9AD8\u5EA6",value:"150px"},{type:"input",field:"width",title:"\u7EC4\u4EF6\u5BBD\u5EA6",value:"150px"},{type:"input",field:"borderradius",title:"\u7EC4\u4EF6\u8FB9\u6846\u5706\u89D2",value:"8px"}])}})(),c=f({name:"UserSelect",label:"\u7528\u6237\u9009\u62E9\u5668",icon:"icon-user-o"}),h=f({name:"DeptSelect",label:"\u90E8\u95E8\u9009\u62E9\u5668",icon:"icon-address-card-o"}),w=(()=>{const l="\u5B57\u5178\u9009\u62E9\u5668",e="DictSelect",p=g(y),i=S([]);return b(async()=>{const a=await T();a&&a.length!==0&&(i.value=(a==null?void 0:a.map(o=>({label:o.name,value:o.type})))??[])}),{icon:"icon-doc-text",label:l,name:e,rule:()=>({type:e,field:n(),title:l,info:"",$required:!1}),props:(a,{t:o})=>u(o,e+".props",[r(),{type:"select",field:"dictType",title:"\u5B57\u5178\u7C7B\u578B",value:"",options:i.value},{type:"select",field:"valueType",title:"\u5B57\u5178\u503C\u7C7B\u578B",value:"str",options:[{label:"\u6570\u5B57",value:"int"},{label:"\u5B57\u7B26\u4E32",value:"str"},{label:"\u5E03\u5C14\u503C",value:"bool"}]},...p])}})(),x=f({name:"ApiSelect",label:"\u63A5\u53E3\u9009\u62E9\u5668",icon:"icon-server",props:[...j],event:["click","change","visibleChange","clear","blur","focus"]});b(async()=>{var l,e;await $(),(l=t.value)==null||l.removeMenuItem("upload"),(e=t.value)==null||e.removeMenuItem("fc-editor"),[m,d,s,v].forEach(p=>{var i,a;(i=t.value)==null||i.addComponent(p),(a=t.value)==null||a.appendMenuItem("main",{icon:p.icon,name:p.name,label:p.label})}),(()=>{var i;const p={name:"system",title:"\u7CFB\u7EDF\u5B57\u6BB5",list:[c,h,w,x].map(a=>{var o;return(o=t.value)==null||o.addComponent(a),{icon:a.icon,name:a.name,label:a.label}})};(i=t.value)==null||i.addMenu(p)})()})};export{M as u};
