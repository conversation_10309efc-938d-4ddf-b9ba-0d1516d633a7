import{a as S,d as q,D as N}from"./index-BeQABqnP.js";import{_ as z}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CxohUsTD.js";import{_ as C}from"./DictTag.vue_vue_type_script_lang-B0tTMPJ6.js";import{d as I}from"./formatTime-CN67D7Gb.js";import{S as L}from"./index-DCEvuXac.js";import{g as k}from"./commonBiz-Dnw63na0.js";import{f as d}from"./formatter-BLTmz7GT.js";import{ak as B,Z as A,_ as O}from"./form-designer-DQFPUccF.js";import{k as R,r as v,b as M,e as W,y as T,m as g,z as i,A as Y,l as Z,C as j,u as o,H as a,E as y,F as w}from"./form-create-B86qX0W_.js";const F={key:0,class:"p-4 text-center text-gray-500"},H=R({__name:"TransactionList",props:{inventoryId:{}},setup(Q){const{t:P}=S(),E=q(),u=Q,m=v(!1),s=v([]),c=v(new Map),b=async()=>{try{const t=await k();if(!t||t.length===0)return;t.forEach(r=>{r&&r.id&&r.name&&c.value.set(r.id,r.name)})}catch{}},x=t=>{if(!t&&t!==0)return"";if(typeof t=="number"){const r=c.value.get(t);if(r)return r}if(typeof t=="string"){for(const[e,l]of c.value)if(e.toString()===t)return l;const r=parseInt(t);if(!isNaN(r)){const e=c.value.get(r);if(e)return e}}return t.toString()},_=async()=>{if(u.inventoryId){m.value=!0;try{c.value.size===0&&await b();const t=await L.getTransactionListByInventoryId(u.inventoryId);s.value=t||[]}catch{s.value=[],E.error("\u83B7\u53D6\u5386\u53F2\u4EA4\u6613\u8BB0\u5F55\u5931\u8D25")}finally{m.value=!1}}else s.value=[]},D=({columns:t,data:r})=>{const e=[];return t.forEach((l,p)=>{if(p===0)return void(e[p]="\u5408\u8BA1");if(["quantity","auxiliaryQuantity","beforeQuantity","afterQuantity"].includes(l.property)){const h=r.map(f=>Number(f[l.property])||0).reduce((f,n)=>f+n,0);e[p]=d(h)}else e[p]=""}),e};return M(()=>u.inventoryId,()=>{u.inventoryId?_():s.value=[]},{immediate:!0}),W(async()=>{await b(),u.inventoryId&&_()}),(t,r)=>{const e=O,l=C,p=A,h=z,f=B;return g(),T(h,null,{default:i(()=>[Y((g(),T(p,{data:o(s),stripe:!0,"show-overflow-tooltip":!0,border:"",size:"small","show-summary":"","summary-method":D},{default:i(()=>[a(e,{label:"\u4E1A\u52A1\u5355\u53F7",align:"center",prop:"bizNo",width:"150"}),a(e,{label:"\u4EA4\u6613\u7C7B\u578B",align:"center",prop:"transactionType",width:"100"},{default:i(n=>[a(l,{type:o(N).SCM_BIZ_TYPE,value:n.row.transactionType},null,8,["type","value"])]),_:1}),a(e,{label:"\u4EA4\u6613\u65B9\u5411",align:"center",prop:"transactionDirection",width:"100"},{default:i(n=>[a(l,{type:o(N).INVENTORY_TRANSACTION_DIRECTION,value:n.row.transactionDirection},null,8,["type","value"])]),_:1}),a(e,{label:"\u79FB\u52A8\u65E5\u671F",align:"center",prop:"moveDate",formatter:o(I),width:"140"},null,8,["formatter"]),a(e,{label:"\u5E93\u5B58\u6279\u53F7",align:"center",prop:"inventoryBatchNo",width:"120"}),a(e,{label:"\u6570\u91CF",align:"center",prop:"quantity",width:"100"},{default:i(n=>[y(w(o(d)(n.row.quantity)),1)]),_:1}),a(e,{label:"\u5355\u4F4D",align:"center",width:"80"},{default:i(n=>[y(w(x(n.row.quantityUnit)),1)]),_:1}),a(e,{label:"\u51FA\u5165\u5E93\u524D\u6570\u91CF",align:"center",prop:"beforeQuantity",width:"120"},{default:i(n=>[y(w(o(d)(n.row.beforeQuantity)),1)]),_:1}),a(e,{label:"\u51FA\u5165\u5E93\u540E\u6570\u91CF",align:"center",prop:"afterQuantity",width:"120"},{default:i(n=>[y(w(o(d)(n.row.afterQuantity)),1)]),_:1}),a(e,{label:"\u6458\u8981",align:"center",prop:"summary",width:"150"}),a(e,{label:"\u79FB\u52A8\u7C7B\u578B",align:"center",prop:"moveType",width:"100"}),a(e,{label:"\u79FB\u52A8\u6E90\u4ED3\u5E93",align:"center",prop:"fromWarehouseName",width:"120"}),a(e,{label:"\u79FB\u52A8\u6E90\u4ED3\u4F4D",align:"center",prop:"fromLocationName",width:"120"}),a(e,{label:"\u79FB\u52A8\u5230\u4ED3\u5E93",align:"center",prop:"toWarehouseName",width:"120"}),a(e,{label:"\u79FB\u52A8\u5230\u4ED3\u4F4D",align:"center",prop:"toLocationName",width:"120"}),a(e,{label:"\u6765\u6E90\u5355\u53F7",align:"center",prop:"sourceNo",width:"150"}),a(e,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",formatter:o(I),width:"160"},null,8,["formatter"])]),_:1},8,["data"])),[[f,o(m)]]),o(m)||o(s).length!==0?j("",!0):(g(),Z("div",F," \u6682\u65E0\u5386\u53F2\u4EA4\u6613\u8BB0\u5F55 "))]),_:1})}}});export{H as _};
