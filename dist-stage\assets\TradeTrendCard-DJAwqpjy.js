import{H as f}from"./index-BeQABqnP.js";import{_ as A}from"./Echart.vue_vue_type_script_setup_true_lang-DR6A3B70.js";import{s as M,t as D,ad as V,ax as r}from"./form-designer-DQFPUccF.js";import{a as j}from"./trade-DFp4dyOy.js";import{f as u}from"./formatTime-CN67D7Gb.js";import{C as G}from"./CardTitle-BZDs3cG8.js";import{k as H,r as O,P as I,e as L,y as C,m as c,z as p,H as v,u as h,v as T,h as z,l as E,G as F,$ as U,E as X,F as Z}from"./form-create-B86qX0W_.js";import"./echarts-B337Cxlk.js";const $={class:"flex flex-row items-center justify-between"},q={class:"flex flex-row items-center gap-2"},B=H({name:"TradeTrendCard",__name:"TradeTrendCard",setup(J){const o=O(1),x=O(!0),b=new Map().set(1,{name:"30\u5929",series:[{name:"\u8BA2\u5355\u91D1\u989D",type:"bar",smooth:!0,data:[]},{name:"\u8BA2\u5355\u6570\u91CF",type:"line",smooth:!0,data:[]}]}).set(7,{name:"\u5468",series:[{name:"\u4E0A\u5468\u91D1\u989D",type:"bar",smooth:!0,data:[]},{name:"\u672C\u5468\u91D1\u989D",type:"bar",smooth:!0,data:[]},{name:"\u4E0A\u5468\u6570\u91CF",type:"line",smooth:!0,data:[]},{name:"\u672C\u5468\u6570\u91CF",type:"line",smooth:!0,data:[]}]}).set(30,{name:"\u6708",series:[{name:"\u4E0A\u6708\u91D1\u989D",type:"bar",smooth:!0,data:[]},{name:"\u672C\u6708\u91D1\u989D",type:"bar",smooth:!0,data:[]},{name:"\u4E0A\u6708\u6570\u91CF",type:"line",smooth:!0,data:[]},{name:"\u672C\u6708\u6570\u91CF",type:"line",smooth:!0,data:[]}]}).set(365,{name:"\u5E74",series:[{name:"\u53BB\u5E74\u91D1\u989D",type:"bar",smooth:!0,data:[]},{name:"\u4ECA\u5E74\u91D1\u989D",type:"bar",smooth:!0,data:[]},{name:"\u53BB\u5E74\u6570\u91CF",type:"line",smooth:!0,data:[]},{name:"\u4ECA\u5E74\u6570\u91CF",type:"line",smooth:!0,data:[]}]}),n=I({grid:{left:20,right:20,bottom:20,top:80,containLabel:!0},legend:{top:50,data:[]},series:[],toolbox:{feature:{dataZoom:{yAxisIndex:!1},brush:{type:["lineX","clear"]},saveAsImage:{show:!0,name:"\u8BA2\u5355\u91CF\u8D8B\u52BF"}}},tooltip:{trigger:"axis",axisPointer:{type:"cross"},padding:[5,10]},xAxis:{type:"category",inverse:!0,boundaryGap:!1,axisTick:{show:!1},data:[],axisLabel:{formatter:e=>{switch(o.value){case 1:return u(e,"MM-DD");case 7:let t=u(e,"ddd");return t=="0"&&(t="\u65E5"),"\u5468"+t;case 30:return u(e,"D");case 365:return u(e,"M")+"\u6708";default:return e}}}},yAxis:{axisTick:{show:!1}}}),g=async()=>{let e,t;switch(o.value){case 7:e=r().startOf("week"),t=r().endOf("week");break;case 30:e=r().startOf("month"),t=r().endOf("month");break;case 365:e=r().startOf("year"),t=r().endOf("year");break;default:e=r().subtract(30,"day").startOf("d"),t=r().endOf("d")}await _(e,t)},_=async(e,t)=>{var i,d,l,w,P,k;x.value=!0;const y=await j(o.value,e,t),m=[],s=[...b.get(o.value).series];for(let a of y)m.push(a.value.date),s.length===2?(s[0].data.push(f(((i=a==null?void 0:a.value)==null?void 0:i.orderPayPrice)||0)),s[1].data.push(((d=a==null?void 0:a.value)==null?void 0:d.orderPayCount)||0)):(s[0].data.push(f(((l=a==null?void 0:a.reference)==null?void 0:l.orderPayPrice)||0)),s[1].data.push(f(((w=a==null?void 0:a.value)==null?void 0:w.orderPayPrice)||0)),s[2].data.push(((P=a==null?void 0:a.reference)==null?void 0:P.orderPayCount)||0),s[3].data.push(((k=a==null?void 0:a.value)==null?void 0:k.orderPayCount)||0));n.xAxis.data=m,n.series=s,n.legend.data=s.map(a=>a.name),x.value=!1};return L(()=>{g()}),(e,t)=>{const y=D,m=M,s=A,i=V;return c(),C(i,{shadow:"never"},{header:p(()=>[T("div",$,[v(h(G),{title:"\u51FA\u5E93\u6982\u89C8"}),T("div",q,[v(m,{modelValue:h(o),"onUpdate:modelValue":t[0]||(t[0]=d=>z(o)?o.value=d:null),onChange:g},{default:p(()=>[(c(!0),E(F,null,U(h(b).entries(),([d,l])=>(c(),C(y,{key:d,value:d},{default:p(()=>[X(Z(l.name),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])])])]),default:p(()=>[v(s,{height:300,options:h(n)},null,8,["options"])]),_:1})}}});export{B as default};
