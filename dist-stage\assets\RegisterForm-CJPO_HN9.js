import{a as A,u as B,q as G,ah as H,al as O,y as $,t as D,z as J,A as k,am as K,c as M}from"./index-BeQABqnP.js";import{_ as Q}from"./Verify-Bd0HcNQH.js";import{_ as W}from"./XButton-BWhMH4NC.js";import{k as X,r as m,c as Y,u as e,P as Z,b as ee,e as ae,A as re,I as se,y as g,m as f,z as l,H as s,C as V}from"./form-create-B86qX0W_.js";import{u as le,L as oe,_ as te}from"./LoginFormTitle.vue_vue_type_script_setup_true_lang-CEAfLqt4.js";import{u as h}from"./useIcon-F2jR7oPp.js";import{i as ie,j as ne,aj as pe,k as me,h as ue,d as ce}from"./form-designer-DQFPUccF.js";import"./_commonjs-dynamic-modules-BHR_E30J.js";const de=M(X({name:"RegisterForm",__name:"RegisterForm",setup(ge){const{t:p}=A(),v=h({icon:"ep:house"}),w=h({icon:"ep:avatar"}),x=h({icon:"ep:lock"}),E=m(),{handleBackLogin:z,getLoginState:P}=le(),{currentRoute:q,push:N}=B(),R=G(),u=m(""),b=m(!1),_=m(),U=m("blockPuzzle"),I=Y(()=>e(P)===oe.REGISTER),S={tenantName:[{required:!0,trigger:"blur",message:"\u8BF7\u8F93\u5165\u60A8\u6240\u5C5E\u7684\u79DF\u6237"},{min:2,max:20,message:"\u79DF\u6237\u8D26\u53F7\u957F\u5EA6\u5FC5\u987B\u4ECB\u4E8E 2 \u548C 20 \u4E4B\u95F4",trigger:"blur"}],username:[{required:!0,trigger:"blur",message:"\u8BF7\u8F93\u5165\u60A8\u7684\u8D26\u53F7"},{min:4,max:30,message:"\u7528\u6237\u8D26\u53F7\u957F\u5EA6\u5FC5\u987B\u4ECB\u4E8E 4 \u548C 30 \u4E4B\u95F4",trigger:"blur"}],nickname:[{required:!0,trigger:"blur",message:"\u8BF7\u8F93\u5165\u60A8\u7684\u6635\u79F0"},{min:0,max:30,message:"\u6635\u79F0\u957F\u5EA6\u5FC5\u987B\u4ECB\u4E8E 0 \u548C 30 \u4E4B\u95F4",trigger:"blur"}],password:[{required:!0,trigger:"blur",message:"\u8BF7\u8F93\u5165\u60A8\u7684\u5BC6\u7801"},{min:5,max:20,message:"\u7528\u6237\u5BC6\u7801\u957F\u5EA6\u5FC5\u987B\u4ECB\u4E8E 5 \u548C 20 \u4E4B\u95F4",trigger:"blur"},{pattern:/^[^<>"'|\\]+$/,message:`\u4E0D\u80FD\u5305\u542B\u975E\u6CD5\u5B57\u7B26\uFF1A< > " ' \\ |`,trigger:"blur"}],confirmPassword:[{required:!0,trigger:"blur",message:"\u8BF7\u518D\u6B21\u8F93\u5165\u60A8\u7684\u5BC6\u7801"},{required:!0,validator:(o,a,i)=>{r.registerForm.password!==a?i(new Error("\u4E24\u6B21\u8F93\u5165\u7684\u5BC6\u7801\u4E0D\u4E00\u81F4")):i()},trigger:"blur"}]},r=Z({isShowPassword:!1,captchaEnable:"true",tenantEnable:"true",registerForm:{tenantName:"\u6CD5\u9EA6\u514B\u65AF",nickname:"",tenantId:0,username:"",password:"",confirmPassword:"",captchaVerification:""}}),y=async o=>{d.value=!0;try{r.tenantEnable&&(await L(),r.registerForm.tenantId=H()),r.captchaEnable&&(r.registerForm.captchaVerification=o.captchaVerification);const a=await O(r.registerForm);if(!a)return;d.value=ce.service({lock:!0,text:"\u6B63\u5728\u52A0\u8F7D\u7CFB\u7EDF\u4E2D...",background:"rgba(0, 0, 0, 0.7)"}),$(),D(a),u.value||(u.value="/"),u.value.indexOf("sso")!==-1?window.location.href=window.location.href.replace("/login?redirect=",""):N({path:u.value||R.addRouters[0].path})}finally{b.value=!1,d.value.close()}},L=async()=>{if(r.tenantEnable==="true"){const o=await J(r.registerForm.tenantName);k(o)}},d=m();return ee(()=>q.value,o=>{var a;u.value=(a=o==null?void 0:o.query)==null?void 0:a.redirect},{immediate:!0}),ae(()=>{(async()=>{if(r.tenantEnable==="true"){const o=location.host,a=await K(o);a&&(r.registerForm.tenantName=a.name,k(a.id))}})()}),(o,a)=>{const i=pe,n=ne,c=me,F=W,T=Q,C=ie,j=ue;return re((f(),g(j,{ref_key:"formLogin",ref:E,model:e(r).registerForm,rules:S,class:"login-form","label-position":"top","label-width":"120px",size:"large"},{default:l(()=>[s(C,{class:"mx-[-10px]"},{default:l(()=>[s(n,{span:24,class:"px-10px"},{default:l(()=>[s(i,null,{default:l(()=>[s(te,{class:"w-full"})]),_:1})]),_:1}),s(n,{span:24,class:"px-10px"},{default:l(()=>[e(r).tenantEnable==="true"?(f(),g(i,{key:0,prop:"tenantName"},{default:l(()=>[s(c,{modelValue:e(r).registerForm.tenantName,"onUpdate:modelValue":a[0]||(a[0]=t=>e(r).registerForm.tenantName=t),placeholder:e(p)("login.tenantname"),"prefix-icon":e(v),link:"",type:"primary",size:"large"},null,8,["modelValue","placeholder","prefix-icon"])]),_:1})):V("",!0)]),_:1}),s(n,{span:24,class:"px-10px"},{default:l(()=>[s(i,{prop:"username"},{default:l(()=>[s(c,{modelValue:e(r).registerForm.username,"onUpdate:modelValue":a[1]||(a[1]=t=>e(r).registerForm.username=t),placeholder:e(p)("login.username"),size:"large","prefix-icon":e(w)},null,8,["modelValue","placeholder","prefix-icon"])]),_:1})]),_:1}),s(n,{span:24,class:"px-10px"},{default:l(()=>[s(i,{prop:"username"},{default:l(()=>[s(c,{modelValue:e(r).registerForm.nickname,"onUpdate:modelValue":a[2]||(a[2]=t=>e(r).registerForm.nickname=t),placeholder:"\u6635\u79F0",size:"large","prefix-icon":e(w)},null,8,["modelValue","prefix-icon"])]),_:1})]),_:1}),s(n,{span:24,class:"px-10px"},{default:l(()=>[s(i,{prop:"password"},{default:l(()=>[s(c,{modelValue:e(r).registerForm.password,"onUpdate:modelValue":a[3]||(a[3]=t=>e(r).registerForm.password=t),type:"password","auto-complete":"off",placeholder:e(p)("login.password"),size:"large","prefix-icon":e(x),"show-password":""},null,8,["modelValue","placeholder","prefix-icon"])]),_:1})]),_:1}),s(n,{span:24,class:"px-10px"},{default:l(()=>[s(i,{prop:"confirmPassword"},{default:l(()=>[s(c,{modelValue:e(r).registerForm.confirmPassword,"onUpdate:modelValue":a[4]||(a[4]=t=>e(r).registerForm.confirmPassword=t),type:"password",size:"large","auto-complete":"off",placeholder:e(p)("login.checkPassword"),"prefix-icon":e(x),"show-password":""},null,8,["modelValue","placeholder","prefix-icon"])]),_:1})]),_:1}),s(n,{span:24,class:"px-10px"},{default:l(()=>[s(i,null,{default:l(()=>[s(F,{loading:e(b),title:e(p)("login.register"),class:"w-full",type:"primary",onClick:a[5]||(a[5]=t=>(async()=>{r.captchaEnable==="false"?await y({}):_.value.show()})())},null,8,["loading","title"])]),_:1})]),_:1}),e(r).captchaEnable==="true"?(f(),g(T,{key:0,ref_key:"verify",ref:_,captchaType:e(U),imgSize:{width:"400px",height:"200px"},mode:"pop",onSuccess:y},null,8,["captchaType"])):V("",!0)]),_:1}),s(F,{title:e(p)("login.hasUser"),class:"w-full",onClick:a[6]||(a[6]=t=>e(z)())},null,8,["title"])]),_:1},8,["model"])),[[se,e(I)]])}}}),[["__scopeId","data-v-a386fe91"]]);export{de as default};
