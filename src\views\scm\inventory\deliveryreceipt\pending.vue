<script lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { DeliveryNoticeApi, DeliveryNoticeVO } from '@/api/scm/sale/deliverynotice'
import { dateFormatter, dateFormatter2 } from '@/utils/formatTime'
import { amountTableFormatter, quantityTableFormatter, formatQuantity, formatAmount } from '@/utils/formatter'
import { DICT_TYPE } from '@/utils/dict'
import { useRouter } from 'vue-router'
import { useClipboard } from '@vueuse/core'

export default {
  name: 'PendingDeliveryNotice',
  emits: ['openDeliveryForm'],
  setup(props, { emit }) {
    const message = useMessage() // 消息弹窗
    const loading = ref(true)
    const list = ref<DeliveryNoticeVO[]>([])
    const total = ref(0)
    const queryParams = reactive({
      pageNo: 1,
      pageSize: 10,
      approveStatus: '3',
      deliveryStatus: '0',
      detail: true
    })
    const router = useRouter()
    const { copy } = useClipboard() // 复制功能

    /** 复制订单单号 */
    const copyOrderNo = async (orderNo: string) => {
      try {
        await copy(orderNo)
        message.success('订单单号复制成功')
      } catch (error) {
        message.error('复制失败')
      }
    }

    /** 格式化日期 */
    const formatDate = (timestamp: number) => {
      if (!timestamp) return ''
      return new Date(timestamp).toLocaleDateString('zh-CN')
    }

    /** 表格汇总方法 */
    const getSummaries = (param: any) => {
      const { columns, data } = param
      const sums: string[] = []
      columns.forEach((column: any, index: number) => {
        if (index === 0) {
          sums[index] = '合计'
          return
        }
        // 数量列汇总 (第6列)
        if (column.property === 'materialQuantity' || index === 6) {
          const values = data.map((item: any) => Number(item.materialQuantity) || 0)
          if (!values.every((value: number) => Number.isNaN(value))) {
            const total = values.reduce((prev: number, curr: number) => {
              const value = Number(curr)
              if (!Number.isNaN(value)) {
                return prev + value
              } else {
                return prev
              }
            }, 0)
            sums[index] = formatQuantity(total)
          } else {
            sums[index] = ''
          }
        }
        // 金额列汇总 (第8列)
        else if (column.property === 'totalAmount' || index === 8) {
          const values = data.map((item: any) => Number(item.totalAmount) || 0)
          if (!values.every((value: number) => Number.isNaN(value))) {
            const total = values.reduce((prev: number, curr: number) => {
              const value = Number(curr)
              if (!Number.isNaN(value)) {
                return prev + value
              } else {
                return prev
              }
            }, 0)
            sums[index] = formatAmount(total)
          } else {
            sums[index] = ''
          }
        } else {
          sums[index] = ''
        }
      })
      return sums
    }

    /** 表格行合并方法 */
    const spanMethod = ({ row, columnIndex }: any) => {
      // 需要合并的主表列索引
      // 0: 选择框, 1: 订单单号, 2: 客户名称, 9: 发货日期
      // 11: 联系人, 12: 联系电话, 13: 收货地址, 14: 送达日期
      // 15: 运输方式, 16: 物流公司, 17: 物流单号, 18: 来源类型
      // 19: 来源单, 20: 说明事项, 21: 创建时间, 22: 操作
      const mergeColumns = [0, 1, 2, 9, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22]

      if (mergeColumns.includes(columnIndex)) {
        if (row._detailIndex === 0 || row._detailIndex === undefined) {
          return {
            rowspan: row._rowSpan || 1,
            colspan: 1
          }
        } else {
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      }

      // 明细列不合并
      return {
        rowspan: 1,
        colspan: 1
      }
    }

    /** 查询列表 */
    const getList = async () => {
      loading.value = true
      try {
        const data = await DeliveryNoticeApi.getDeliveryNoticePage(queryParams)
        const mergedList: any[] = []
        if (data.list && data.list.length > 0) {
          data.list.forEach((mainItem: any) => {
            if (mainItem.details && mainItem.details.length > 0) {
              mainItem.details.forEach((detail: any, index: number) => {
                mergedList.push({
                  ...mainItem,
                  ...detail,
                  _originalId: mainItem.id,
                  _detailIndex: index,
                  id: `${mainItem.id}_${detail.id || index}`,
                  _showActions: false,
                  _rowSpan: index === 0 ? mainItem.details.length : 0
                })
              })
            } else {
              mergedList.push({
                ...mainItem,
                _originalId: mainItem.id,
                _showActions: false,
                _rowSpan: 1
              })
            }
          })
        }
        list.value = mergedList
        total.value = data.total
      } finally {
        loading.value = false
      }
    }

    /** 跳转到详情页面 */
    const handleDetail = (id: number) => {
      router.push(`/scm/sale/deliverynotice/detail/${id}`)
    }

    /** 处理出库操作 */
    const handleDelivery = async (id: number) => {
      try {
        // 获取发货通知单主数据和明细数据
        const deliverData = await DeliveryNoticeApi.getDeliveryNotice(id)
        const deliveryDetailData = await DeliveryNoticeApi.getDeliveryNoticeDetailListByNoticeId(id)

        // 向父组件发送事件，传递发货通知单数据
        emit('openDeliveryForm', {
          type: 'create',
          sourceData: {
            sourceType: 'delivery_notice', // 来源类型：发货通知单
            sourceId: deliverData.id, // 来源单据ID
            sourceNo: deliverData.orderNo, // 来源单编号
            objectName: deliverData.customerName, // 客户名称
            objectOrderNo: deliverData.customerOrderNo || '', // 客户订单号
            deliveryDate: deliverData.deliveryDate, // 发货日期
            // 传递更多需要的数据
            customerContact: deliverData.customerContact || '', // 客户联系人
            customerPhone: deliverData.customerPhone || '', // 客户电话
            customerAddress: deliverData.customerAddress || '', // 客户地址
            expectedArrivalDate: deliverData.expectedArrivalDate, // 预计到达日期
            transportMethod: deliverData.transportMethod || '', // 运输方式
            logisticsCompany: deliverData.logisticsCompany || '', // 物流公司
            logisticsTrackingNumber: deliverData.logisticsTrackingNumber || '', // 物流单号
            logisticsCost: deliverData.logisticsCost || '', // 物流费用
            logisticsPhone: deliverData.logisticsPhone || '', // 物流电话
            logisticsPlateNumber: deliverData.logisticsPlateNumber || '', // 物流车牌号
            logisticsContact: deliverData.logisticsContact || '', // 物流联系人
            remarks: deliverData.remarks || '', // 备注
            customerId: deliverData.customerId, // 客户ID
            kdId: deliverData.kdId, // 快递ID
            // 明细数据转换
            details: deliveryDetailData.map((detail: any) => ({
              id: detail.id, // 明细ID
              num: detail.num, // 序号
              noticeId: detail.noticeId, // 通知单ID
              noticeNo: detail.noticeNo, // 通知单号
              saleOrderId: detail.saleOrderId, // 销售订单ID
              saleOrderNo: detail.saleOrderNo, // 销售订单号
              saleOrderDetailId: detail.saleOrderDetailId, // 销售订单明细ID
              materialId: detail.materialId, // 物料ID
              materialCode: detail.materialCode, // 物料编码
              materialName: detail.materialName, // 物料名称
              materialSpec: detail.materialSpec, // 物料规格
              materialQuantity: detail.materialQuantity, // 物料数量
              materialUnit: detail.materialUnit, // 物料单位
              batchNo: detail.batchNo || '', // 批号
              materialUnitPrice: detail.materialUnitPrice, // 物料单价
              totalAmount: detail.totalAmount, // 总金额
              standardQuantity: detail.standardQuantity, // 标准数量
              standardUnit: detail.standardUnit, // 标准单位
              specQuantity: detail.specQuantity, // 规格数量
              packagingRequirements: detail.packagingRequirements || '', // 包装要求
              sourceType: 'delivery_notice', // 来源类型
              sourceId: deliverData.id, // 来源ID
              sourceNo: deliverData.orderNo, // 来源单号
              sourceDetailId: detail.id, // 来源明细ID
              warehouseId: detail.warehouseId, // 仓库ID
              locationId: detail.locationId, // 库位ID
              deliveryDate: detail.deliveryDate, // 发货日期
              kdOrderId: detail.kdOrderId, // 快递订单ID
              kdId: detail.kdId, // 快递ID
              remark: detail.remark || '', // 备注
              note: detail.note || '' // 说明
            }))
          }
        })
      } catch (error) {
        console.error('获取发货通知单数据失败:', error)
        message.error('获取发货通知单数据失败，请重试')
      }
    }

    onMounted(() => {
      getList()
    })

    return {
      loading,
      list,
      total,
      queryParams,
      getList,
      getSummaries,
      spanMethod,
      formatDate,
      amountTableFormatter,
      quantityTableFormatter,
      handleDetail,
      handleDelivery,
      copyOrderNo,
      DICT_TYPE,
      dateFormatter,
      dateFormatter2
    }
  }
}
</script>

<template>
  <el-table
    v-loading="loading"
    :data="list"
    :stripe="true"
    border
    :show-overflow-tooltip="true"
    highlight-current-row
    show-summary
    :summary-method="getSummaries"
    :span-method="spanMethod"
  >
    <!-- <el-table-column type="selection" width="60" fixed="left" /> -->
    <el-table-column label="订单单号" align="left" prop="orderNo" width="160px" fixed="left">
      <template #default="scope">
        <div class="order-no-container vertical">
          <div class="order-no-cell">
            <div class="order-no-content">
              <el-button
                link
                type="primary"
                @click="handleDetail(scope.row._originalId || scope.row.id)"
                class="order-no-link"
              >
                {{ scope.row.orderNo }}
              </el-button>
            </div>
            <el-button
              link
              type="info"
              @click="copyOrderNo(scope.row.orderNo)"
              class="copy-btn copy-btn-fixed"
              size="small"
            >
              <Icon icon="ep:copy-document" :size="12"/>
            </el-button>
          </div>
          <dict-tag
            v-if="scope.row.approveStatus"
            :type="DICT_TYPE.APPROVE_STATUS"
            :value="scope.row.approveStatus"
            class="approval-tag"
          />
        </div>
      </template>
    </el-table-column>
    <el-table-column label="客户名称" align="left" prop="customerName" width="150px" fixed="left"/>
    <el-table-column label="物料编码" align="left" prop="materialCode" width="120px"/>
    <el-table-column label="物料名称" align="left" prop="materialName" width="150px"/>
    <el-table-column label="规格" align="center" prop="materialSpec" width="100px"/>
    <el-table-column label="数量" align="center" prop="materialQuantity" width="110px" :formatter="quantityTableFormatter">
      <template #default="scope">
        <el-tag type="success" v-if="scope.row.materialQuantity">
          {{ quantityTableFormatter(null, null, scope.row.materialQuantity, null) }}
        </el-tag>
      </template>
    </el-table-column>
    <el-table-column label="单价" align="center" prop="materialUnitPrice" width="110px" :formatter="amountTableFormatter">
      <template #default="scope">
        <span v-if="scope.row.materialUnitPrice">
          {{ amountTableFormatter(null, null, scope.row.materialUnitPrice, null) }}
        </span>
      </template>
    </el-table-column>
    <el-table-column label="金额" align="center" prop="totalAmount" width="120px" :formatter="amountTableFormatter">
      <template #default="scope">
        <el-tag type="warning" v-if="scope.row.totalAmount">
          {{ amountTableFormatter(null, null, scope.row.totalAmount, null) }}
        </el-tag>
      </template>
    </el-table-column>
    <el-table-column label="发货日期" align="center" prop="deliveryDate" width="100px">
      <template #default="scope">
        <span v-if="scope.row.deliveryDate">
          {{ formatDate(scope.row.deliveryDate) }}
        </span>
      </template>
    </el-table-column>
    <el-table-column label="备注" align="left" prop="remark" width="120px"/>
    <el-table-column label="联系人" align="left" prop="customerContact" />
    <el-table-column label="联系电话" align="left" prop="customerPhone" width="100px"/>
    <el-table-column label="收货地址" align="left" prop="customerAddress" width="100px"/>
    <el-table-column label="送达日期" align="center" prop="expectedArrivalDate" width="100px" :formatter="dateFormatter2"/>
    <el-table-column label="运输方式" align="center" prop="transportMethod" width="100px"/>
    <el-table-column label="物流公司" align="left" prop="logisticsCompany" width="100px"/>
    <el-table-column label="物流单号" align="left" prop="logisticsTrackingNumber" width="100px"/>
    <el-table-column label="来源类型" align="center" width="120px">
      <template #default>
        <el-tag type="info" size="small">发货通知单</el-tag>
      </template>
    </el-table-column>
    <el-table-column label="来源单" align="left" prop="orderNo" width="140px">
      <template #default="scope">
        <div class="order-no-cell">
          <div class="order-no-content">
            <span>{{ scope.row.orderNo }}</span>
          </div>
          <el-button
            link
            type="info"
            @click="copyOrderNo(scope.row.orderNo)"
            class="copy-btn copy-btn-fixed"
            size="small"
          >
            <Icon icon="ep:copy-document" :size="12"/>
          </el-button>
        </div>
      </template>
    </el-table-column>
    <el-table-column label="说明事项" align="left" prop="remarks" width="100px"/>
    <el-table-column
      label="创建时间"
      align="center"
      prop="createTime"
      :formatter="dateFormatter"
      width="180px"
    />
    <!-- 操作列 -->
    <el-table-column label="操作" align="center" width="100px" fixed="right">
      <template #default="scope">
        <el-button
          type="primary"
          size="small"
          @click="handleDelivery(scope.row._originalId)"
        >
          出库
        </el-button>
      </template>
    </el-table-column>
  </el-table>
</template>

<style scoped>
.order-no-container {
  display: flex;
  align-items: center;
  gap: 6px;
  flex-wrap: wrap;
  padding: 4px 0;
}
.order-no-container.vertical {
  flex-direction: column;
  align-items: flex-start;
  gap: 2px;
}
.order-no-link {
  font-weight: 500;
  padding: 0;
  height: auto;
  line-height: 1.4;
  flex-shrink: 0;
}
.order-no-link:hover {
  text-decoration: underline;
}
.order-no-cell {
  display: flex;
  align-items: center;
  width: 100%;
  min-height: 20px;
  gap: 5px; /* 文字和图标之间保持一点点间隔 */
}

.order-no-content {
  flex: 0 1 auto; /* 改为自适应宽度，不占满剩余空间 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: calc(100% - 16px); /* 为图标预留空间 */
}
.copy-btn {
  padding: 2px !important;
  height: 20px !important;
  min-height: 20px !important;
  width: 20px !important;
  font-size: 12px;
  opacity: 0.6;
  transition: opacity 0.2s;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}


.copy-btn:hover {
  opacity: 1;
}
.approval-tag :deep(.el-tag) {
  font-size: 9px !important;
  padding: 1px 4px !important;
  height: 16px !important;
  line-height: 14px !important;
  border-radius: 6px !important;
  font-weight: 500 !important;
  flex-shrink: 0;
}
</style>
