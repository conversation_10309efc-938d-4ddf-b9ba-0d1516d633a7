import{_ as y}from"./index-BeQABqnP.js";import{U as C}from"./index-B76WTMRw.js";import{ak as N,h as D,Z as j,_ as G,aj as P,k as z,i as A,f as B}from"./form-designer-DQFPUccF.js";import{k as E,r as c,P as F,b as H,l as L,m as v,G as R,A as Z,H as e,u,y as q,z as a,E as J}from"./form-create-B86qX0W_.js";const K=E({__name:"UnitForm",props:{groupId:{}},setup(x,{expose:g}){const f=x,s=c(!1),t=c([]),p=F({}),V=c();H(()=>f.groupId,async i=>{if(t.value=[],i)try{s.value=!0,t.value=await C.getUnitListByGroupId(i)}finally{s.value=!1}},{immediate:!0});const h=()=>{const i={id:void 0,groupId:void 0,groupCode:void 0,groupName:void 0,code:void 0,name:void 0,coefficient:void 0,remark:void 0,parentId:void 0};i.groupId=f.groupId,t.value.push(i)};return g({validate:()=>V.value.validate(),getData:()=>t.value}),(i,_)=>{const r=G,n=z,m=P,b=y,w=j,U=D,$=B,I=A,k=N;return v(),L(R,null,[Z((v(),q(U,{ref_key:"formRef",ref:V,model:u(t),rules:u(p),"label-width":"0px","inline-message":!0},{default:a(()=>[e(w,{data:u(t),class:"-mt-10px"},{default:a(()=>[e(r,{label:"\u5E8F\u53F7",type:"index",width:"100"}),e(r,{label:"\u5206\u7EC4\u7F16\u7801","min-width":"150"},{default:a(({row:l,$index:d})=>[e(m,{prop:`${d}.groupCode`,rules:u(p).groupCode,class:"mb-0px!"},{default:a(()=>[e(n,{modelValue:l.groupCode,"onUpdate:modelValue":o=>l.groupCode=o,placeholder:"\u8BF7\u8F93\u5165\u5206\u7EC4\u7F16\u7801"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])]),_:1}),e(r,{label:"\u5206\u7EC4\u540D\u79F0","min-width":"150"},{default:a(({row:l,$index:d})=>[e(m,{prop:`${d}.groupName`,rules:u(p).groupName,class:"mb-0px!"},{default:a(()=>[e(n,{modelValue:l.groupName,"onUpdate:modelValue":o=>l.groupName=o,placeholder:"\u8BF7\u8F93\u5165\u5206\u7EC4\u540D\u79F0"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])]),_:1}),e(r,{label:"\u7F16\u7801","min-width":"150"},{default:a(({row:l,$index:d})=>[e(m,{prop:`${d}.code`,rules:u(p).code,class:"mb-0px!"},{default:a(()=>[e(n,{modelValue:l.code,"onUpdate:modelValue":o=>l.code=o,placeholder:"\u8BF7\u8F93\u5165\u7F16\u7801"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])]),_:1}),e(r,{label:"\u540D\u79F0","min-width":"150"},{default:a(({row:l,$index:d})=>[e(m,{prop:`${d}.name`,rules:u(p).name,class:"mb-0px!"},{default:a(()=>[e(n,{modelValue:l.name,"onUpdate:modelValue":o=>l.name=o,placeholder:"\u8BF7\u8F93\u5165\u540D\u79F0"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])]),_:1}),e(r,{label:"\u6362\u7B97\u7387","min-width":"150"},{default:a(({row:l,$index:d})=>[e(m,{prop:`${d}.coefficient`,rules:u(p).coefficient,class:"mb-0px!"},{default:a(()=>[e(n,{modelValue:l.coefficient,"onUpdate:modelValue":o=>l.coefficient=o,placeholder:"\u8BF7\u8F93\u5165\u6362\u7B97\u7387"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])]),_:1}),e(r,{label:"\u5907\u6CE8","min-width":"150"},{default:a(({row:l,$index:d})=>[e(m,{prop:`${d}.remark`,rules:u(p).remark,class:"mb-0px!"},{default:a(()=>[e(n,{modelValue:l.remark,"onUpdate:modelValue":o=>l.remark=o,placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])]),_:1}),e(r,{label:"\u4E0A\u7EA7ID","min-width":"150"},{default:a(({row:l,$index:d})=>[e(m,{prop:`${d}.parentId`,rules:u(p).parentId,class:"mb-0px!"},{default:a(()=>[e(n,{modelValue:l.parentId,"onUpdate:modelValue":o=>l.parentId=o,placeholder:"\u8BF7\u8F93\u5165\u4E0A\u7EA7ID"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])]),_:1}),e(r,{align:"center",fixed:"right",label:"\u64CD\u4F5C",width:"60"},{default:a(({$index:l})=>[e(b,{icon:"ep:delete",onClick:d=>{return o=l,void t.value.splice(o,1);var o},color:"#f56c6c"},null,8,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1},8,["model","rules"])),[[k,u(s)]]),e(I,{justify:"center",class:"mt-3"},{default:a(()=>[e($,{onClick:h,round:""},{default:a(()=>_[0]||(_[0]=[J("+ \u6DFB\u52A0\u5355\u4F4D")])),_:1})]),_:1})],64)}}});export{K as _};
