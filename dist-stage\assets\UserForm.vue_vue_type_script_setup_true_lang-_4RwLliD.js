import{a as H,d as P,h as U,D as x,Z as X}from"./index-BeQABqnP.js";import{_ as Y}from"./Dialog.vue_vue_type_style_index_0_lang-DpoeWsKb.js";import{g as Z,u as $}from"./index-ge1eh6aq.js";import{g as B}from"./index-COn6lqB-.js";import{d as J}from"./tree-COGD3qag.js";import{_ as W}from"./MemberTagSelect.vue_vue_type_script_setup_true_lang-B5B6HjiV.js";import{_ as K}from"./MemberGroupSelect.vue_vue_type_script_setup_true_lang-DFCPAacX.js";import{h as L,aj as Q,k as ee,s as ae,u as le,F as oe,a1 as de,ak as ue,f as te}from"./form-designer-DQFPUccF.js";import{k as se,r,P as re,y as f,m as p,z as u,A as me,u as e,H as d,l as w,G as S,$ as E,E as c,F,h as ie}from"./form-create-B86qX0W_.js";const pe=se({__name:"UserForm",emits:["success"],setup(ne,{expose:q,emit:C}){const{t:V}=H(),b=P(),m=r(!1),_=r(""),i=r(!1),k=r(""),o=r({id:void 0,mobile:void 0,password:void 0,status:void 0,nickname:void 0,avatar:void 0,name:void 0,sex:void 0,areaId:void 0,birthday:void 0,mark:void 0,tagIds:[],groupId:void 0}),M=re({mobile:[{required:!0,message:"\u624B\u673A\u53F7\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],status:[{required:!0,message:"\u72B6\u6001\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),n=r(),y=r([]);q({open:async(s,a)=>{if(m.value=!0,_.value=V("action."+s),k.value=s,T(),a){i.value=!0;try{o.value=await Z(a)}finally{i.value=!1}}y.value=await B()}});const O=C,R=async()=>{if(n&&await n.value.validate()){i.value=!0;try{const s=o.value;k.value==="create"?b.success(V("common.createSuccess")):(await $(s),b.success(V("common.updateSuccess"))),m.value=!1,O("success")}finally{i.value=!1}}},T=()=>{var s;o.value={id:void 0,mobile:void 0,password:void 0,status:void 0,nickname:void 0,avatar:void 0,name:void 0,sex:void 0,areaId:void 0,birthday:void 0,mark:void 0,tagIds:[],groupId:void 0},(s=n.value)==null||s.resetFields()};return(s,a)=>{const v=ee,t=Q,g=le,h=ae,A=X,N=oe,j=de,z=L,I=te,D=Y,G=ue;return p(),f(D,{title:e(_),modelValue:e(m),"onUpdate:modelValue":a[12]||(a[12]=l=>ie(m)?m.value=l:null)},{footer:u(()=>[d(I,{onClick:R,type:"primary",disabled:e(i)},{default:u(()=>a[13]||(a[13]=[c("\u786E \u5B9A")])),_:1},8,["disabled"]),d(I,{onClick:a[11]||(a[11]=l=>m.value=!1)},{default:u(()=>a[14]||(a[14]=[c("\u53D6 \u6D88")])),_:1})]),default:u(()=>[me((p(),f(z,{ref_key:"formRef",ref:n,model:e(o),rules:e(M),"label-width":"100px"},{default:u(()=>[d(t,{label:"\u624B\u673A\u53F7",prop:"mobile"},{default:u(()=>[d(v,{modelValue:e(o).mobile,"onUpdate:modelValue":a[0]||(a[0]=l=>e(o).mobile=l),placeholder:"\u8BF7\u8F93\u5165\u624B\u673A\u53F7"},null,8,["modelValue"])]),_:1}),d(t,{label:"\u72B6\u6001",prop:"status"},{default:u(()=>[d(h,{modelValue:e(o).status,"onUpdate:modelValue":a[1]||(a[1]=l=>e(o).status=l)},{default:u(()=>[(p(!0),w(S,null,E(e(U)(e(x).COMMON_STATUS),l=>(p(),f(g,{key:l.value,value:l.value},{default:u(()=>[c(F(l.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),d(t,{label:"\u7528\u6237\u6635\u79F0",prop:"nickname"},{default:u(()=>[d(v,{modelValue:e(o).nickname,"onUpdate:modelValue":a[2]||(a[2]=l=>e(o).nickname=l),placeholder:"\u8BF7\u8F93\u5165\u7528\u6237\u6635\u79F0"},null,8,["modelValue"])]),_:1}),d(t,{label:"\u5934\u50CF",prop:"avatar"},{default:u(()=>[d(A,{modelValue:e(o).avatar,"onUpdate:modelValue":a[3]||(a[3]=l=>e(o).avatar=l),limit:1,"is-show-tip":!1},null,8,["modelValue"])]),_:1}),d(t,{label:"\u771F\u5B9E\u540D\u5B57",prop:"name"},{default:u(()=>[d(v,{modelValue:e(o).name,"onUpdate:modelValue":a[4]||(a[4]=l=>e(o).name=l),placeholder:"\u8BF7\u8F93\u5165\u771F\u5B9E\u540D\u5B57"},null,8,["modelValue"])]),_:1}),d(t,{label:"\u7528\u6237\u6027\u522B",prop:"sex"},{default:u(()=>[d(h,{modelValue:e(o).sex,"onUpdate:modelValue":a[5]||(a[5]=l=>e(o).sex=l)},{default:u(()=>[(p(!0),w(S,null,E(e(U)(e(x).SYSTEM_USER_SEX),l=>(p(),f(g,{key:l.value,value:l.value},{default:u(()=>[c(F(l.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),d(t,{label:"\u51FA\u751F\u65E5\u671F",prop:"birthday"},{default:u(()=>[d(N,{modelValue:e(o).birthday,"onUpdate:modelValue":a[6]||(a[6]=l=>e(o).birthday=l),type:"date","value-format":"x",placeholder:"\u9009\u62E9\u51FA\u751F\u65E5\u671F"},null,8,["modelValue"])]),_:1}),d(t,{label:"\u6240\u5728\u5730",prop:"areaId"},{default:u(()=>[d(j,{modelValue:e(o).areaId,"onUpdate:modelValue":a[7]||(a[7]=l=>e(o).areaId=l),data:e(y),props:e(J),"render-after-expand":!0},null,8,["modelValue","data","props"])]),_:1}),d(t,{label:"\u7528\u6237\u6807\u7B7E",prop:"tagIds"},{default:u(()=>[d(W,{modelValue:e(o).tagIds,"onUpdate:modelValue":a[8]||(a[8]=l=>e(o).tagIds=l),"show-add":""},null,8,["modelValue"])]),_:1}),d(t,{label:"\u7528\u6237\u5206\u7EC4",prop:"groupId"},{default:u(()=>[d(K,{modelValue:e(o).groupId,"onUpdate:modelValue":a[9]||(a[9]=l=>e(o).groupId=l)},null,8,["modelValue"])]),_:1}),d(t,{label:"\u4F1A\u5458\u5907\u6CE8",prop:"mark"},{default:u(()=>[d(v,{type:"textarea",modelValue:e(o).mark,"onUpdate:modelValue":a[10]||(a[10]=l=>e(o).mark=l),placeholder:"\u8BF7\u8F93\u5165\u4F1A\u5458\u5907\u6CE8"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[G,e(i)]])]),_:1},8,["title","modelValue"])}}});export{pe as _};
