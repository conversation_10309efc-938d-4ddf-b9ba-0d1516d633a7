import{p as n,_ as f,c as d}from"./index-BeQABqnP.js";import{f as u}from"./form-designer-DQFPUccF.js";import{k as m,c as k,L as I,y as s,m as a,t as y,u as _,z as b,C as c,E as g,F as x}from"./form-create-B86qX0W_.js";const C=d(m({name:"XTextButton",__name:"XTextButton",props:{modelValue:n.bool.def(!1),loading:n.bool.def(!1),preIcon:n.string.def(""),postIcon:n.string.def(""),title:n.string.def(""),type:n.oneOf(["","primary","success","warning","danger","info"]).def("primary"),circle:n.bool.def(!1),round:n.bool.def(!1),plain:n.bool.def(!1),onClick:{type:Function,default:null}},setup(o){const l=o,i=k(()=>{const r=["title","preIcon","postIcon","onClick"],t={...I(),...l};for(const e in t)r.indexOf(e)!==-1&&delete t[e];return t});return(r,t)=>{const e=f,p=u;return a(),s(p,y({link:""},_(i),{onClick:o.onClick}),{default:b(()=>[o.preIcon?(a(),s(e,{key:0,icon:o.preIcon,class:"mr-1px"},null,8,["icon"])):c("",!0),g(" "+x(o.title?o.title:"")+" ",1),o.postIcon?(a(),s(e,{key:1,icon:o.postIcon,class:"mr-1px"},null,8,["icon"])):c("",!0)]),_:1},16,["onClick"])}}}),[["__scopeId","data-v-7561ab3f"]]);export{C as _};
