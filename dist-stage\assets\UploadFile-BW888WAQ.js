import{d as I,c as b}from"./index-BeQABqnP.js";import{b as v,a as U,H as h,U as x}from"./upload-CRqxVqwM.js";import{U as l}from"./useUpload-CEoH0OEO.js";import{f as z,z as E}from"./form-designer-DQFPUccF.js";import{k as H,i as k,r as q,P as F,y as j,m as P,z as o,H as S,E as $,v as w,q as A,u as t}from"./form-create-B86qX0W_.js";const B={class:"el-upload__tip",style:{"margin-left":"5px"}},C=b(H({__name:"UploadFile",props:{type:{}},emits:["uploaded"],setup(p,{emit:d}){const e=I(),m=p,u=k("accountId"),r=q([]),n=d,s=F({type:l.Image,title:"",introduction:"",accountId:u}),c=m.type===l.Image?v:U,f=a=>{if(a.code!==0)return e.alertError("\u4E0A\u4F20\u51FA\u9519\uFF1A"+a.msg),!1;r.value=[],s.title="",s.introduction="",e.notifySuccess("\u4E0A\u4F20\u6210\u529F"),n("uploaded")},y=a=>e.error("\u4E0A\u4F20\u5931\u8D25: "+a.message);return(a,i)=>{const _=z,g=E;return P(),j(g,{action:t(x),headers:t(h),multiple:"",limit:1,"file-list":t(r),data:t(s),"on-error":y,"before-upload":t(c),"on-success":f},{tip:o(()=>[w("span",B,[A(a.$slots,"default",{},void 0,!0)])]),default:o(()=>[S(_,{type:"primary",plain:""},{default:o(()=>i[0]||(i[0]=[$(" \u70B9\u51FB\u4E0A\u4F20 ")])),_:1})]),_:3},8,["action","headers","file-list","data","before-upload"])}}}),[["__scopeId","data-v-c21a7348"]]);export{C as default};
