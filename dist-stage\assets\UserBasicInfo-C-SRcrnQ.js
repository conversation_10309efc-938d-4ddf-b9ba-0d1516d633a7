import{D as f,c as v}from"./index-BeQABqnP.js";import{_ as k}from"./DictTag.vue_vue_type_script_lang-B0tTMPJ6.js";import{f as o}from"./formatTime-CN67D7Gb.js";import"./Descriptions.vue_vue_type_style_index_0_scoped_74d4336e_lang-C9-wkVGw.js";import{D as r}from"./DescriptionsItemLabel-Bu5M3Lam.js";import{i as S,j as D,ah as E,am as g,an as I,ad as T}from"./form-designer-DQFPUccF.js";import{k as q,y as m,m as i,z as a,C as _,l as x,H as e,E as n,F as t,u,G as z,q as U}from"./form-create-B86qX0W_.js";import"./el-collapse-transition-l0sNRNKZ.js";const j=v(q({__name:"UserBasicInfo",props:{user:{},mode:{default:"member"}},setup:M=>(l,N)=>{const b=E,d=D,s=I,c=k,p=g,h=S,y=T;return i(),m(y,{shadow:"never"},{header:a(()=>[U(l.$slots,"header",{},void 0,!0)]),default:a(()=>[l.mode==="member"?(i(),m(h,{key:0},{default:a(()=>[e(d,{span:4},{default:a(()=>[e(b,{size:140,src:l.user.avatar||void 0,shape:"square"},null,8,["src"])]),_:1}),e(d,{span:20},{default:a(()=>[e(p,{column:2},{default:a(()=>[e(s,null,{label:a(()=>[e(u(r),{icon:"ep:user",label:"\u7528\u6237\u540D"})]),default:a(()=>[n(" "+t(l.user.name||"\u7A7A"),1)]),_:1}),e(s,null,{label:a(()=>[e(u(r),{icon:"ep:user",label:"\u6635\u79F0"})]),default:a(()=>[n(" "+t(l.user.nickname),1)]),_:1}),e(s,{label:"\u624B\u673A\u53F7"},{label:a(()=>[e(u(r),{icon:"ep:phone",label:"\u624B\u673A\u53F7"})]),default:a(()=>[n(" "+t(l.user.mobile),1)]),_:1}),e(s,null,{label:a(()=>[e(u(r),{icon:"fa:mars-double",label:"\u6027\u522B"})]),default:a(()=>[e(c,{type:u(f).SYSTEM_USER_SEX,value:l.user.sex},null,8,["type","value"])]),_:1}),e(s,null,{label:a(()=>[e(u(r),{icon:"ep:location",label:"\u6240\u5728\u5730"})]),default:a(()=>[n(" "+t(l.user.areaName),1)]),_:1}),e(s,null,{label:a(()=>[e(u(r),{icon:"ep:position",label:"\u6CE8\u518C IP"})]),default:a(()=>[n(" "+t(l.user.registerIp),1)]),_:1}),e(s,null,{label:a(()=>[e(u(r),{icon:"fa:birthday-cake",label:"\u751F\u65E5"})]),default:a(()=>[n(" "+t(l.user.birthday?u(o)(l.user.birthday):"\u7A7A"),1)]),_:1}),e(s,null,{label:a(()=>[e(u(r),{icon:"ep:calendar",label:"\u6CE8\u518C\u65F6\u95F4"})]),default:a(()=>[n(" "+t(l.user.createTime?u(o)(l.user.createTime):"\u7A7A"),1)]),_:1}),e(s,null,{label:a(()=>[e(u(r),{icon:"ep:calendar",label:"\u6700\u540E\u767B\u5F55\u65F6\u95F4"})]),default:a(()=>[n(" "+t(l.user.loginDate?u(o)(l.user.loginDate):"\u7A7A"),1)]),_:1})]),_:1})]),_:1})]),_:1})):_("",!0),l.mode==="kefu"?(i(),x(z,{key:1},[e(b,{size:140,src:l.user.avatar||void 0,shape:"square"},null,8,["src"]),e(p,{column:1,class:"kefu-descriptions"},{default:a(()=>[e(s,null,{label:a(()=>[e(u(r),{icon:"ep:user",label:"\u7528\u6237\u540D"})]),default:a(()=>[n(" "+t(l.user.name||"\u7A7A"),1)]),_:1}),e(s,null,{label:a(()=>[e(u(r),{icon:"ep:user",label:"\u6635\u79F0"})]),default:a(()=>[n(" "+t(l.user.nickname),1)]),_:1}),e(s,null,{label:a(()=>[e(u(r),{icon:"ep:phone",label:"\u624B\u673A\u53F7"})]),default:a(()=>[n(" "+t(l.user.mobile),1)]),_:1}),e(s,null,{label:a(()=>[e(u(r),{icon:"fa:mars-double",label:"\u6027\u522B"})]),default:a(()=>[e(c,{type:u(f).SYSTEM_USER_SEX,value:l.user.sex},null,8,["type","value"])]),_:1}),e(s,null,{label:a(()=>[e(u(r),{icon:"ep:location",label:"\u6240\u5728\u5730"})]),default:a(()=>[n(" "+t(l.user.areaName),1)]),_:1}),e(s,null,{label:a(()=>[e(u(r),{icon:"ep:position",label:"\u6CE8\u518C IP"})]),default:a(()=>[n(" "+t(l.user.registerIp),1)]),_:1}),e(s,null,{label:a(()=>[e(u(r),{icon:"fa:birthday-cake",label:"\u751F\u65E5"})]),default:a(()=>[n(" "+t(l.user.birthday?u(o)(l.user.birthday):"\u7A7A"),1)]),_:1}),e(s,null,{label:a(()=>[e(u(r),{icon:"ep:calendar",label:"\u6CE8\u518C\u65F6\u95F4"})]),default:a(()=>[n(" "+t(l.user.createTime?u(o)(l.user.createTime):"\u7A7A"),1)]),_:1}),e(s,null,{label:a(()=>[e(u(r),{icon:"ep:calendar",label:"\u6700\u540E\u767B\u5F55\u65F6\u95F4"})]),default:a(()=>[n(" "+t(l.user.loginDate?u(o)(l.user.loginDate):"\u7A7A"),1)]),_:1})]),_:1})],64)):_("",!0)]),_:3})}}),[["__scopeId","data-v-9b7ab18c"]]);export{j as default};
