import{a as W,d as X,au as Y,D as Z}from"./index-BeQABqnP.js";import{_ as ee}from"./Dialog.vue_vue_type_style_index_0_lang-DpoeWsKb.js";import{_ as le}from"./index-CZs2S1Cj.js";import{R as U}from"./index-Ufxn2eGP.js";import oe from"./RequestOrderDetailForm-STbjlxZ5.js";import{U as de}from"./index-BDU5cx5r.js";import{B as re}from"./index-CH8pxbb6.js";import{M as te}from"./index-D4qK--X-.js";import{O as ae}from"./index-B6tWTPuf.js";import{h as ue,aj as ie,k as pe,x as se,Q as me,F as ne,ak as ce,a0 as ve,$ as ye,f as be}from"./form-designer-DQFPUccF.js";import{k as fe,r as s,P as Ne,c as Ve,e as Ie,y as I,m as n,u as l,h as z,z as a,A as _e,H as t,l as w,G as Q,$ as h,C as ge,E as $}from"./form-create-B86qX0W_.js";const De=fe({name:"RequestOrderForm",__name:"RequestOrderForm",emits:["success"],setup(qe,{expose:F,emit:M}){const{t:g}=W(),S=X(),k=s(!1),b=s(!1),C=s(""),c=s(!1),x=s(""),e=s({id:void 0,requestNo:void 0,customerId:void 0,customerName:void 0,orderType:"sale_order",orderId:void 0,orderNo:void 0,orderDetailId:void 0,productId:void 0,productCode:void 0,productName:void 0,productSubType:void 0,productUnit:void 0,spec:void 0,status:void 0,approveStatus:void 0,approverId:void 0,approverName:void 0,bomId:void 0,bomCode:void 0,bomVersion:void 0,orderDate:void 0,orderQuantity:void 0,fulfilledQuantity:void 0,orderSpecQuantity:void 0,orderUnit:void 0,deliverDate:void 0,requirement:void 0,remark:void 0,readyStatus:void 0,readyQuantity:void 0,slotQuantity:1}),B=Ne({productName:[{required:!0,message:"\u8BF7\u9009\u62E9\u4EA7\u54C1",trigger:"blur"}]}),D=s(),_=s("requestOrderDetail"),q=s();F({open:async(u,r)=>{if(b.value=!0,C.value=g("action."+u),x.value=u,j(),r){c.value=!0;try{e.value=await U.getRequestOrder(r)}finally{c.value=!1}}}});const P=M,E=async()=>{if(await D.value.validate(),e.value.bomId)try{await q.value.validate()}catch{return void(_.value="requestOrderDetail")}c.value=!0;try{const u=e.value;e.value.bomId&&(u.requestOrderDetails=q.value.getData()),x.value==="create"?(await U.createRequestOrder(u),S.success(g("common.createSuccess"))):(await U.updateRequestOrder(u),S.success(g("common.updateSuccess"))),b.value=!1,P("success")}finally{c.value=!1}},j=()=>{var u;e.value={id:void 0,requestNo:void 0,customerId:void 0,customerName:void 0,orderType:"sale_order",orderId:void 0,orderNo:void 0,orderDetailId:void 0,productId:void 0,productCode:void 0,productName:void 0,productSubType:void 0,productUnit:void 0,spec:void 0,status:void 0,approveStatus:void 0,approverId:void 0,approverName:void 0,bomId:void 0,bomCode:void 0,bomVersion:void 0,orderDate:void 0,orderQuantity:void 0,fulfilledQuantity:void 0,orderSpecQuantity:void 0,orderUnit:void 0,deliverDate:void 0,requirement:void 0,remark:void 0,readyStatus:void 0,readyQuantity:void 0,slotQuantity:1},(u=D.value)==null||u.resetFields()},G=async u=>{const{pageNo:r,pageSize:p,query:i,...v}=u,y=await ae.getSimpleOrderDetailPage({pageNo:r,pageSize:p,orderNo:i,...v}),{list:f,total:N}=await y;return{list:f.map(V=>({...V,productName:V.productName||"\u65E0\u4EA7\u54C1\u540D\u79F0",customerName:V.customerName||"\u65E0\u5BA2\u6237\u540D\u79F0",orderNo:V.orderNo||"\u65E0\u8BA2\u5355\u7F16\u53F7"})),total:N}};Ve(()=>e.value.orderDetailId===void 0?null:{id:e.value.orderDetailId,label:[e.value.orderNo,e.value.productName,e.value.productCode].filter(Boolean).join(" - ")});const O=s([]),m=s([]),T=async u=>{if(!u)return void(m.value=[]);const r=await re.getSimpleBomPage({pageSize:20,pageNo:1,materialId:u});m.value=r.list,m.value.length>0&&!e.value.bomId&&(e.value.bomId=m.value[0].id,e.value.bomCode=m.value[0].code,e.value.bomVersion=m.value[0].versionNumber||"1.0",e.value.slotQuantity=m.value[0].perMadeQuanlity?m.value[0].perMadeQuanlity:m.value[0].quanlity)},A=async u=>{const{pageNo:r,pageSize:p,query:i,...v}=u,y=await te.getSimpleMaterialPage({pageNo:r,pageSize:p,name:i,...v}),{list:f,total:N}=await y;return{list:f,total:N}};return Ie(async()=>{await(async()=>{const u=await de.getUnitPage({pageSize:100,pageNo:1});O.value=u.list})()}),(u,r)=>{const p=pe,i=ie,v=me,y=se,f=le,N=ne,V=ue,H=ye,L=ve,R=be,J=ee,K=ce;return n(),I(J,{title:l(C),modelValue:l(b),"onUpdate:modelValue":r[20]||(r[20]=d=>z(b)?b.value=d:null),width:"80%",loading:l(c)},{footer:a(()=>[t(R,{onClick:E,type:"primary",disabled:l(c)},{default:a(()=>r[21]||(r[21]=[$("\u786E \u5B9A")])),_:1},8,["disabled"]),t(R,{onClick:r[19]||(r[19]=d=>b.value=!1)},{default:a(()=>r[22]||(r[22]=[$("\u53D6 \u6D88")])),_:1})]),default:a(()=>[_e((n(),I(V,{ref_key:"formRef",ref:D,model:l(e),rules:l(B),"label-width":"100px",inline:!0},{default:a(()=>[t(i,{label:"\u9700\u6C42\u5355\u53F7",prop:"requestNo"},{default:a(()=>[t(p,{modelValue:l(e).requestNo,"onUpdate:modelValue":r[0]||(r[0]=d=>l(e).requestNo=d),placeholder:"\u4FDD\u5B58\u65F6\u81EA\u52A8\u751F\u6210",class:"!w-240px",disabled:""},null,8,["modelValue"])]),_:1}),t(i,{label:"\u6765\u6E90\u7C7B\u578B",prop:"orderType"},{default:a(()=>[t(y,{modelValue:l(e).orderType,"onUpdate:modelValue":r[1]||(r[1]=d=>l(e).orderType=d),placeholder:"\u8BF7\u9009\u62E9\u6765\u6E90\u7C7B\u578B",class:"!w-240px"},{default:a(()=>[(n(!0),w(Q,null,h(l(Y)(l(Z).MFG_ORDER_SOURCE),d=>(n(),I(v,{key:d.value,label:d.label,value:d.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(i,{label:"\u9500\u552E\u8BA2\u5355",prop:"orderDetailId"},{default:a(()=>[t(f,{filterable:"",clearable:"",class:"!w-240px",modelValue:l(e).orderDetailId,"onUpdate:modelValue":r[2]||(r[2]=d=>l(e).orderDetailId=d),placeholder:"\u8BF7\u9009\u62E9\u9500\u552E\u8BA2\u5355","initial-load":!1,"load-method":G,"value-key":"id","label-key":d=>`${d.orderNo} - ${d.productName} - ${d.productCode}`,"default-value":{id:l(e).orderDetailId,orderNo:l(e).orderNo,productName:l(e).productName,productCode:l(e).productCode},"query-key":u.orderNo,"extra-params":{productionStatus:"not_planned"},onChange:r[3]||(r[3]=(d,o)=>{l(e).orderDetailId=d,l(e).orderId=o==null?void 0:o.orderId,l(e).orderNo=o==null?void 0:o.orderNo,l(e).productId=o==null?void 0:o.productId,l(e).productName=o==null?void 0:o.productName,l(e).productCode=o==null?void 0:o.productCode,l(e).productUnit=o==null?void 0:o.unit,l(e).spec=o==null?void 0:o.spec,l(e).productSubType=o==null?void 0:o.subType,l(e).customerId=o==null?void 0:o.customerId,l(e).customerName=o==null?void 0:o.customerName,l(e).orderUnit=o==null?void 0:o.unit,l(e).orderQuantity=o==null?void 0:o.quantity,l(e).orderDate=o==null?void 0:o.orderDate,l(e).deliverDate=o==null?void 0:o.deliverDate,l(e).orderSpecQuantity=o==null?void 0:o.specQuantityTotal,l(e).bomLabel=null,l(e).bomId=null,l(e).bomCode=null,k.value=!!d,T(o==null?void 0:o.productId)})},null,8,["modelValue","label-key","default-value","query-key"])]),_:1}),t(i,{label:"\u4EA7\u54C1\u540D\u79F0",prop:"productName"},{default:a(()=>[t(f,{filterable:"",clearable:"",class:"!w-240px",modelValue:l(e).productName,"onUpdate:modelValue":r[4]||(r[4]=d=>l(e).productName=d),placeholder:"\u8BF7\u9009\u62E9\u4EA7\u54C1\u540D\u79F0","initial-load":!1,"load-method":A,"label-key":d=>`${d.name} - ${d.fullCode} - ${d.spec}`,disabled:l(k)===!0,"value-key":"id","default-value":{value:l(e).productName,label:l(e).productName},"query-key":u.productName,"extra-params":{types:[2,3]},onChange:r[5]||(r[5]=(d,o)=>{l(e).productId=o.id,l(e).productName=o==null?void 0:o.name,l(e).productCode=o==null?void 0:o.fullCode,l(e).productSubType=o==null?void 0:o.subType,l(e).productUnit=Number(o==null?void 0:o.unit),l(e).spec=o==null?void 0:o.spec,l(e).orderUnit=o==null?void 0:o.mfgUnit,T(o==null?void 0:o.id)})},null,8,["modelValue","label-key","disabled","default-value","query-key"])]),_:1}),t(i,{label:"\u8BA2\u5355\u6570\u91CF",prop:"orderQuantity"},{default:a(()=>[t(p,{modelValue:l(e).orderQuantity,"onUpdate:modelValue":r[7]||(r[7]=d=>l(e).orderQuantity=d),placeholder:"\u8BF7\u8F93\u5165\u8BA2\u5355\u6570\u91CF",class:"!w-240px",disabled:""},{append:a(()=>[t(y,{modelValue:l(e).productUnit,"onUpdate:modelValue":r[6]||(r[6]=d=>l(e).productUnit=d),placeholder:"\u8BF7\u9009\u62E9\u8BA2\u5355\u5355\u4F4D",class:"!w-100px",disabled:""},{default:a(()=>[(n(!0),w(Q,null,h(l(O),d=>(n(),I(v,{key:d.id,label:d.name,value:d.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["modelValue"])]),_:1}),t(i,{label:"\u89C4\u683C",prop:"spec"},{default:a(()=>[t(p,{modelValue:l(e).spec,"onUpdate:modelValue":r[8]||(r[8]=d=>l(e).spec=d),placeholder:"\u8BF7\u8F93\u5165\u89C4\u683C",class:"!w-240px",disabled:""},null,8,["modelValue"])]),_:1}),t(i,{label:"\u5BA2\u6237",prop:"customerName"},{default:a(()=>[t(p,{modelValue:l(e).customerName,"onUpdate:modelValue":r[9]||(r[9]=d=>l(e).customerName=d),placeholder:"\u8BF7\u8F93\u5165\u5BA2\u6237\u540D\u79F0",class:"!w-240px",disabled:""},null,8,["modelValue"])]),_:1}),t(i,{label:"\u4E0B\u5355\u65F6\u95F4",prop:"orderDate"},{default:a(()=>[t(N,{modelValue:l(e).orderDate,"onUpdate:modelValue":r[10]||(r[10]=d=>l(e).orderDate=d),type:"date","value-format":"x",placeholder:"\u9009\u62E9\u4E0B\u5355\u65F6\u95F4",class:"!w-240px",disabled:""},null,8,["modelValue"])]),_:1}),t(i,{label:"\u4EA4\u671F",prop:"deliverDate"},{default:a(()=>[t(N,{modelValue:l(e).deliverDate,"onUpdate:modelValue":r[11]||(r[11]=d=>l(e).deliverDate=d),type:"date","value-format":"x",placeholder:"\u9009\u62E9\u4EA4\u671F",class:"!w-240px"},null,8,["modelValue"])]),_:1}),t(i,{label:"Bom\u7F16\u7801",prop:"bomCode"},{default:a(()=>[t(y,{modelValue:l(e).bomCode,"onUpdate:modelValue":r[12]||(r[12]=d=>l(e).bomCode=d),placeholder:"\u8BF7\u9009\u62E9Bom\u7F16\u7801",clearable:"",class:"!w-240px",onChange:r[13]||(r[13]=(d,o)=>{l(e).bomCode=o==null?void 0:o.code,l(e).bomVersion=(o==null?void 0:o.versionNumber)||"1.0",l(e).bomId=d,l(e).slotQuantity=o.perMadeQuanlity?o.perMadeQuanlity:o.quanlity})},{default:a(()=>[(n(!0),w(Q,null,h(l(m),d=>(n(),I(v,{key:d.id,label:d.name||d.materialName+" - "+d.code+" - "+(d.versionNumber||"1.0"),value:d.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(i,{label:"\u6BCF\u69FD\u6570\u91CF",prop:"slotQuantity"},{default:a(()=>[t(p,{modelValue:l(e).slotQuantity,"onUpdate:modelValue":r[14]||(r[14]=d=>l(e).slotQuantity=d),placeholder:"\u8BF7\u8F93\u5165\u6BCF\u69FD\u6570\u91CF",class:"!w-240px",type:"number"},null,8,["modelValue"])]),_:1}),t(i,{label:"\u8BA2\u5355\u89C4\u683C\u6570\u91CF",prop:"orderSpecQuantity"},{default:a(()=>[t(p,{modelValue:l(e).orderSpecQuantity,"onUpdate:modelValue":r[15]||(r[15]=d=>l(e).orderSpecQuantity=d),placeholder:"\u8BF7\u8F93\u5165\u8BA2\u5355\u89C4\u683C\u6570\u91CF",class:"!w-240px",disabled:""},null,8,["modelValue"])]),_:1}),t(i,{label:"\u751F\u4EA7\u8981\u6C42",prop:"requirement"},{default:a(()=>[t(p,{modelValue:l(e).requirement,"onUpdate:modelValue":r[16]||(r[16]=d=>l(e).requirement=d),placeholder:"\u8BF7\u8F93\u5165\u751F\u4EA7\u8981\u6C42",class:"!w-610px"},null,8,["modelValue"])]),_:1}),t(i,{label:"\u5907\u6CE8",prop:"remark"},{default:a(()=>[t(p,{modelValue:l(e).remark,"onUpdate:modelValue":r[17]||(r[17]=d=>l(e).remark=d),placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8",class:"!w-240px"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[K,l(c)]]),t(L,{modelValue:l(_),"onUpdate:modelValue":r[18]||(r[18]=d=>z(_)?_.value=d:null)},{default:a(()=>[l(e).bomId?(n(),I(H,{key:0,label:"\u751F\u4EA7\u8BA2\u5355\u660E\u7EC6",name:"requestOrderDetail"},{default:a(()=>[t(oe,{ref_key:"requestOrderDetailFormRef",ref:q,"biz-order-id":l(e).id,"bom-id":l(e).bomId},null,8,["biz-order-id","bom-id"])]),_:1})):ge("",!0)]),_:1},8,["modelValue"])]),_:1},8,["title","modelValue","loading"])}}});export{De as _};
