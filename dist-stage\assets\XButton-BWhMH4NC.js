import{p as o,_ as f,c as d}from"./index-BeQABqnP.js";import{f as u}from"./form-designer-DQFPUccF.js";import{k as m,c as k,L as I,y as s,m as c,z as _,C as r,E as b,F as g,t as y,u as C}from"./form-create-B86qX0W_.js";const x=d(m({name:"XButton",__name:"XButton",props:{modelValue:o.bool.def(!1),loading:o.bool.def(!1),preIcon:o.string.def(""),postIcon:o.string.def(""),title:o.string.def(""),type:o.oneOf(["","primary","success","warning","danger","info"]).def(""),link:o.bool.def(!1),circle:o.bool.def(!1),round:o.bool.def(!1),plain:o.bool.def(!1),onClick:{type:Function,default:null}},setup(n){const a=n,i=k(()=>{const l=["title","preIcon","postIcon","onClick"],t={...I(),...a};for(const e in t)l.indexOf(e)!==-1&&delete t[e];return t});return(l,t)=>{const e=f,p=u;return c(),s(p,y(C(i),{onClick:n.onClick}),{default:_(()=>[n.preIcon?(c(),s(e,{key:0,icon:n.preIcon,class:"mr-1px"},null,8,["icon"])):r("",!0),b(" "+g(n.title?n.title:"")+" ",1),n.postIcon?(c(),s(e,{key:1,icon:n.postIcon,class:"mr-1px"},null,8,["icon"])):r("",!0)]),_:1},16,["onClick"])}}}),[["__scopeId","data-v-9ff26ce2"]]);export{x as _};
