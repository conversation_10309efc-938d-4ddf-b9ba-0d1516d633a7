import{H as d}from"./index-BeQABqnP.js";import{_ as U}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CxohUsTD.js";import{_ as j}from"./index.vue_vue_type_script_setup_true_lang-BjqH9dXd.js";import{d as x}from"./formatTime-CN67D7Gb.js";import{g as S}from"./index-DrJkUdsU.js";import{Z as h,_ as k,ak as B}from"./form-designer-DQFPUccF.js";import{k as H,r as n,P as L,e as P,y as g,m as f,z as p,A as q,H as l,u as a,E as w,F as _}from"./form-create-B86qX0W_.js";const A=H({name:"UserBalanceList",__name:"UserBalanceList",props:{walletId:{type:Number,required:!1}},setup(b){const v=b,s=n(!0),m=n(0),t=L({pageNo:1,pageSize:10,walletId:null}),u=n([]),c=async()=>{s.value=!0;try{t.walletId=v.walletId;const i=await S(t);u.value=i.list,m.value=i.total}finally{s.value=!1}};return P(()=>{c()}),(i,o)=>{const r=k,y=h,z=j,I=U,N=B;return f(),g(I,null,{default:p(()=>[q((f(),g(y,{data:a(u),"show-overflow-tooltip":!0,stripe:!0},{default:p(()=>[l(r,{align:"center",label:"\u7F16\u53F7",prop:"id"}),l(r,{align:"center",label:"\u5173\u8054\u4E1A\u52A1\u6807\u9898",prop:"title"}),l(r,{align:"center",label:"\u4EA4\u6613\u91D1\u989D",prop:"price"},{default:p(({row:e})=>[w(_(a(d)(e.price))+" \u5143",1)]),_:1}),l(r,{align:"center",label:"\u94B1\u5305\u4F59\u989D",prop:"balance"},{default:p(({row:e})=>[w(_(a(d)(e.balance))+" \u5143",1)]),_:1}),l(r,{formatter:a(x),align:"center",label:"\u4EA4\u6613\u65F6\u95F4",prop:"createTime",width:"180px"},null,8,["formatter"])]),_:1},8,["data"])),[[N,a(s)]]),l(z,{limit:a(t).pageSize,"onUpdate:limit":o[0]||(o[0]=e=>a(t).pageSize=e),page:a(t).pageNo,"onUpdate:page":o[1]||(o[1]=e=>a(t).pageNo=e),total:a(m),onPagination:c},null,8,["limit","page","total"])]),_:1})}}});export{A as _};
