import{p as s}from"./index-BeQABqnP.js";import{_ as l}from"./Echart.vue_vue_type_script_setup_true_lang-DR6A3B70.js";import{C as d}from"./CardTitle-BZDs3cG8.js";import{ad as u}from"./form-designer-DQFPUccF.js";import{k as c,P as h,b as x,y,m as g,z as o,H as i,u as r}from"./form-create-B86qX0W_.js";const f=c({name:"MemberStatisticsCard",__name:"TimeSummaryChart",props:{title:s.string.def("").isRequired,value:s.object.isRequired},setup(n){const t=n,e=h({dataset:{dimensions:["time","price"],source:[]},grid:{left:20,right:20,bottom:20,top:80,containLabel:!0},legend:{top:50},series:[{name:"\u91D1\u989D",type:"line",smooth:!0,areaStyle:{}}],toolbox:{feature:{dataZoom:{yAxisIndex:!1},brush:{type:["lineX","clear"]},saveAsImage:{show:!0,name:t.title}}},tooltip:{trigger:"axis",axisPointer:{type:"cross"},padding:[5,10]},xAxis:{type:"category",boundaryGap:!1,axisTick:{show:!1}},yAxis:{axisTick:{show:!1}}});return x(()=>t.value,a=>{a&&e.dataset&&e.dataset.source&&(e.dataset.source=a)}),(a,b)=>{const m=l,p=u;return g(),y(p,{shadow:"never"},{header:o(()=>[i(r(d),{title:t.title},null,8,["title"])]),default:o(()=>[i(m,{height:300,options:r(e)},null,8,["options"])]),_:1})}}});export{f as _};
