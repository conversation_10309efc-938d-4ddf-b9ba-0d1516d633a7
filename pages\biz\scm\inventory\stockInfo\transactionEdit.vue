<template>
	<view class="transaction-edit-page">
		<!-- 表单内容 -->
		<scroll-view scroll-y class="form-content" v-if="!loading">
			<uv-form ref="formRef" :model="transactionForm" :rules="formValidationRules" labelPosition="top" labelWidth="120">
				<!-- 统一表单卡片 -->
				<view class="form-card">
					<view class="card-content">
						<!-- 基本信息 -->
						<uv-form-item label="交易编号">
							<uv-input
								v-model="transactionForm.bizId"
								placeholder="请输入交易编号"
								disabled
							/>
						</uv-form-item>

						<uv-form-item label="交易单号">
							<uv-input
								v-model="transactionForm.bizNo"
								placeholder="请输入交易单号"
								disabled
							/>
						</uv-form-item>

						<uv-form-item label="交易类型">
							<picker
								@change="onTransactionTypeChange"
								:value="transactionTypeIndex"
								:range="transactionTypeOptions || []"
								range-key="label"
							>
								<view class="picker-value">
									<text>{{ getTransactionTypeDisplayText() }}</text>
									<text class="picker-arrow">▼</text>
								</view>
							</picker>
						</uv-form-item>

						<uv-form-item label="交易方向">
							<picker
								@change="onTransactionDirectionChange"
								:value="transactionDirectionIndex"
								:range="transactionDirectionOptions || []"
								range-key="label"
							>
								<view class="picker-value">
									<text>{{ getTransactionDirectionDisplayText() }}</text>
									<text class="picker-arrow">▼</text>
								</view>
							</picker>
						</uv-form-item>

						<uv-form-item label="移动类型">
							<picker
								@change="onMoveTypeChange"
								:value="moveTypeIndex"
								:range="moveTypeOptions || []"
								range-key="label"
							>
								<view class="picker-value">
									<text>{{ getMoveTypeDisplayText() }}</text>
									<text class="picker-arrow">▼</text>
								</view>
							</picker>
						</uv-form-item>

						<uv-form-item label="移动日期">
							<uni-datetime-picker
								v-model="transactionForm.moveDate"
								type="date"
								placeholder="请选择移动日期"
								:clear-icon="false"
							/>
						</uv-form-item>

						<!-- 物料信息 -->
						<uv-form-item label="物料编码">
							<uv-input
								v-model="transactionForm.materialCode"
								placeholder="物料编码"
								disabled
							/>
						</uv-form-item>

						<uv-form-item label="物料规格">
							<uv-input
								v-model="transactionForm.materialSpec"
								placeholder="物料规格"
								disabled
							/>
						</uv-form-item>

						<!-- 仓库信息 -->
						<uv-form-item label="来源仓库">
							<uv-input
								v-model="transactionForm.fromWarehouseName"
								placeholder="来源仓库"
							/>
						</uv-form-item>

						<uv-form-item label="来源仓位">
							<uv-input
								v-model="transactionForm.fromLocationName"
								placeholder="来源仓位"
							/>
						</uv-form-item>

						<uv-form-item label="目标仓库">
							<uv-input
								v-model="transactionForm.toWarehouseName"
								placeholder="目标仓库"
							/>
						</uv-form-item>

						<uv-form-item label="目标仓位">
							<uv-input
								v-model="transactionForm.toLocationName"
								placeholder="目标仓位"
							/>
						</uv-form-item>

						<!-- 数量信息 -->
						<uv-form-item label="数量">
							<uv-input
								v-model="transactionForm.quantity"
								type="number"
								placeholder="请输入数量"
							/>
						</uv-form-item>

						<uv-form-item label="数量单位">
							<SelectPicker
								v-model="transactionForm.quantityUnit"
								:options="unitOptions || []"
								title="选择数量单位"
								label-field="name"
								value-field="id"
								placeholder="请选择数量单位"
							/>
						</uv-form-item>

						<uv-form-item label="基本单位数量">
							<uv-input
								v-model="transactionForm.auxiliaryQuantity"
								type="number"
								placeholder="请输入基本单位数量"
							/>
						</uv-form-item>

						<uv-form-item label="基本单位">
							<SelectPicker
								v-model="transactionForm.auxiliaryUnit"
								:options="unitOptions || []"
								title="选择基本单位"
								label-field="name"
								value-field="id"
								placeholder="请选择基本单位"
							/>
						</uv-form-item>

						<uv-form-item label="出入库前数量">
							<uv-input
								v-model="transactionForm.beforeQuantity"
								type="number"
								placeholder="请输入出入库前数量"
							/>
						</uv-form-item>

						<uv-form-item label="出入库后数量">
							<uv-input
								v-model="transactionForm.afterQuantity"
								type="number"
								placeholder="请输入出入库后数量"
							/>
						</uv-form-item>

						<!-- 其他信息 -->
						<uv-form-item label="来源单号">
							<uv-input
								v-model="transactionForm.sourceNo"
								placeholder="请输入来源单号"
							/>
						</uv-form-item>

						<uv-form-item label="库存批号">
							<uv-input
								v-model="transactionForm.inventoryBatchNo"
								placeholder="请输入库存批号"
							/>
						</uv-form-item>

						<uv-form-item label="备注">
							<uv-input
								v-model="transactionForm.remark"
								type="textarea"
								placeholder="请输入备注"
								maxlength="200"
								:autoHeight="true"
							/>
						</uv-form-item>
					</view>
				</view>


			</uv-form>
		</scroll-view>

		<!-- 底部操作按钮 -->
		<view class="bottom-actions">
			<button class="cancel-button" @click="handleCancel">取消</button>
			<button class="save-button" :disabled="saving" @click="handleSave">
				<text v-if="saving">保存中...</text>
				<text v-else>保存</text>
			</button>
		</view>

		<!-- 加载状态 -->
		<view v-if="loading" class="loading-container">
			<uni-load-more status="loading"></uni-load-more>
		</view>
	</view>
</template>

<script>
import { getBatchDictOptions, DICT_TYPE } from '@/utils/dict.js'
import { stockInfoApi } from '@/api/scm/inventory/stockInfo/index.js'

export default {
	data() {
		return {
			stockId: null,
			loading: false,
			saving: false,
			isUpdate: false,
			transactionForm: {
				id: undefined,
				bizId: undefined,
				bizNo: undefined,
				bizDetailId: undefined,
				transactionType: undefined,
				transactionDirection: undefined,
				materialId: undefined,
				materialCode: undefined,
				materialName: undefined,
				materialType: undefined,
				materialSpec: undefined,
				moveType: undefined,
				fromWarehouseId: undefined,
				fromWarehouseName: undefined,
				fromLocationId: undefined,
				fromLocationName: undefined,
				toWarehouseId: undefined,
				toWarehouseName: undefined,
				toLocationId: undefined,
				toLocationName: undefined,
				moveDate: undefined,
				sourceId: undefined,
				sourceNo: undefined,
				inventoryId: undefined,
				inventoryBatchNo: undefined,
				quantity: undefined,
				quantityUnit: undefined,
				auxiliaryQuantity: undefined,
				auxiliaryUnit: undefined,
				remark: undefined,
				beforeQuantity: undefined,
				afterQuantity: undefined,
				costObjectCode: undefined,
				costObjectName: undefined,
				accountingVoucherNumber: undefined,
				tenantName: undefined,
				tempId: undefined
			},
			// 字典数据
			transactionTypeOptions: [],
			transactionDirectionOptions: [],
			moveTypeOptions: [],
			unitOptions: [],
			dictData: {},
			// picker 索引
			transactionTypeIndex: -1,
			transactionDirectionIndex: -1,
			moveTypeIndex: -1,
			// 表单验证规则
			formValidationRules: {
				transactionType: {
					type: 'string',
					required: true,
					message: '请选择交易类型',
					trigger: ['blur', 'change']
				},
				transactionDirection: {
					type: 'string',
					required: true,
					message: '请选择交易方向',
					trigger: ['blur', 'change']
				},
				quantity: {
					type: 'number',
					required: true,
					message: '请输入数量',
					trigger: ['blur', 'change']
				}
			}
		}
	},
	async onLoad() {
		const eventChannel = this.getOpenerEventChannel()
		if(eventChannel){
			eventChannel.on('acceptDataFormOpener',(data) => {
				if(data) {
					this.stockId = data.stockId
					this.isUpdate = data.isUpdate || false

					if (data.transactionForm) {
						this.transactionForm = { ...this.transactionForm, ...data.transactionForm }
					}

					// 初始化数据
					this.initData()
				}
			})
		}
	},
	methods: {
		// 初始化数据
		async initData() {
			await this.getDictData()
		},

		// 获取字典数据
		async getDictData() {
			try {
				const dictTypes = [
					DICT_TYPE.SCM_BIZ_TYPE,
					DICT_TYPE.INVENTORY_TRANSACTION_DIRECTION,
					DICT_TYPE.INVENTORY_MOVE_TYPE
				]
				this.dictData = await getBatchDictOptions(dictTypes)

				// 设置选项数据
				this.transactionTypeOptions = this.dictData[DICT_TYPE.SCM_BIZ_TYPE] || []
				this.transactionDirectionOptions = this.dictData[DICT_TYPE.INVENTORY_TRANSACTION_DIRECTION] || []
				this.moveTypeOptions = this.dictData[DICT_TYPE.INVENTORY_MOVE_TYPE] || []

				// 加载单位数据
				await this.loadUnits()

				// 更新 picker 索引
				this.updatePickerIndexes()

			} catch (error) {
				// 字典数据加载失败不影响主要功能
				this.dictData = {}
				this.transactionTypeOptions = []
				this.transactionDirectionOptions = []
				this.moveTypeOptions = []
				this.unitOptions = []
			}
		},

		// 加载单位数据
		async loadUnits() {
			try {
				const response = await stockInfoApi.getUnitList({ pageNo: 1, pageSize: 100 })
				let units = []
				if (response && response.data && response.data.list) {
					units = response.data.list
				}

				this.unitOptions = units
			} catch (error) {
				this.unitOptions = []
			}
		},

		// picker 选择处理函数
		onTransactionTypeChange(e) {
			const index = e.detail.value
			this.transactionTypeIndex = index
			if (index >= 0 && index < this.transactionTypeOptions.length) {
				this.transactionForm.transactionType = this.transactionTypeOptions[index].value
			}
		},

		onTransactionDirectionChange(e) {
			const index = e.detail.value
			this.transactionDirectionIndex = index
			if (index >= 0 && index < this.transactionDirectionOptions.length) {
				this.transactionForm.transactionDirection = this.transactionDirectionOptions[index].value
			}
		},

		onMoveTypeChange(e) {
			const index = e.detail.value
			this.moveTypeIndex = index
			if (index >= 0 && index < this.moveTypeOptions.length) {
				this.transactionForm.moveType = this.moveTypeOptions[index].value
			}
		},

		// 获取显示文本的方法
		getTransactionTypeDisplayText() {
			if (this.transactionTypeIndex >= 0 && this.transactionTypeIndex < this.transactionTypeOptions.length) {
				return this.transactionTypeOptions[this.transactionTypeIndex].label
			}
			return '请选择交易类型'
		},

		getTransactionDirectionDisplayText() {
			if (this.transactionDirectionIndex >= 0 && this.transactionDirectionIndex < this.transactionDirectionOptions.length) {
				return this.transactionDirectionOptions[this.transactionDirectionIndex].label
			}
			return '请选择交易方向'
		},

		getMoveTypeDisplayText() {
			if (this.moveTypeIndex >= 0 && this.moveTypeIndex < this.moveTypeOptions.length) {
				return this.moveTypeOptions[this.moveTypeIndex].label
			}
			return '请选择移动类型'
		},

		// 更新 picker 索引
		updatePickerIndexes() {
			// 更新交易类型索引
			if (this.transactionForm.transactionType && this.transactionTypeOptions.length > 0) {
				this.transactionTypeIndex = this.transactionTypeOptions.findIndex(item =>
					item.value === this.transactionForm.transactionType
				)
			}

			// 更新交易方向索引
			if (this.transactionForm.transactionDirection && this.transactionDirectionOptions.length > 0) {
				this.transactionDirectionIndex = this.transactionDirectionOptions.findIndex(item =>
					item.value === this.transactionForm.transactionDirection
				)
			}

			// 更新移动类型索引
			if (this.transactionForm.moveType && this.moveTypeOptions.length > 0) {
				this.moveTypeIndex = this.moveTypeOptions.findIndex(item =>
					item.value === this.transactionForm.moveType
				)
			}
		},



		// 取消编辑
		handleCancel() {
			uni.showModal({
				title: '确认取消',
				content: '确定要取消编辑吗？未保存的修改将丢失。',
				success: (res) => {
					if (res.confirm) {
						uni.navigateBack()
					}
				}
			})
		},

		// 保存交易明细
		async handleSave() {
			try {
				// 表单验证
				await this.$refs.formRef.validate()

				this.saving = true

				// 设置库存ID
				this.transactionForm.inventoryId = this.stockId

				// 通过事件通道将数据传回上一页
				const eventChannel = this.getOpenerEventChannel()
				if (eventChannel) {
					eventChannel.emit('saveTransactionDetail', {
						transactionDetail: this.transactionForm,
						isUpdate: this.isUpdate
					})
				}

				uni.showToast({
					title: '保存成功',
					icon: 'success'
				})

				// 延迟返回上一页
				setTimeout(() => {
					uni.navigateBack()
				}, 1500)

			} catch (error) {
				console.error('保存失败:', error)
				if (error.errorFields) {
					// 表单验证失败
					uni.showToast({
						title: '请检查表单输入',
						icon: 'error'
					})
				} else {
					uni.showToast({
						title: '保存失败',
						icon: 'error'
					})
				}
			} finally {
				this.saving = false
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.transaction-edit-page {
	background-color: #f8f8f8;
	min-height: 100vh;
	padding: 24rpx;
	padding-bottom: 160rpx;
	box-sizing: border-box;
}

.form-content {
	flex: 1;
}

/* 表单卡片样式 */
.form-card {
	background-color: #ffffff;
	border-radius: 16rpx;
	margin-bottom: 24rpx;
	overflow: hidden;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.card-header {
	background-color: #f8f9fa;
	padding: 24rpx 32rpx;
	border-bottom: 1px solid #e9ecef;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.card-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #2c3e50;
}

.card-content {
	padding: 32rpx;
}

.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: white;
	padding: 32rpx 40rpx;
	border-top: 1px solid #eee;
	z-index: 100;
	display: flex;
	gap: 24rpx;

	.cancel-button {
		flex: 1;
		height: 88rpx;
		background-color: #6c757d;
		color: white;
		border: none;
		border-radius: 16rpx;
		font-size: 32rpx;
		font-weight: 600;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.save-button {
		flex: 1;
		height: 88rpx;
		background-color: #007aff;
		color: white;
		border: none;
		border-radius: 16rpx;
		font-size: 32rpx;
		font-weight: 600;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.save-button:disabled {
		background-color: #c0c4cc;
		color: #ffffff;
		cursor: not-allowed;
	}
}

.loading-container {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
}

/* picker 样式 */
.picker-value {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 24rpx 32rpx;
	background-color: #f8f9fa;
	border-radius: 12rpx;
	border: 1px solid #e9ecef;
	min-height: 88rpx;
	box-sizing: border-box;
}

.picker-value text:first-child {
	flex: 1;
	color: #333;
	font-size: 28rpx;
}

.picker-arrow {
	color: #999;
	font-size: 24rpx;
	margin-left: 16rpx;
}
</style>
