import{d as E,F as z,L as A}from"./index-BeQABqnP.js";import{_ as D}from"./SkuList.vue_vue_type_script_setup_true_lang-Cvk0F_9N.js";import{_ as F,al as U,f as j,Z as q}from"./form-designer-DQFPUccF.js";import{k as B,r as m,b as h,y as v,m as _,z as o,H as a,C as H,u as l,q as R,E as g,F as T}from"./form-create-B86qX0W_.js";const Z=B({name:"PromotionSpuAndSkuList",__name:"SpuAndSkuList",props:{spuList:{},ruleConfig:{},spuPropertyListP:{},deletable:{type:Boolean}},emits:["delete"],setup(b,{expose:x,emit:L}){const C=E(),k=b,n=m([]),w=m(),d=m([]),y=m([]);x({getSkuConfigs:e=>{w.value.validateSku();const r=[];return d.value.forEach(t=>{var u;(u=t.spuDetail.skus)==null||u.forEach(f=>{r.push(f[e])})}),r}});const S=L;return h(()=>k.spuList,e=>{e&&(n.value=e)},{deep:!0,immediate:!0}),h(()=>k.spuPropertyListP,e=>{e&&(d.value=e,setTimeout(()=>{y.value=e.map(r=>r.spuId+"")},200))},{deep:!0,immediate:!0}),(e,r)=>{const t=F,u=U,f=j,I=q;return _(),v(I,{data:l(n),"expand-row-keys":l(y),"row-key":"id"},{default:o(()=>[a(t,{type:"expand",width:"30"},{default:o(({row:i})=>{var c,s;return[a(l(D),{ref_key:"skuListRef",ref:w,"is-activity-component":!0,"prop-form-data":(c=l(d).find(p=>p.spuId===i.id))==null?void 0:c.spuDetail,"property-list":(s=l(d).find(p=>p.spuId===i.id))==null?void 0:s.propertyList,"rule-config":e.ruleConfig},{extension:o(()=>[R(e.$slots,"default")]),_:2},1032,["prop-form-data","property-list","rule-config"])]}),_:3}),a(t,{key:"id",align:"center",label:"\u5546\u54C1\u7F16\u53F7",prop:"id"}),a(t,{label:"\u5546\u54C1\u56FE","min-width":"80"},{default:o(({row:i})=>[a(u,{src:i.picUrl,class:"h-30px w-30px",onClick:c=>{return s=i.picUrl,void A({zIndex:99999999,urlList:[s]});var s}},null,8,["src","onClick"])]),_:1}),a(t,{"show-overflow-tooltip":!0,label:"\u5546\u54C1\u540D\u79F0","min-width":"300",prop:"name"}),a(t,{align:"center",label:"\u5546\u54C1\u552E\u4EF7","min-width":"90",prop:"price"},{default:o(({row:i})=>[g(T(l(z)(i.price)),1)]),_:1}),a(t,{align:"center",label:"\u9500\u91CF","min-width":"90",prop:"salesCount"}),a(t,{align:"center",label:"\u5E93\u5B58","min-width":"90",prop:"stock"}),l(n).length>1&&e.deletable?(_(),v(t,{key:0,align:"center",label:"\u64CD\u4F5C","min-width":"90"},{default:o(i=>[a(f,{link:"",type:"primary",onClick:c=>(async s=>{await C.confirm("\u662F\u5426\u5220\u9664\u5546\u54C1\u7F16\u53F7\u4E3A"+s+"\u7684\u6570\u636E\uFF1F");const p=n.value.findIndex(P=>P.id==s);n.value.splice(p,1),S("delete",s)})(i.row.id)},{default:o(()=>r[0]||(r[0]=[g(" \u5220\u9664")])),_:2},1032,["onClick"])]),_:1})):H("",!0)]),_:3},8,["data","expand-row-keys"])}}});export{Z as _};
