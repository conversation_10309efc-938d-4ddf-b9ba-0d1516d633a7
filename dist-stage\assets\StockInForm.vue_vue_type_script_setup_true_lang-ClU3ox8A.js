import{W as r,a as Z,d as B,X as J}from"./index-BeQABqnP.js";import{_ as K}from"./Dialog.vue_vue_type_style_index_0_lang-DpoeWsKb.js";import{_ as M}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CxohUsTD.js";import{_ as N}from"./StockInItemForm.vue_vue_type_script_setup_true_lang-Bn_Mgxfu.js";import{S as Y}from"./index-JbKsvMIp.js";import{h as ee,i as ae,j as le,aj as te,k as se,F as oe,x as ue,Q as de,ak as ie,a0 as re,$ as me,f as pe}from"./form-designer-DQFPUccF.js";import{k as ne,r as i,P as ce,c as fe,y as v,m as f,z as t,A as ve,H as l,u as a,l as ke,G as _e,$ as ye,h as F,C as be,E as j}from"./form-create-B86qX0W_.js";const k={getStockInPage:async u=>await r.get({url:"/erp/stock-in/page",params:u}),getStockIn:async u=>await r.get({url:"/erp/stock-in/get?id="+u}),createStockIn:async u=>await r.post({url:"/erp/stock-in/create",data:u}),updateStockIn:async u=>await r.put({url:"/erp/stock-in/update",data:u}),updateStockInStatus:async(u,_)=>await r.put({url:"/erp/stock-in/update-status",params:{id:u,status:_}}),deleteStockIn:async u=>await r.delete({url:"/erp/stock-in/delete",params:{ids:u.join(",")}}),exportStockIn:async u=>await r.download({url:"/erp/stock-in/export-excel",params:u})},we=ne({name:"StockInForm",__name:"StockInForm",emits:["success"],setup(u,{expose:_,emit:C}){const{t:y}=Z(),I=B(),m=i(!1),g=i(""),p=i(!1),b=i(""),o=i({id:void 0,supplierId:void 0,inTime:void 0,remark:void 0,fileUrl:"",items:[]}),q=ce({inTime:[{required:!0,message:"\u5165\u5E93\u65F6\u95F4\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),w=fe(()=>b.value==="detail"),V=i(),U=i([]),S=i("item"),x=i();_({open:async(d,e)=>{if(m.value=!0,g.value=y("action."+d),b.value=d,L(),e){p.value=!0;try{o.value=await k.getStockIn(e)}finally{p.value=!1}}U.value=await Y.getSupplierSimpleList()}});const z=C,A=async()=>{await V.value.validate(),await x.value.validate(),p.value=!0;try{const d=o.value;b.value==="create"?(await k.createStockIn(d),I.success(y("common.createSuccess"))):(await k.updateStockIn(d),I.success(y("common.updateSuccess"))),m.value=!1,z("success")}finally{p.value=!1}},L=()=>{var d;o.value={id:void 0,supplierId:void 0,inTime:void 0,remark:void 0,fileUrl:void 0,items:[]},(d=V.value)==null||d.resetFields()};return(d,e)=>{const h=se,n=te,c=le,P=oe,R=de,$=ue,D=J,E=ae,G=ee,H=me,O=re,Q=M,T=pe,W=K,X=ie;return f(),v(W,{title:a(g),modelValue:a(m),"onUpdate:modelValue":e[7]||(e[7]=s=>F(m)?m.value=s:null),width:"1080"},{footer:t(()=>[a(w)?be("",!0):(f(),v(T,{key:0,onClick:A,type:"primary",disabled:a(p)},{default:t(()=>e[8]||(e[8]=[j(" \u786E \u5B9A ")])),_:1},8,["disabled"])),l(T,{onClick:e[6]||(e[6]=s=>m.value=!1)},{default:t(()=>e[9]||(e[9]=[j("\u53D6 \u6D88")])),_:1})]),default:t(()=>[ve((f(),v(G,{ref_key:"formRef",ref:V,model:a(o),rules:a(q),"label-width":"100px",disabled:a(w)},{default:t(()=>[l(E,{gutter:20},{default:t(()=>[l(c,{span:8},{default:t(()=>[l(n,{label:"\u5165\u5E93\u5355\u53F7",prop:"no"},{default:t(()=>[l(h,{disabled:"",modelValue:a(o).no,"onUpdate:modelValue":e[0]||(e[0]=s=>a(o).no=s),placeholder:"\u4FDD\u5B58\u65F6\u81EA\u52A8\u751F\u6210"},null,8,["modelValue"])]),_:1})]),_:1}),l(c,{span:8},{default:t(()=>[l(n,{label:"\u5165\u5E93\u65F6\u95F4",prop:"inTime"},{default:t(()=>[l(P,{modelValue:a(o).inTime,"onUpdate:modelValue":e[1]||(e[1]=s=>a(o).inTime=s),type:"date","value-format":"x",placeholder:"\u9009\u62E9\u5165\u5E93\u65F6\u95F4",class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),l(c,{span:8},{default:t(()=>[l(n,{label:"\u4F9B\u5E94\u5546",prop:"supplierId"},{default:t(()=>[l($,{modelValue:a(o).supplierId,"onUpdate:modelValue":e[2]||(e[2]=s=>a(o).supplierId=s),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u4F9B\u5E94\u5546",class:"!w-1/1"},{default:t(()=>[(f(!0),ke(_e,null,ye(a(U),s=>(f(),v(R,{key:s.id,label:s.name,value:s.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(c,{span:16},{default:t(()=>[l(n,{label:"\u5907\u6CE8",prop:"remark"},{default:t(()=>[l(h,{type:"textarea",modelValue:a(o).remark,"onUpdate:modelValue":e[3]||(e[3]=s=>a(o).remark=s),rows:1,placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue"])]),_:1})]),_:1}),l(c,{span:8},{default:t(()=>[l(n,{label:"\u9644\u4EF6",prop:"fileUrl"},{default:t(()=>[l(D,{"is-show-tip":!1,modelValue:a(o).fileUrl,"onUpdate:modelValue":e[4]||(e[4]=s=>a(o).fileUrl=s),limit:1},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules","disabled"])),[[X,a(p)]]),l(Q,null,{default:t(()=>[l(O,{modelValue:a(S),"onUpdate:modelValue":e[5]||(e[5]=s=>F(S)?S.value=s:null),class:"-mt-15px -mb-10px"},{default:t(()=>[l(H,{label:"\u5165\u5E93\u4EA7\u54C1\u6E05\u5355",name:"item"},{default:t(()=>[l(N,{ref_key:"itemFormRef",ref:x,items:a(o).items,disabled:a(w)},null,8,["items","disabled"])]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["title","modelValue"])}}});export{k as S,we as _};
