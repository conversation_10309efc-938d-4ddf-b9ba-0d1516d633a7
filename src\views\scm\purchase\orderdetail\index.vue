<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="auto"
    >
      <el-form-item label="订单号" prop="orderNo">
        <el-input
          v-model="queryParams.orderNo"
          placeholder="请输入订单号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="物料名称" prop="materialName">
        <el-input
          v-model="queryParams.materialName"
          placeholder="请输入物料名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="供应商名称" prop="supplierName">
        <el-input
          v-model="queryParams.supplierName"
          placeholder="请输入供应商名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <!-- 可展开收起的表单项 -->
      <template v-if="isExpanded">

        <el-form-item label="批号" prop="batchNo">
          <el-input
            v-model="queryParams.batchNo"
            placeholder="请输入批号"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="订单日期" prop="orderDate">
          <el-date-picker
            v-model="queryParams.orderDate"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="需求来源类型" prop="sourceType">
          <el-select
            v-model="queryParams.sourceType"
            placeholder="请选择需求来源类型"
            clearable
            class="!w-240px"
          >
          <el-option
              v-for="dict in getStrDictOptions(DICT_TYPE.PURCHASE_REQ_SOURCE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="来源单号" prop="sourceNo">
          <el-input
            v-model="queryParams.sourceNo"
            placeholder="请输入来源单号"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="创建时间" prop="createTime">
          <el-date-picker
            v-model="queryParams.createTime"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-240px"
          />
        </el-form-item>
      </template>
      <!-- 操作按钮行 -->
      <el-form-item>
        <el-button @click="handleQuery" class="ml-4"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['purchase:order-detail:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['purchase:order-detail:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
        <el-button
          type="text"
          @click="toggleExpanded"
          class="ml-2"
        >
          {{ isExpanded ? '收起' : '展开' }}
          <Icon :icon="isExpanded ? 'ep:arrow-up' : 'ep:arrow-down'" class="ml-1" />
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true" show-summary :summary-method="summaryMethod">
      <el-table-column label="订单号" align="center" prop="orderNo" width="180" fixed="left">
        <template #default="scope">
          <div class="order-no-cell" v-if="scope.row.orderNo">
            <div class="order-no-content">
              <span>{{ scope.row.orderNo }}</span>
            </div>
            <el-button
              link
              type="info"
              @click="copyOrderNo(scope.row.orderNo)"
              class="copy-btn copy-btn-fixed"
              size="small"
            >
              <Icon icon="ep:copy-document" :size="12"/>
            </el-button>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column
        label="订单日期"
        align="center"
        prop="orderDate"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="供应商名称" align="center" prop="supplierName" width="100"/>
      <el-table-column label="物料名称" align="center" prop="materialName" />
      <el-table-column label="物料数量" align="center" prop="quantity" :formatter="quantityTableFormatter" width="130"/>
      <el-table-column label="单位" align="center" width="80px">
        <template #default="scope">
          {{ getUnitName(scope.row.unit) || scope.row.unit || '' }}
        </template>
      </el-table-column>
      <el-table-column label="物料单价" align="center" prop="unitPrice" :formatter="amountTableFormatter" width="120"/>
      <el-table-column label="含税单价" align="center" prop="unitTaxPrice" :formatter="amountTableFormatter" width="120"/>
      <el-table-column label="物料金额" align="center" prop="amount" :formatter="amountTableFormatter" width="120"/>
      <el-table-column label="税率" align="center" prop="tax" />
      <el-table-column label="税额" align="center" prop="taxAmount" :formatter="amountTableFormatter"/>
      <el-table-column label="价税合计" align="center" prop="totalAmount" :formatter="amountTableFormatter" width="120"/>
      <el-table-column label="商品跟进状态" align="center" prop="progressStatus" width="120">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.PURCHASE_ORDER_STATUS" :value="scope.row.progressStatus" />
        </template>
      </el-table-column>
      <el-table-column label="已接收数量" align="center" prop="receivedQuantity" width="100" :formatter="quantityTableFormatter"/>
      <el-table-column label="接收日期" align="center" prop="receivedDate" />
      <el-table-column label="批号" align="center" prop="batchNo" />
      <el-table-column label="需求来源类型" align="center" prop="sourceType" width="120">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.PURCHASE_REQ_SOURCE" :value="scope.row.sourceType" />
        </template>
      </el-table-column>
      <el-table-column label="来源单号" align="center" prop="sourceNo" width="120">
        <template #default="scope">
          <div class="order-no-cell" v-if="scope.row.sourceNo">
            <div class="order-no-content">
              <span>{{ scope.row.sourceNo }}</span>
            </div>
            <el-button
              link
              type="info"
              @click="copyOrderNo(scope.row.sourceNo)"
              class="copy-btn copy-btn-fixed"
              size="small"
            >
              <Icon icon="ep:copy-document" :size="12"/>
            </el-button>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />

      <el-table-column label="跟进人" align="center" prop="follower" />
      <el-table-column label="跟进日期" align="center" prop="followDate" />
      <el-table-column label="采购进度备注" align="center" prop="progressRemark" width="120"/>
      <el-table-column label="入库数量" align="center" prop="inStockQuantity" :formatter="quantityTableFormatter"/>
      <el-table-column label="辅助单位" align="center" width="100px">
        <template #default="scope">
          {{ getUnitName(scope.row.auxilaryUnit) || scope.row.auxilaryUnit || '' }}
        </template>
      </el-table-column>
      <el-table-column label="辅助单位数量" align="center" prop="auxilaryQuantity" width="120" :formatter="quantityTableFormatter"/>
      <el-table-column label="辅助入库数量" align="center" prop="inAuxilaryQuantity" width="120" :formatter="quantityTableFormatter"/>
      <el-table-column label="在途数量" align="center" prop="inTransitQuantity" :formatter="quantityTableFormatter"/>
      <el-table-column label="关联数量" align="center" prop="relateQuantity" :formatter="quantityTableFormatter"/>
      <el-table-column label="合同单号" align="center" prop="contractNo" width="120">
        <template #default="scope">
          <div class="order-no-cell" v-if="scope.row.contractNo">
            <div class="order-no-content">
              <span>{{ scope.row.contractNo }}</span>
            </div>
            <el-button
              link
              type="info"
              @click="copyOrderNo(scope.row.contractNo)"
              class="copy-btn copy-btn-fixed"
              size="small"
            >
              <Icon icon="ep:copy-document" :size="12"/>
            </el-button>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="开票数量" align="center" prop="invoiceQuantity" :formatter="quantityTableFormatter"/>
      <el-table-column label="辅助开票数量" align="center" prop="invoiceAuxilaryQuantity" width="120" :formatter="quantityTableFormatter"/>
      <el-table-column label="操作" align="center" min-width="120px" fixed="right">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['purchase:order-detail:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['purchase:order-detail:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <OrderDetailForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { OrderDetailApi, OrderDetailVO } from '@/api/scm/purchase/orderdetail'
import { getRemoteUnit } from '@/utils/commonBiz'
import OrderDetailForm from './OrderDetailForm.vue'
import { getStrDictOptions,DICT_TYPE } from '@/utils/dict'
import { amountTableFormatter, quantityTableFormatter, formatAmount, formatQuantity } from '@/utils/formatter'
import DictTag from '@/components/DictTag/src/DictTag.vue'
import { useClipboard } from '@vueuse/core'

/** 采购订单产品 列表 */
defineOptions({ name: 'OrderDetail' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const { copy } = useClipboard() // 复制功能

/** 复制订单单号 */
const copyOrderNo = async (orderNo: string) => {
  try {
    await copy(orderNo)
    message.success('单号复制成功')
  } catch (error) {
    message.error('复制失败')
  }
}

const loading = ref(true) // 列表的加载中
const list = ref<OrderDetailVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const unitMap = ref<Map<number, string>>(new Map()) // 单位ID到名称的映射
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  orderNo: undefined,
  orderDate: [],
  supplierName: undefined,
  materialName: undefined,
  batchNo: undefined,
  sourceType: undefined,
  sourceNo: undefined,
  createTime: [],
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

// 展开收起相关状态
const isExpanded = ref(true) // 默认展开状态

/** 切换展开收起状态 */
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
}

/** 加载单位数据 */
const loadUnits = async () => {
  try {
    // 批量获取所有单位信息
    const units = await getRemoteUnit()

    if (!units || units.length === 0) {
      return
    }

    // 建立单位映射
    units.forEach((unit: any) => {
      if (unit && unit.id && unit.name) {
        unitMap.value.set(unit.id, unit.name)
      }
    })
  } catch (error) {
    console.error('加载单位数据失败:', error)
  }
}

/** 获取单位名称 */
const getUnitName = (unitId: number | string) => {
  if (!unitId) return ''
  const id = typeof unitId === 'string' ? parseInt(unitId) : unitId
  const unitName = unitMap.value.get(id)
  return unitName || unitId.toString()
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await OrderDetailApi.getOrderDetailPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await OrderDetailApi.deleteOrderDetail(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 表格汇总方法 */
const summaryMethod = ({ columns, data }: { columns: any[], data: any[] }) => {
  const sums: any[] = []

  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }

    // 需要汇总的数量字段
    const quantityFields = [
      'quantity', 'receivedQuantity', 'inStockQuantity', 'auxilaryQuantity',
      'inAuxilaryQuantity', 'inTransitQuantity', 'relateQuantity',
      'invoiceQuantity', 'invoiceAuxilaryQuantity'
    ]

    // 需要汇总的金额字段
    const amountFields = [
      'unitPrice', 'unitTaxPrice', 'amount', 'taxAmount', 'totalAmount'
    ]

    if (quantityFields.includes(column.property)) {
      // 数量字段汇总
      const values = data.map(item => Number(item[column.property]) || 0)
      const total = values.reduce((prev, curr) => prev + curr, 0)
      sums[index] = total > 0 ? formatQuantity(total) : '0'
    } else if (amountFields.includes(column.property)) {
      // 金额字段汇总
      const values = data.map(item => Number(item[column.property]) || 0)
      const total = values.reduce((prev, curr) => prev + curr, 0)
      sums[index] = total > 0 ? formatAmount(total) : '¥0.00'
    } else {
      // 其他字段不显示汇总
      sums[index] = ''
    }
  })

  return sums
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await OrderDetailApi.exportOrderDetail(queryParams)
    download.excel(data, '采购订单产品.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(async () => {
  // 先加载单位数据
  await loadUnits()
  // 再加载列表数据
  getList()
})
</script>

<style scoped>
/* 展开收起动画效果 */
.el-form-item {
  transition: all 0.3s ease;
}

/* 订单号行布局 */
.order-no-cell {
  display: flex;
  align-items: center;
  width: 100%;
  min-height: 20px;
  gap: 5px; /* 文字和图标之间保持一点点间隔 */
}

.order-no-content {
  flex: 0 1 auto; /* 改为自适应宽度，不占满剩余空间 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: calc(100% - 16px); /* 为图标预留空间 */
}

/* 复制按钮样式 */
.copy-btn {
  padding: 2px !important;
  height: 20px !important;
  min-height: 20px !important;
  width: 20px !important;
  font-size: 12px;
  opacity: 0.6;
  transition: opacity 0.2s;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}



.copy-btn:hover {
  opacity: 1;
}
</style>
