<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <!-- 始终显示的搜索项 -->
      <el-form-item label="订单单号" prop="orderNo">
        <el-input
          v-model="queryParams.orderNo"
          placeholder="请输入订单单号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="客户名称" prop="customerName">
        <el-input
          v-model="queryParams.customerName"
          placeholder="请输入客户名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="审批状态" prop="approveStatus">
        <el-select
          v-model="queryParams.approveStatus"
          placeholder="请选择审批状态"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.APPROVE_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <!-- 高级搜索项 - 可展开收起 -->
      <template v-if="isExpanded">
        <el-form-item label="联系人" prop="customerContact">
          <el-input
            v-model="queryParams.customerContact"
            placeholder="请输入联系人姓名"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="联系电话" prop="customerPhone">
          <el-input
            v-model="queryParams.customerPhone"
            placeholder="请输入联系电话"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="收货地址" prop="customerAddress">
          <el-input
            v-model="queryParams.customerAddress"
            placeholder="请输入收货地址"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="发货日期" prop="deliveryDate">
          <el-date-picker
            v-model="queryParams.deliveryDate"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="送达日期" prop="expectedArrivalDate">
          <el-date-picker
            v-model="queryParams.expectedArrivalDate"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="运输方式" prop="transportMethod">
          <el-input
            v-model="queryParams.transportMethod"
            placeholder="请输入运输方式"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="物流公司" prop="logisticsCompany">
          <el-input
            v-model="queryParams.logisticsCompany"
            placeholder="请输入物流公司"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="物流单号" prop="logisticsTrackingNumber">
          <el-input
            v-model="queryParams.logisticsTrackingNumber"
            placeholder="请输入物流单号"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
      </template>

      <!-- 操作按钮行 -->
      <el-form-item>
        <el-button @click="handleQuery" class="ml-4"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['sale:delivery-notice:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['sale:delivery-notice:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
        <el-button
          type="primary"
          plain
          @click="handleApproval"
          :loading="exportLoading"
          v-hasPermi="['sale:delivery-notice:approve']"
        >
          <Icon icon="ep:check" class="mr-5px" /> 审核
        </el-button>
        <el-button
          type="text"
          @click="toggleExpanded"
          class="ml-2"
        >
          {{ isExpanded ? '收起' : '展开' }}
          <Icon :icon="isExpanded ? 'ep:arrow-up' : 'ep:arrow-down'" class="ml-1" />
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table
      v-loading="loading"
      :data="list"
      :stripe="true"
      border
      :show-overflow-tooltip="true"
      highlight-current-row
      @current-change="handleCurrentChange"
      @selection-change="handleSelectionChange"
      show-summary
      :summary-method="getSummaries"
      :span-method="spanMethod"
    >
      <el-table-column type="selection" width="60" fixed="left" />
      <el-table-column label="订单单号" align="left" prop="orderNo" width="180px" fixed="left">
        <template #default="scope">
          <div class="order-no-container vertical">
            <div class="order-no-cell">
              <div class="order-no-content">
                <el-button
                  link
                  type="primary"
                  @click="handleDetail(scope.row._originalId || scope.row.id)"
                  class="order-no-link"
                >
                  {{ scope.row.orderNo }}
                </el-button>
              </div>
              <el-button
                link
                type="info"
                @click="copyOrderNo(scope.row.orderNo)"
                class="copy-btn copy-btn-fixed"
                size="small"
              >
                <Icon icon="ep:copy-document" :size="12"/>
              </el-button>
            </div>
            <dict-tag
              v-if="scope.row.approveStatus"
              :type="DICT_TYPE.APPROVE_STATUS"
              :value="scope.row.approveStatus"
              class="approval-tag"
            />
          </div>
        </template>
      </el-table-column>
      <el-table-column label="客户名称" align="left" prop="customerName" width="150px" fixed="left"/>
      <!-- 明细数据列 -->
      <el-table-column label="物料编码" align="left" prop="materialCode" width="120px"/>
      <el-table-column label="物料名称" align="left" prop="materialName" width="150px"/>
      <el-table-column label="规格" align="center" prop="materialSpec" width="100px"/>
      <el-table-column label="数量" align="center" prop="materialQuantity" width="110px" :formatter="quantityTableFormatter">
        <template #default="scope">
          <el-tag type="success" v-if="scope.row.materialQuantity">
            {{ quantityTableFormatter(null, null, scope.row.materialQuantity, null) }}
            <span v-if="getUnitName(scope.row.materialUnit)" style="margin-left: 4px;">
              {{ getUnitName(scope.row.materialUnit) }}
            </span>
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="单价" align="center" prop="materialUnitPrice" width="110px" :formatter="amountTableFormatter">
        <template #default="scope">
          <span v-if="scope.row.materialUnitPrice">
            {{ amountTableFormatter(null, null, scope.row.materialUnitPrice, null) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="金额" align="center" prop="totalAmount" width="120px" :formatter="amountTableFormatter">
        <template #default="scope">
          <el-tag type="warning" v-if="scope.row.totalAmount">
            {{ amountTableFormatter(null, null, scope.row.totalAmount, null) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="发货日期" align="center" prop="deliveryDate" width="100px">
        <template #default="scope">
          <span v-if="scope.row.deliveryDate">
            {{ formatDate(scope.row.deliveryDate) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="left" prop="remark" width="120px"/>
      <el-table-column label="批号" align="left" prop="batchNo" />
      <!-- 原有的主表信息列 -->
      <el-table-column label="联系人" align="left" prop="customerContact" />
      <el-table-column label="联系电话" align="left" prop="customerPhone" width="100px"/>
      <el-table-column label="收货地址" align="left" prop="customerAddress" width="100px"/>
      <el-table-column label="送达日期" align="center" prop="expectedArrivalDate" width="100px" :formatter="dateFormatter2"/>
      <el-table-column label="运输方式" align="center" prop="transportMethod" width="100px"/>
      <el-table-column label="物流公司" align="left" prop="logisticsCompany" width="100px"/>
      <el-table-column label="物流单号" align="left" prop="logisticsTrackingNumber" width="120px">
        <template #default="scope">
          <div class="order-no-cell" v-if="scope.row.logisticsTrackingNumber">
            <div class="order-no-content">
              <span>{{ scope.row.logisticsTrackingNumber }}</span>
            </div>
            <el-button
              link
              type="info"
              @click="copyOrderNo(scope.row.logisticsTrackingNumber)"
              class="copy-btn copy-btn-fixed"
              size="small"
            >
              <Icon icon="ep:copy-document" :size="12"/>
            </el-button>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="来源类型" align="center" width="120px">
        <template #default>
          <el-tag type="warning" size="small">销售订单</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="来源单" align="left" width="140px">
        <template #default="scope">
          <div class="order-no-cell" v-if="scope.row.saleOrderNo">
            <div class="order-no-content">
              <span>{{ scope.row.saleOrderNo }}</span>
            </div>
            <el-button
              link
              type="info"
              @click="copyOrderNo(scope.row.saleOrderNo)"
              class="copy-btn copy-btn-fixed"
              size="small"
            >
              <Icon icon="ep:copy-document" :size="12"/>
            </el-button>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="说明事项" align="left" prop="remarks" width="100px"/>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" fixed="right"  min-width="120">
        <template #default="scope">
          <div v-if="scope.row._showActions">
            <el-button
              link
              type="primary"
              @click="openForm('update', scope.row._originalId || scope.row.id)"
              v-hasPermi="['sale:delivery-notice:update']"
              v-if="scope.row.approveStatus != '3'"
            >
              编辑
            </el-button>
            <el-button
              link
              type="danger"
              @click="handleDelete(scope.row._originalId || scope.row.id)"
              v-hasPermi="['sale:delivery-notice:delete']"
              v-if="scope.row.approveStatus != '3'"
            >
              删除
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <DeliveryNoticeForm ref="formRef" @success="getList" />
  <!-- 审批信息弹窗 -->
  <ApproveInfoForm ref="approveInfoFormRef" @success="getList" />
  <!-- 子表的列表 -->
  <!-- <ContentWrap>
    <el-tabs model-value="deliveryNoticeDetail">
      <el-tab-pane label="发货通知单明细" name="deliveryNoticeDetail">
        <DeliveryNoticeDetailList :notice-id="currentRow.id" />
      </el-tab-pane>
    </el-tabs>
  </ContentWrap> -->
</template>

<script setup lang="ts">
import { dateFormatter, dateFormatter2 } from '@/utils/formatTime'
import download from '@/utils/download'
import { DeliveryNoticeApi, DeliveryNoticeVO } from '@/api/scm/sale/deliverynotice'
import DeliveryNoticeForm from './DeliveryNoticeForm.vue'
import ApproveInfoForm from '../../base/approveinfo/ApproveInfoForm.vue'
import { amountTableFormatter, quantityTableFormatter, formatQuantity, formatAmount } from '@/utils/formatter'
import { DICT_TYPE, getStrDictOptions } from '@/utils/dict'
import { useRouter } from 'vue-router'
import { getRemoteUnit } from '@/utils/commonBiz'
import { useClipboard } from '@vueuse/core'
// import DeliveryNoticeDetailList from './components/DeliveryNoticeDetailList.vue'

/** 发货通知单 列表 */
defineOptions({ name: 'DeliveryNotice' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const router = useRouter() // 路由
const { copy } = useClipboard() // 复制功能

/** 复制订单单号 */
const copyOrderNo = async (orderNo: string) => {
  try {
    await copy(orderNo)
    message.success('单号复制成功')
  } catch (error) {
    message.error('复制失败')
  }
}

const loading = ref(true) // 列表的加载中
const list = ref<DeliveryNoticeVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const isExpanded = ref(false) // 搜索表单展开状态
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  orderId: undefined,
  orderNo: undefined,
  customerName: undefined,
  customerContact: undefined,
  customerPhone: undefined,
  customerAddress: undefined,
  deliveryDate: [],
  expectedArrivalDate: [],
  transportMethod: undefined,
  logisticsCompany: undefined,
  logisticsTrackingNumber: undefined,
  remarks: undefined,
  createTime: [],
  kdId: undefined,
  customerId: undefined,
  approveNo: undefined,
  approveStatus: undefined,
  approveDate: [],
  approverId: undefined,
  approverName: undefined,
  detail:true
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const selectedRows = ref<DeliveryNoticeVO[]>([]) // 选中的行
const approveInfoFormRef = ref() // 审批信息表单 Ref

// 单位相关数据
const unitList = ref<any[]>([]) // 单位列表
const unitMap = ref(new Map()) // 单位ID到名称的映射

/** 格式化日期 */
const formatDate = (timestamp: number) => {
  if (!timestamp) return ''
  return new Date(timestamp).toLocaleDateString('zh-CN')
}

/** 获取单位数据 */
const getUnitList = async () => {
  try {
    const data = await getRemoteUnit()
    unitList.value = data
    // 构建单位ID到名称的映射
    unitMap.value.clear()
    data.forEach((unit: any) => {
      unitMap.value.set(unit.id, unit.name)
    })
  } catch (error) {
    console.error('获取单位列表失败:', error)
  }
}

/** 获取单位名称 */
const getUnitName = (unitId: number | string) => {
  if (!unitId && unitId !== 0) return ''

  // 确保unitId是数字类型
  const id = typeof unitId === 'string' ? parseInt(unitId) : unitId

  if (isNaN(id)) {
    return ''
  }

  const unitName = unitMap.value.get(id)
  return unitName || ''
}

/** 表格汇总方法 */
const getSummaries = (param: any) => {
  const { columns, data } = param
  const sums: string[] = []

  columns.forEach((column: any, index: number) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }

    // 根据列属性进行汇总，而不是依赖索引
    if (column.property === 'materialQuantity') {
      const values = data.map((item: any) => Number(item.materialQuantity) || 0)
      if (!values.every((value: number) => Number.isNaN(value))) {
        const total = values.reduce((prev: number, curr: number) => {
          const value = Number(curr)
          if (!Number.isNaN(value)) {
            return prev + value
          } else {
            return prev
          }
        }, 0)

        // 获取第一个有效的单位作为汇总单位（假设同一发货通知单中的物料单位相同）
        const firstValidUnit = data.find((item: any) => item.materialUnit)?.materialUnit
        const unitName = firstValidUnit ? getUnitName(firstValidUnit) : ''

        sums[index] = `${formatQuantity(total)}${unitName ? ' ' + unitName : ''}`
      } else {
        sums[index] = ''
      }
    }
    // 对金额列进行汇总
    else if (column.property === 'totalAmount') {
      const values = data.map((item: any) => Number(item.totalAmount) || 0)
      if (!values.every((value: number) => Number.isNaN(value))) {
        const total = values.reduce((prev: number, curr: number) => {
          const value = Number(curr)
          if (!Number.isNaN(value)) {
            return prev + value
          } else {
            return prev
          }
        }, 0)
        sums[index] = `${formatAmount(total)}`
      } else {
        sums[index] = ''
      }
    }
    // 对单价列进行汇总
    else if (column.property === 'materialUnitPrice') {
      const values = data.filter((item: any) => Number(item.materialUnitPrice) > 0)
        .map((item: any) => Number(item.materialUnitPrice))
      if (values.length > 0) {
        const average = values.reduce((prev: number, curr: number) => prev + curr, 0)
        sums[index] = ` ${formatAmount(average)}`
      } else {
        sums[index] = ''
      }
    }
    // 对订单单号列显示统计信息
    else if (column.property === 'orderNo' && index === 1) {
      // 统计不重复的订单数量
      const uniqueOrders = new Set(data.map((item: any) => item._originalId || item.id))
      sums[index] = `共 ${uniqueOrders.size} 单`
    }
    // 对客户名称列显示统计信息
    else if (column.property === 'customerName' && index === 2) {
      // 统计不重复的客户数量
      const uniqueCustomers = new Set(data.map((item: any) => item.customerName).filter(Boolean))
      sums[index] = `${uniqueCustomers.size} 客户`
    }
    else {
      // 其他列不显示汇总信息
      sums[index] = ''
    }
  })

  return sums
}

/** 表格行合并方法 */
const spanMethod = ({ row, columnIndex }: any) => {

  // 需要合并的主表列
  const mainTableColumns = [
    0,  // 选择框
    1,  // 订单单号
    2,  // 客户名称
    12, // 联系人
    13, // 联系电话
    14, // 收货地址
    15, // 送达日期
    16, // 运输方式
    17, // 物流公司
    18, // 物流单号
    19, // 来源类型
    20, // 来源单号
    21, // 说明事项
    22, // 创建时间
    23  // 操作列
  ]

  // 明细列（不需要合并）
  const detailColumns = [
    3,  // 物料编码
    4,  // 物料名称
    5,  // 规格
    6,  // 数量
    7,  // 单价
    8,  // 金额
    9,  // 发货日期
    10, // 备注
    11  // 批号
  ]

  // 合并主表相关列
  if (mainTableColumns.includes(columnIndex)) {
    if (row._detailIndex === 0 || row._detailIndex === undefined) {
      // 第一行显示，合并行数为该主表记录的明细数量
      return {
        rowspan: row._rowSpan || 1,
        colspan: 1
      }
    } else {
      // 其他行隐藏
      return {
        rowspan: 0,
        colspan: 0
      }
    }
  }

  // 明细列不合并，正常显示
  if (detailColumns.includes(columnIndex)) {
    return {
      rowspan: 1,
      colspan: 1
    }
  }

  // 默认不合并
  return {
    rowspan: 1,
    colspan: 1
  }
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await DeliveryNoticeApi.getDeliveryNoticePage(queryParams)

    // 将明细数据合并到主表中显示
    const mergedList: any[] = []

    if (data.list && data.list.length > 0) {
      data.list.forEach((mainItem: any) => {
        if (mainItem.details && mainItem.details.length > 0) {
          // 如果有明细数据，为每个明细创建一行，合并主表信息
          mainItem.details.forEach((detail: any, index: number) => {
            mergedList.push({
              // 主表数据
              ...mainItem,
              // 明细数据
              ...detail,
              // 确保ID唯一性
              _originalId: mainItem.id,
              _detailIndex: index,
              id: `${mainItem.id}_${detail.id || index}`,
              // 只在第一行显示主表的操作按钮
              _showActions: index === 0,
              _rowSpan: index === 0 ? mainItem.details.length : 0
            })
          })
        } else {
          // 如果没有明细数据，直接显示主表数据
          mergedList.push({
            ...mainItem,
            _originalId: mainItem.id,
            _showActions: true,
            _rowSpan: 1
          })
        }
      })
    }

    list.value = mergedList
    total.value = data.total

    console.log('合并后的数据:', mergedList)
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 切换展开/收起状态 */
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 跳转到详情页面 */
const handleDetail = (id: number) => {
  router.push(`/scm/sale/deliverynotice/detail/${id}`)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await DeliveryNoticeApi.deleteDeliveryNotice(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await DeliveryNoticeApi.exportDeliveryNotice(queryParams)
    download.excel(data, '发货通知单.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 审核按钮操作 */
const handleApproval = async () => {
  if(!selectedRows.value || selectedRows.value.length === 0) {
    message.error('请选择要审核的发货通知单！')
    return
  }

  // 如果选中了多个通知单，提示用户
  if(selectedRows.value.length > 1) {
    message.warning('当前只支持单个发货通知单审核，请选择一个通知单进行审核')
    return
  }

  // 设置当前行为选中的第一个通知单
  const selectedNotice = selectedRows.value[0]
  currentRow.value = selectedNotice

  // 发起审批，传入业务数据
  approveInfoFormRef.value.open('approve', undefined, {
    bizId: (selectedNotice as any)._originalId || selectedNotice.id,
    bizNo: selectedNotice.orderNo,
    bizType: 'delivery_notice'
  })
}

/** 选中行操作 */
const currentRow = ref<DeliveryNoticeVO>({} as DeliveryNoticeVO) // 选中行
const handleCurrentChange = (row: DeliveryNoticeVO) => {
  currentRow.value = row
}

/** 选择变化处理 */
const handleSelectionChange = (selection: DeliveryNoticeVO[]) => {
  selectedRows.value = selection
}

/** 初始化 **/
onMounted(() => {
  getList()
  getUnitList()
})
</script>

<style scoped>
/* 订单单号容器样式 */
.order-no-container {
  display: flex;
  align-items: center;
  gap: 6px;
  flex-wrap: wrap;
  padding: 4px 0;
}

/* 垂直布局的订单单号容器 */
.order-no-container.vertical {
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
}

.order-no-link {
  font-weight: 500;
  padding: 0;
  height: auto;
  line-height: 1.4;
  flex-shrink: 0;
}

.order-no-link:hover {
  text-decoration: underline;
}

/* 订单号行布局 */
.order-no-cell {
  display: flex;
  align-items: center;
  width: 100%;
  min-height: 20px;
  gap: 5px; /* 文字和图标之间保持一点点间隔 */
}

.order-no-content {
  flex: 0 1 auto; /* 改为自适应宽度，不占满剩余空间 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: calc(100% - 16px); /* 为图标预留空间 */
}

/* 复制按钮样式 */
.copy-btn {
  padding: 2px !important;
  height: 20px !important;
  min-height: 20px !important;
  width: 20px !important;
  font-size: 12px;
  opacity: 0.6;
  transition: opacity 0.2s;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}



.copy-btn:hover {
  opacity: 1;
}

/* 审批状态标签样式 */
.approval-tag :deep(.el-tag) {
  font-size: 9px !important;
  padding: 1px 4px !important;
  height: 16px !important;
  line-height: 14px !important;
  border-radius: 6px !important;
  font-weight: 500 !important;
  flex-shrink: 0;
}


</style>
