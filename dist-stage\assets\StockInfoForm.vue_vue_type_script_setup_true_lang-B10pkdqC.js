import{a as de,d as ie,au as T,D as P}from"./index-BeQABqnP.js";import{_ as ne}from"./Dialog.vue_vue_type_style_index_0_lang-DpoeWsKb.js";import{S as D}from"./index-DCEvuXac.js";import{WarehouseApi as re}from"./index-CASHthoJ.js";import{W as me}from"./index-DnvZjr4Y.js";import{_ as se}from"./TransactionForm.vue_vue_type_script_setup_true_lang-BsuwVBdY.js";import{_ as pe}from"./index-CZs2S1Cj.js";import{e as ce,g as ve}from"./commonBiz-Dnw63na0.js";import{M as L}from"./index-D4qK--X-.js";import{h as ye}from"./tree-COGD3qag.js";import{h as fe,i as Ve,j as _e,aj as he,k as be,x as Ue,Q as ke,F as we,a1 as Ie,ak as Ne,a0 as Ce,$ as Se,f as ge}from"./form-designer-DQFPUccF.js";import{k as xe,r as p,P as Qe,c as qe,b as W,e as Te,y as v,m as s,z as u,A as Pe,H as t,u as a,l as f,G as V,$ as _,h as $,E as z,n as De}from"./form-create-B86qX0W_.js";const Ae=xe({name:"StockInfoForm",__name:"StockInfoForm",emits:["success"],setup(Ee,{expose:O,emit:j}){const{t:N}=de(),A=ie(),h=p(!1),E=p(""),b=p(!1),F=p(""),e=p({id:void 0,materialId:void 0,materialName:void 0,materialCode:void 0,materialType:void 0,materialSource:void 0,batchNo:void 0,quantity:void 0,lockQuantity:void 0,unlockQuantity:void 0,quantityUnit:void 0,auxiliaryUnit:void 0,auxiliaryQuantity:void 0,inventoryQuantity:void 0,price:void 0,priceUnit:void 0,inDate:void 0,warehouseId:void 0,warehouseName:void 0,locationId:void 0,locationName:void 0,locations:void 0,status:void 0,totalCost:void 0,remark:void 0,tenantName:void 0,purchasePrice:void 0,salePrice:void 0}),G=Qe({}),C=p(),I=p("transaction"),S=p();O({open:async(n,o,i)=>{if(h.value=!0,E.value=N("action."+n),F.value=n,B(),k.value.length===0&&await M(),o){b.value=!0;try{if(e.value=await D.getStockInfo(o),e.value.quantityUnit&&typeof e.value.quantityUnit=="string"&&(e.value.quantityUnit=parseInt(e.value.quantityUnit)),e.value.auxiliaryUnit&&typeof e.value.auxiliaryUnit=="string"&&(e.value.auxiliaryUnit=parseInt(e.value.auxiliaryUnit)),e.value.priceUnit&&typeof e.value.priceUnit=="string"&&(e.value.priceUnit=parseInt(e.value.priceUnit)),e.value.materialId)try{const d=await L.getMaterial(e.value.materialId);e.value.materialName=d.name,e.value.materialCode=d.fullCode,e.value.materialType=d.type,e.value.materialSource=d.source,e.value.spec=d.spec,await De(),Q.value++}catch{}e.value.warehouseId&&q(e.value.warehouseId)}finally{b.value=!1}}else if(i)try{const d=await L.getMaterial(i);e.value.materialId=d.id,e.value.materialName=d.name,e.value.materialCode=d.fullCode,e.value.materialType=d.type,e.value.materialSource=d.source,e.value.spec=d.spec}catch{}}});const H=j,K=async()=>{await C.value.validate();try{await S.value.validate()}catch{return void(I.value="transaction")}b.value=!0;try{const n=e.value;n.transactions=S.value.getData(),F.value==="create"?(await D.createStockInfo(n),A.success(N("common.createSuccess"))):(await D.updateStockInfo(n),A.success(N("common.updateSuccess"))),h.value=!1,H("success")}finally{b.value=!1}},B=()=>{var n;e.value={id:void 0,materialId:void 0,materialName:void 0,materialCode:void 0,materialType:void 0,materialSource:void 0,batchNo:void 0,quantity:void 0,lockQuantity:void 0,unlockQuantity:void 0,quantityUnit:void 0,auxiliaryUnit:void 0,auxiliaryQuantity:void 0,inventoryQuantity:void 0,price:void 0,priceUnit:void 0,inDate:void 0,warehouseId:void 0,warehouseName:void 0,locationId:void 0,locationName:void 0,locations:void 0,status:void 0,totalCost:void 0,remark:void 0,tenantName:void 0,purchasePrice:void 0,salePrice:void 0},(n=C.value)==null||n.resetFields()},k=p([]),g=p([]),x=p([]),U=p([]),w=p([]),J=qe(()=>{if(e.value.materialId&&e.value.materialName)return{value:e.value.materialId,label:e.value.materialName}}),Q=p(0);W([()=>e.value.materialId,()=>e.value.materialName],()=>{Q.value++},{deep:!0});const M=async()=>{try{k.value=await ve()}catch{}},q=n=>{w.value=n?U.value.filter(o=>o.warehouseId===n):U.value},X=n=>{e.value.locationName=void 0;const o=g.value.find(i=>i.name===n);o?(e.value.warehouseId=o.id,q(o.id)):(e.value.warehouseId=void 0,w.value=U.value)};return W(()=>e.value.warehouseId,n=>{n&&U.value.length>0&&q(n)},{immediate:!0}),Te(async()=>{await M(),await(async()=>{try{const n=await re.getWarehouseList({pageNo:1,pageSize:100});g.value=n||[];const o=ye(n||[],"id","parentId"),i=d=>d.map(m=>{const c=m.children&&m.children.length>0;return{...m,disabled:c,children:c?i(m.children):void 0}});x.value=i(o)}catch{g.value=[],x.value=[]}})(),await(async()=>{try{const n=await me.getWarehouseLocationPage({pageNo:1,pageSize:100});U.value=n.list||[],w.value=n.list||[]}catch{U.value=[],w.value=[]}})()}),(n,o)=>{const i=he,d=_e,m=be,c=ke,y=Ue,Y=we,Z=Ie,ee=Ve,ae=fe,le=Se,te=Ce,R=ge,oe=ne,ue=Ne;return s(),v(oe,{title:a(E),modelValue:a(h),"onUpdate:modelValue":o[27]||(o[27]=l=>$(h)?h.value=l:null),width:1e3},{footer:u(()=>[t(R,{onClick:K,type:"primary",disabled:a(b)},{default:u(()=>o[28]||(o[28]=[z("\u786E \u5B9A")])),_:1},8,["disabled"]),t(R,{onClick:o[26]||(o[26]=l=>h.value=!1)},{default:u(()=>o[29]||(o[29]=[z("\u53D6 \u6D88")])),_:1})]),default:u(()=>[Pe((s(),v(ae,{ref_key:"formRef",ref:C,model:a(e),rules:a(G),"label-width":"100px"},{default:u(()=>[t(ee,{gutter:20},{default:u(()=>[t(d,{span:8},{default:u(()=>[t(i,{label:"\u7269\u6599",prop:"materialId"},{default:u(()=>[(s(),v(pe,{key:`material-select-${a(Q)}`,filterable:"",clearable:"",class:"!w-240px",modelValue:a(e).materialName,"onUpdate:modelValue":o[0]||(o[0]=l=>a(e).materialName=l),placeholder:"\u8BF7\u9009\u62E9\u7269\u6599","initial-load":!1,"load-method":a(ce),"label-key":"name",disabled:!1,"value-key":"id","default-value":a(J),"query-key":"name",onChange:o[1]||(o[1]=(l,r)=>{r?(a(e).materialId=r.id,a(e).materialName=r==null?void 0:r.name,a(e).materialCode=r==null?void 0:r.fullCode,a(e).materialType=r==null?void 0:r.type,a(e).materialSource=r==null?void 0:r.source,a(e).quantityUnit=Number(r==null?void 0:r.unit),a(e).spec=r==null?void 0:r.spec):(a(e).materialId=void 0,a(e).materialName=void 0,a(e).materialCode=void 0,a(e).materialType=void 0,a(e).materialSource=void 0,a(e).quantityUnit=void 0,a(e).spec=void 0)})},null,8,["modelValue","load-method","default-value"]))]),_:1})]),_:1}),t(d,{span:8},{default:u(()=>[t(i,{label:"\u7269\u6599\u7F16\u7801",prop:"materialCode"},{default:u(()=>[t(m,{modelValue:a(e).materialCode,"onUpdate:modelValue":o[2]||(o[2]=l=>a(e).materialCode=l),placeholder:"\u8BF7\u8F93\u5165\u7269\u6599\u7F16\u7801",disabled:""},null,8,["modelValue"])]),_:1})]),_:1}),t(d,{span:8},{default:u(()=>[t(i,{label:"\u7269\u6599\u89C4\u683C",prop:"spec"},{default:u(()=>[t(m,{modelValue:a(e).spec,"onUpdate:modelValue":o[3]||(o[3]=l=>a(e).spec=l),placeholder:"\u8BF7\u8F93\u5165\u7269\u6599\u89C4\u683C",disabled:""},null,8,["modelValue"])]),_:1})]),_:1}),t(d,{span:8},{default:u(()=>[t(i,{label:"\u7269\u6599\u7C7B\u578B",prop:"materialType"},{default:u(()=>[t(y,{modelValue:a(e).materialType,"onUpdate:modelValue":o[4]||(o[4]=l=>a(e).materialType=l),placeholder:"\u8BF7\u9009\u62E9\u7269\u6599\u7C7B\u578B",disabled:""},{default:u(()=>[(s(!0),f(V,null,_(a(T)(a(P).MATERIAL_TYPE),l=>(s(),v(c,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),t(d,{span:8},{default:u(()=>[t(i,{label:"\u7269\u6599\u6765\u6E90",prop:"materialSource"},{default:u(()=>[t(y,{modelValue:a(e).materialSource,"onUpdate:modelValue":o[5]||(o[5]=l=>a(e).materialSource=l),placeholder:"\u8BF7\u9009\u62E9\u7269\u6599\u6765\u6E90",disabled:""},{default:u(()=>[(s(!0),f(V,null,_(a(T)(a(P).MATERIAL_SOURCE),l=>(s(),v(c,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),t(d,{span:8},{default:u(()=>[t(i,{label:"\u6570\u91CF",prop:"quantity"},{default:u(()=>[t(m,{modelValue:a(e).quantity,"onUpdate:modelValue":o[6]||(o[6]=l=>a(e).quantity=l),placeholder:"\u8BF7\u8F93\u5165\u6570\u91CF",disabled:""},null,8,["modelValue"])]),_:1})]),_:1}),t(d,{span:8},{default:u(()=>[t(i,{label:"\u9501\u5B9A\u6570\u91CF",prop:"lockQuantity"},{default:u(()=>[t(m,{modelValue:a(e).lockQuantity,"onUpdate:modelValue":o[7]||(o[7]=l=>a(e).lockQuantity=l),placeholder:"\u8BF7\u8F93\u5165\u9501\u5B9A\u6570\u91CF",disabled:""},null,8,["modelValue"])]),_:1})]),_:1}),t(d,{span:8},{default:u(()=>[t(i,{label:"\u672A\u9501\u6570\u91CF",prop:"unlockQuantity"},{default:u(()=>[t(m,{modelValue:a(e).unlockQuantity,"onUpdate:modelValue":o[8]||(o[8]=l=>a(e).unlockQuantity=l),placeholder:"\u8BF7\u8F93\u5165\u672A\u9501\u6570\u91CF",disabled:""},null,8,["modelValue"])]),_:1})]),_:1}),t(d,{span:8},{default:u(()=>[t(i,{label:"\u6570\u91CF\u5355\u4F4D",prop:"quantityUnit"},{default:u(()=>[t(y,{modelValue:a(e).quantityUnit,"onUpdate:modelValue":o[9]||(o[9]=l=>a(e).quantityUnit=l),placeholder:"\u8BF7\u9009\u62E9\u6570\u91CF\u5355\u4F4D",disabled:""},{default:u(()=>[(s(!0),f(V,null,_(a(k),l=>(s(),v(c,{key:l.id,label:l.name,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),t(d,{span:8},{default:u(()=>[t(i,{label:"\u57FA\u672C\u5355\u4F4D",prop:"auxiliaryUnit"},{default:u(()=>[t(y,{modelValue:a(e).auxiliaryUnit,"onUpdate:modelValue":o[10]||(o[10]=l=>a(e).auxiliaryUnit=l),placeholder:"\u8BF7\u9009\u62E9\u57FA\u672C\u5355\u4F4D",disabled:""},{default:u(()=>[(s(!0),f(V,null,_(a(k),l=>(s(),v(c,{key:l.id,label:l.name,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),t(d,{span:8},{default:u(()=>[t(i,{label:"\u57FA\u672C\u5355\u4F4D\u6570\u91CF",prop:"auxiliaryQuantity"},{default:u(()=>[t(m,{modelValue:a(e).auxiliaryQuantity,"onUpdate:modelValue":o[11]||(o[11]=l=>a(e).auxiliaryQuantity=l),placeholder:"\u8BF7\u8F93\u5165\u57FA\u672C\u5355\u4F4D\u6570\u91CF"},null,8,["modelValue"])]),_:1})]),_:1}),t(d,{span:8},{default:u(()=>[t(i,{label:"\u5E93\u5B58\u6570\u91CF",prop:"inventoryQuantity"},{default:u(()=>[t(m,{modelValue:a(e).inventoryQuantity,"onUpdate:modelValue":o[12]||(o[12]=l=>a(e).inventoryQuantity=l),placeholder:"\u8BF7\u8F93\u5165\u5E93\u5B58\u6570\u91CF"},null,8,["modelValue"])]),_:1})]),_:1}),t(d,{span:8},{default:u(()=>[t(i,{label:"\u4EF7\u683C",prop:"price"},{default:u(()=>[t(m,{modelValue:a(e).price,"onUpdate:modelValue":o[13]||(o[13]=l=>a(e).price=l),placeholder:"\u8BF7\u8F93\u5165\u4EF7\u683C"},null,8,["modelValue"])]),_:1})]),_:1}),t(d,{span:8},{default:u(()=>[t(i,{label:"\u4EF7\u683C\u5355\u4F4D",prop:"priceUnit"},{default:u(()=>[t(y,{modelValue:a(e).priceUnit,"onUpdate:modelValue":o[14]||(o[14]=l=>a(e).priceUnit=l),placeholder:"\u8BF7\u9009\u62E9\u4EF7\u683C\u5355\u4F4D"},{default:u(()=>[(s(!0),f(V,null,_(a(k),l=>(s(),v(c,{key:l.id,label:l.name,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),t(d,{span:8},{default:u(()=>[t(i,{label:"\u5165\u5E93\u65E5\u671F",prop:"inDate"},{default:u(()=>[t(Y,{modelValue:a(e).inDate,"onUpdate:modelValue":o[15]||(o[15]=l=>a(e).inDate=l),type:"date","value-format":"x",placeholder:"\u9009\u62E9\u5165\u5E93\u65E5\u671F"},null,8,["modelValue"])]),_:1})]),_:1}),t(d,{span:8},{default:u(()=>[t(i,{label:"\u4ED3\u5E93\u540D\u79F0",prop:"warehouseName"},{default:u(()=>[t(Z,{modelValue:a(e).warehouseName,"onUpdate:modelValue":o[16]||(o[16]=l=>a(e).warehouseName=l),data:a(x),placeholder:"\u8BF7\u9009\u62E9\u4ED3\u5E93\u540D\u79F0",clearable:"","check-strictly":"","render-after-expand":!1,class:"!w-240px","node-key":"id",props:{value:"name",label:"name",children:"children",disabled:"disabled"},onChange:X},null,8,["modelValue","data"])]),_:1})]),_:1}),t(d,{span:8},{default:u(()=>[t(i,{label:"\u4ED3\u4F4D\u540D\u79F0",prop:"locationName"},{default:u(()=>[t(y,{modelValue:a(e).locationName,"onUpdate:modelValue":o[17]||(o[17]=l=>a(e).locationName=l),placeholder:"\u8BF7\u9009\u62E9\u4ED3\u4F4D\u540D\u79F0",clearable:"",class:"!w-240px"},{default:u(()=>[(s(!0),f(V,null,_(a(w),l=>(s(),v(c,{key:l.id,label:l.name,value:l.name},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),t(d,{span:8},{default:u(()=>[t(i,{label:"\u9884\u7559\u7684\u591A\u4ED3\u4F4D",prop:"locations"},{default:u(()=>[t(m,{modelValue:a(e).locations,"onUpdate:modelValue":o[18]||(o[18]=l=>a(e).locations=l),placeholder:"\u8BF7\u8F93\u5165\u9884\u7559\u7684\u591A\u4ED3\u4F4D"},null,8,["modelValue"])]),_:1})]),_:1}),t(d,{span:8},{default:u(()=>[t(i,{label:"\u5E93\u5B58\u72B6\u6001",prop:"status",disabled:""},{default:u(()=>[t(y,{modelValue:a(e).status,"onUpdate:modelValue":o[19]||(o[19]=l=>a(e).status=l),placeholder:"\u8BF7\u9009\u62E9\u5E93\u5B58\u72B6\u6001",disabled:""},{default:u(()=>[(s(!0),f(V,null,_(a(T)(a(P).STOCK_STATUS),l=>(s(),v(c,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),t(d,{span:8},{default:u(()=>[t(i,{label:"\u603B\u4EF7\u503C",prop:"totalCost"},{default:u(()=>[t(m,{modelValue:a(e).totalCost,"onUpdate:modelValue":o[20]||(o[20]=l=>a(e).totalCost=l),placeholder:"\u8BF7\u8F93\u5165\u603B\u4EF7\u503C"},null,8,["modelValue"])]),_:1})]),_:1}),t(d,{span:8},{default:u(()=>[t(i,{label:"\u5907\u6CE8",prop:"remark"},{default:u(()=>[t(m,{modelValue:a(e).remark,"onUpdate:modelValue":o[21]||(o[21]=l=>a(e).remark=l),placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue"])]),_:1})]),_:1}),t(d,{span:8},{default:u(()=>[t(i,{label:"\u79DF\u6237\u540D\u79F0",prop:"tenantName"},{default:u(()=>[t(m,{modelValue:a(e).tenantName,"onUpdate:modelValue":o[22]||(o[22]=l=>a(e).tenantName=l),placeholder:"\u8BF7\u8F93\u5165\u79DF\u6237\u540D\u79F0"},null,8,["modelValue"])]),_:1})]),_:1}),t(d,{span:8},{default:u(()=>[t(i,{label:"\u91C7\u8D2D\u4EF7\u683C",prop:"purchasePrice"},{default:u(()=>[t(m,{modelValue:a(e).purchasePrice,"onUpdate:modelValue":o[23]||(o[23]=l=>a(e).purchasePrice=l),placeholder:"\u8BF7\u8F93\u5165\u91C7\u8D2D\u4EF7\u683C"},null,8,["modelValue"])]),_:1})]),_:1}),t(d,{span:8},{default:u(()=>[t(i,{label:"\u9500\u552E\u4EF7\u683C",prop:"salePrice"},{default:u(()=>[t(m,{modelValue:a(e).salePrice,"onUpdate:modelValue":o[24]||(o[24]=l=>a(e).salePrice=l),placeholder:"\u8BF7\u8F93\u5165\u9500\u552E\u4EF7\u683C"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])),[[ue,a(b)]]),t(te,{modelValue:a(I),"onUpdate:modelValue":o[25]||(o[25]=l=>$(I)?I.value=l:null)},{default:u(()=>[t(le,{label:"\u5E93\u5B58\u4EA4\u6613\u660E\u7EC6",name:"transaction"},{default:u(()=>[t(se,{ref_key:"transactionFormRef",ref:S,"inventory-id":a(e).id},null,8,["inventory-id"])]),_:1})]),_:1},8,["modelValue"])]),_:1},8,["title","modelValue"])}}});export{Ae as _};
