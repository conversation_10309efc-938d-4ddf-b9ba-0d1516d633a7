import{a as G,d as M,h as N,D as O}from"./index-BeQABqnP.js";import{_ as T}from"./Dialog.vue_vue_type_style_index_0_lang-DpoeWsKb.js";import{W as V}from"./index-GJzpvaVt.js";import{C as j}from"./constants-C3gLHYOK.js";import{h as z,aj as B,k as D,s as H,u as K,l as L,ak as R,f as $}from"./form-designer-DQFPUccF.js";import{k as I,r as m,P as J,y as h,m as c,z as s,A as Q,u as a,H as r,l as X,G as Y,$ as Z,E as _,F as ee,h as ae}from"./form-create-B86qX0W_.js";const le=I({name:"WarehouseForm",__name:"WarehouseForm",emits:["success"],setup(oe,{expose:y,emit:P}){const{t:n}=G(),k=M(),t=m(!1),b=m(""),i=m(!1),g=m(""),o=m({id:void 0,name:void 0,address:void 0,sort:void 0,remark:void 0,principal:void 0,warehousePrice:void 0,truckagePrice:void 0,status:void 0}),U=J({name:[{required:!0,message:"\u4ED3\u5E93\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],sort:[{required:!0,message:"\u6392\u5E8F\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],status:[{required:!0,message:"\u5F00\u542F\u72B6\u6001\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),v=m();y({open:async(u,e)=>{if(t.value=!0,b.value=n("action."+u),g.value=u,q(),e){i.value=!0;try{o.value=await V.getWarehouse(e)}finally{i.value=!1}}}});const W=P,S=async()=>{await v.value.validate(),i.value=!0;try{const u=o.value;g.value==="create"?(await V.createWarehouse(u),k.success(n("common.createSuccess"))):(await V.updateWarehouse(u),k.success(n("common.updateSuccess"))),t.value=!1,W("success")}finally{i.value=!1}},q=()=>{var u;o.value={id:void 0,name:void 0,address:void 0,sort:void 0,remark:void 0,principal:void 0,warehousePrice:void 0,truckagePrice:void 0,status:j.ENABLE},(u=v.value)==null||u.resetFields()};return(u,e)=>{const p=D,d=B,x=K,C=H,f=L,E=z,w=$,F=T,A=R;return c(),h(F,{title:a(b),modelValue:a(t),"onUpdate:modelValue":e[9]||(e[9]=l=>ae(t)?t.value=l:null)},{footer:s(()=>[r(w,{onClick:S,type:"primary",disabled:a(i)},{default:s(()=>e[10]||(e[10]=[_("\u786E \u5B9A")])),_:1},8,["disabled"]),r(w,{onClick:e[8]||(e[8]=l=>t.value=!1)},{default:s(()=>e[11]||(e[11]=[_("\u53D6 \u6D88")])),_:1})]),default:s(()=>[Q((c(),h(E,{ref_key:"formRef",ref:v,model:a(o),rules:a(U),"label-width":"100px"},{default:s(()=>[r(d,{label:"\u4ED3\u5E93\u540D\u79F0",prop:"name"},{default:s(()=>[r(p,{modelValue:a(o).name,"onUpdate:modelValue":e[0]||(e[0]=l=>a(o).name=l),placeholder:"\u8BF7\u8F93\u5165\u4ED3\u5E93\u540D\u79F0"},null,8,["modelValue"])]),_:1}),r(d,{label:"\u4ED3\u5E93\u5730\u5740",prop:"address"},{default:s(()=>[r(p,{modelValue:a(o).address,"onUpdate:modelValue":e[1]||(e[1]=l=>a(o).address=l),placeholder:"\u8BF7\u8F93\u5165\u4ED3\u5E93\u5730\u5740"},null,8,["modelValue"])]),_:1}),r(d,{label:"\u4ED3\u5E93\u72B6\u6001",prop:"status"},{default:s(()=>[r(C,{modelValue:a(o).status,"onUpdate:modelValue":e[2]||(e[2]=l=>a(o).status=l)},{default:s(()=>[(c(!0),X(Y,null,Z(a(N)(a(O).COMMON_STATUS),l=>(c(),h(x,{key:l.value,value:l.value},{default:s(()=>[_(ee(l.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),r(d,{label:"\u4ED3\u50A8\u8D39",prop:"warehousePrice"},{default:s(()=>[r(f,{modelValue:a(o).warehousePrice,"onUpdate:modelValue":e[3]||(e[3]=l=>a(o).warehousePrice=l),placeholder:"\u8BF7\u8F93\u5165\u4ED3\u50A8\u8D39\uFF0C\u5355\u4F4D\uFF1A\u5143/\u5929/KG",min:0,precision:2,class:"!w-1/1"},null,8,["modelValue"])]),_:1}),r(d,{label:"\u642C\u8FD0\u8D39",prop:"truckagePrice"},{default:s(()=>[r(f,{modelValue:a(o).truckagePrice,"onUpdate:modelValue":e[4]||(e[4]=l=>a(o).truckagePrice=l),placeholder:"\u8BF7\u8F93\u5165\u642C\u8FD0\u8D39\uFF0C\u5355\u4F4D\uFF1A\u5143",min:0,precision:2,class:"!w-1/1"},null,8,["modelValue"])]),_:1}),r(d,{label:"\u8D1F\u8D23\u4EBA",prop:"principal"},{default:s(()=>[r(p,{modelValue:a(o).principal,"onUpdate:modelValue":e[5]||(e[5]=l=>a(o).principal=l),placeholder:"\u8BF7\u8F93\u5165\u8D1F\u8D23\u4EBA"},null,8,["modelValue"])]),_:1}),r(d,{label:"\u6392\u5E8F",prop:"sort"},{default:s(()=>[r(f,{modelValue:a(o).sort,"onUpdate:modelValue":e[6]||(e[6]=l=>a(o).sort=l),placeholder:"\u8BF7\u8F93\u5165\u6392\u5E8F",precision:0,class:"!w-1/1"},null,8,["modelValue"])]),_:1}),r(d,{label:"\u5907\u6CE8",prop:"remark"},{default:s(()=>[r(p,{type:"textarea",modelValue:a(o).remark,"onUpdate:modelValue":e[7]||(e[7]=l=>a(o).remark=l),placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[A,a(i)]])]),_:1},8,["title","modelValue"])}}});export{le as _};
