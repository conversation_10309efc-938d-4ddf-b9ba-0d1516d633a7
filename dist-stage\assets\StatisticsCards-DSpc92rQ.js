import{_ as i,c as g}from"./index-BeQABqnP.js";import{ad as _,i as m,j as x,a7 as h,ah as b}from"./form-designer-DQFPUccF.js";import{k as w,l as z,m as v,v as u,H as s,z as t,E as e,F as d,u as o}from"./form-create-B86qX0W_.js";const k={class:"statistics-container"},y={class:"statistics-card-wrapper"},j={class:"statistics-card-wrapper"},G={class:"statistics-card-wrapper"},O={class:"statistics-card-wrapper"},C=g(w({__name:"StatisticsCards",props:{statistics:{}},setup:E=>(l,r)=>{const a=h,c=x,n=b,p=m,f=_;return v(),z("div",k,[u("div",y,[s(f,{shadow:"hover",class:"statistics-card border-blue"},{default:t(()=>[s(p,{justify:"space-between",align:"top"},{default:t(()=>[s(c,{span:16},{default:t(()=>[s(a,{size:"small",class:"!text-gray-500"},{default:t(()=>r[0]||(r[0]=[e("\u603B\u8BA2\u5355\u6570")])),_:1}),s(a,{size:"large",tag:"h3",class:"!text-2xl !font-bold !mt-1 !block"},{default:t(()=>[e(d(l.statistics.saleOrders),1)]),_:1}),s(a,{size:"small",class:"!text-green-500 !mt-2 !flex !items-center"},{default:t(()=>[s(o(i),{icon:"ep:arrow-up",class:"mr-1"}),e(" \u8F83\u4E0A\u6708\u589E\u957F "+d(l.statistics.saleGrowth)+"% ",1)]),_:1})]),_:1}),s(c,{span:8,class:"!text-right"},{default:t(()=>[s(n,{size:40,class:"!bg-blue-100"},{default:t(()=>[s(o(i),{icon:"ep:sell",class:"!text-blue-500"})]),_:1})]),_:1})]),_:1})]),_:1})]),u("div",j,[s(f,{shadow:"hover",class:"statistics-card border-green"},{default:t(()=>[s(p,{justify:"space-between",align:"top"},{default:t(()=>[s(c,{span:16},{default:t(()=>[s(a,{size:"small",class:"!text-gray-500"},{default:t(()=>r[1]||(r[1]=[e("\u5DF2\u5B8C\u6210\u8BA2\u5355")])),_:1}),s(a,{size:"large",tag:"h3",class:"!text-2xl !font-bold !mt-1 !block"},{default:t(()=>[e(d(l.statistics.purchaseOrders),1)]),_:1}),s(a,{size:"small",class:"!text-orange-500 !mt-2 !flex !items-center"},{default:t(()=>[s(o(i),{icon:"ep:arrow-up",class:"mr-1"}),e(" \u8F83\u4E0A\u6708\u589E\u957F "+d(l.statistics.purchaseGrowth)+"% ",1)]),_:1})]),_:1}),s(c,{span:8,class:"!text-right"},{default:t(()=>[s(n,{size:40,class:"!bg-orange-100"},{default:t(()=>[s(o(i),{icon:"ep:shopping-cart",class:"!text-orange-500"})]),_:1})]),_:1})]),_:1})]),_:1})]),u("div",G,[s(f,{shadow:"hover",class:"statistics-card border-orange"},{default:t(()=>[s(p,{justify:"space-between",align:"top"},{default:t(()=>[s(c,{span:16},{default:t(()=>[s(a,{size:"small",class:"!text-gray-500"},{default:t(()=>r[2]||(r[2]=[e("\u8FDB\u884C\u4E2D\u8BA2\u5355")])),_:1}),s(a,{size:"large",tag:"h3",class:"!text-2xl !font-bold !mt-1 !block"},{default:t(()=>[e(d(l.statistics.workOrders),1)]),_:1}),s(a,{size:"small",class:"!text-green-500 !mt-2 !flex !items-center"},{default:t(()=>[s(o(i),{icon:"ep:arrow-up",class:"mr-1"}),e(" \u8F83\u4E0A\u6708\u589E\u957F "+d(l.statistics.workGrowth)+"% ",1)]),_:1})]),_:1}),s(c,{span:8,class:"!text-right"},{default:t(()=>[s(n,{size:40,class:"!bg-green-100"},{default:t(()=>[s(o(i),{icon:"ep:tools",class:"!text-green-500"})]),_:1})]),_:1})]),_:1})]),_:1})]),u("div",O,[s(f,{shadow:"hover",class:"statistics-card border-red"},{default:t(()=>[s(p,{justify:"space-between",align:"top"},{default:t(()=>[s(c,{span:16},{default:t(()=>[s(a,{size:"small",class:"!text-gray-500"},{default:t(()=>r[3]||(r[3]=[e("\u903E\u671F\u8BA2\u5355")])),_:1}),s(a,{size:"large",tag:"h3",class:"!text-2xl !font-bold !mt-1 !block"},{default:t(()=>[e(d(l.statistics.completedOrders),1)]),_:1}),s(a,{size:"small",class:"!text-green-500 !mt-2 !flex !items-center"},{default:t(()=>[s(o(i),{icon:"ep:arrow-up",class:"mr-1"}),e(" \u8F83\u4E0A\u6708\u589E\u957F "+d(l.statistics.completedGrowth)+"% ",1)]),_:1})]),_:1}),s(c,{span:8,class:"!text-right"},{default:t(()=>[s(n,{size:40,class:"!bg-green-100"},{default:t(()=>[s(o(i),{icon:"ep:check",class:"!text-green-500"})]),_:1})]),_:1})]),_:1})]),_:1})])])}}),[["__scopeId","data-v-3f931773"]]);export{C as default};
