import{aM as S,ax as q,aL as Q,ay as R}from"./index-BeQABqnP.js";import{P as Z}from"./index-COu9YF6c.js";import{W as J}from"./index-GJzpvaVt.js";import{S as K}from"./index-BGb7xQiJ.js";import{ak as T,h as X,Z as Y,_ as ee,aj as ae,x as le,Q as oe,k as de,l as te,f as re,i as ue}from"./form-designer-DQFPUccF.js";import{k as ie,r as h,P as se,b as B,e as ne,l as U,m as c,G as k,A as me,y as g,C as pe,u as s,z as l,H as e,$ as E,E as L}from"./form-create-B86qX0W_.js";const ce=ie({__name:"StockOutItemForm",props:{items:{},disabled:{type:Boolean}},setup(j,{expose:G}){const W=j,z=h(!1),m=h([]),w=se({inId:[{required:!0,message:"\u51FA\u5E93\u7F16\u53F7\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],warehouseId:[{required:!0,message:"\u4ED3\u5E93\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],productId:[{required:!0,message:"\u4EA7\u54C1\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],count:[{required:!0,message:"\u4EA7\u54C1\u6570\u91CF\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),I=h([]),v=h([]),x=h([]),y=h(void 0);B(()=>W.items,async d=>{m.value=d},{immediate:!0}),B(()=>m.value,d=>{d&&d.length!==0&&d.forEach(r=>{r.totalPrice=R(r.productPrice,r.count)})},{deep:!0});const A=d=>{const{columns:r,data:i}=d,p=[];return r.forEach((f,u)=>{if(u!==0)if(["count","totalPrice"].includes(f.property)){const n=Q(i.map(V=>Number(V[f.property])));p[u]=f.property==="count"?S(n):q(n)}else p[u]="";else p[u]="\u5408\u8BA1"}),p},C=()=>{var r;const d={id:void 0,warehouseId:(r=y.value)==null?void 0:r.id,productId:void 0,productUnitName:void 0,productBarCode:void 0,productPrice:void 0,stockCount:void 0,count:1,totalPrice:void 0,remark:void 0};m.value.push(d)},P=async d=>{if(!d.productId||!d.warehouseId)return;const r=await K.getStock2(d.productId,d.warehouseId);d.stockCount=r?r.count:0};return G({validate:()=>I.value.validate()}),ne(async()=>{v.value=await Z.getProductSimpleList(),x.value=await J.getWarehouseSimpleList(),y.value=x.value.find(d=>d.defaultStatus),m.value.length===0&&C()}),(d,r)=>{const i=ee,p=oe,f=le,u=ae,n=de,V=te,$=re,F=Y,H=X,M=ue,O=T;return c(),U(k,null,[me((c(),g(H,{ref_key:"formRef",ref:I,model:s(m),rules:s(w),"label-width":"0px","inline-message":!0,disabled:d.disabled},{default:l(()=>[e(F,{data:s(m),"show-summary":"","summary-method":A,class:"-mt-10px"},{default:l(()=>[e(i,{label:"\u5E8F\u53F7",type:"index",align:"center",width:"60"}),e(i,{label:"\u4ED3\u5E93\u540D\u79F0","min-width":"125"},{default:l(({row:a,$index:t})=>[e(u,{prop:`${t}.warehouseId`,rules:s(w).warehouseId,class:"mb-0px!"},{default:l(()=>[e(f,{modelValue:a.warehouseId,"onUpdate:modelValue":o=>a.warehouseId=o,clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u4ED3\u5E93",onChange:o=>((N,b)=>{P(b)})(0,a)},{default:l(()=>[(c(!0),U(k,null,E(s(x),o=>(c(),g(p,{key:o.id,label:o.name,value:o.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),_:2},1032,["prop","rules"])]),_:1}),e(i,{label:"\u4EA7\u54C1\u540D\u79F0","min-width":"180"},{default:l(({row:a,$index:t})=>[e(u,{prop:`${t}.productId`,rules:s(w).productId,class:"mb-0px!"},{default:l(()=>[e(f,{modelValue:a.productId,"onUpdate:modelValue":o=>a.productId=o,clearable:"",filterable:"",onChange:o=>((N,b)=>{const _=v.value.find(D=>D.id===N);_&&(b.productUnitName=_.unitName,b.productBarCode=_.barCode,b.productPrice=_.minPrice),P(b)})(o,a),placeholder:"\u8BF7\u9009\u62E9\u4EA7\u54C1"},{default:l(()=>[(c(!0),U(k,null,E(s(v),o=>(c(),g(p,{key:o.id,label:o.name,value:o.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),_:2},1032,["prop","rules"])]),_:1}),e(i,{label:"\u5E93\u5B58","min-width":"100"},{default:l(({row:a})=>[e(u,{class:"mb-0px!"},{default:l(()=>[e(n,{disabled:"",modelValue:a.stockCount,"onUpdate:modelValue":t=>a.stockCount=t,formatter:s(S)},null,8,["modelValue","onUpdate:modelValue","formatter"])]),_:2},1024)]),_:1}),e(i,{label:"\u6761\u7801","min-width":"150"},{default:l(({row:a})=>[e(u,{class:"mb-0px!"},{default:l(()=>[e(n,{disabled:"",modelValue:a.productBarCode,"onUpdate:modelValue":t=>a.productBarCode=t},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:1}),e(i,{label:"\u5355\u4F4D","min-width":"80"},{default:l(({row:a})=>[e(u,{class:"mb-0px!"},{default:l(()=>[e(n,{disabled:"",modelValue:a.productUnitName,"onUpdate:modelValue":t=>a.productUnitName=t},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:1}),e(i,{label:"\u6570\u91CF",prop:"count",fixed:"right","min-width":"140"},{default:l(({row:a,$index:t})=>[e(u,{prop:`${t}.count`,rules:s(w).count,class:"mb-0px!"},{default:l(()=>[e(V,{modelValue:a.count,"onUpdate:modelValue":o=>a.count=o,"controls-position":"right",min:.001,precision:3,class:"!w-100%"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])]),_:1}),e(i,{label:"\u4EA7\u54C1\u5355\u4EF7",fixed:"right","min-width":"120"},{default:l(({row:a,$index:t})=>[e(u,{prop:`${t}.productPrice`,class:"mb-0px!"},{default:l(()=>[e(V,{modelValue:a.productPrice,"onUpdate:modelValue":o=>a.productPrice=o,"controls-position":"right",min:.01,precision:2,class:"!w-100%"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"])]),_:1}),e(i,{label:"\u5408\u8BA1\u91D1\u989D",prop:"totalPrice",fixed:"right","min-width":"100"},{default:l(({row:a,$index:t})=>[e(u,{prop:`${t}.totalPrice`,class:"mb-0px!"},{default:l(()=>[e(n,{disabled:"",modelValue:a.totalPrice,"onUpdate:modelValue":o=>a.totalPrice=o,formatter:s(q)},null,8,["modelValue","onUpdate:modelValue","formatter"])]),_:2},1032,["prop"])]),_:1}),e(i,{label:"\u5907\u6CE8","min-width":"150"},{default:l(({row:a,$index:t})=>[e(u,{prop:`${t}.remark`,class:"mb-0px!"},{default:l(()=>[e(n,{modelValue:a.remark,"onUpdate:modelValue":o=>a.remark=o,placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"])]),_:1}),e(i,{align:"center",fixed:"right",label:"\u64CD\u4F5C",width:"60"},{default:l(({$index:a})=>[e($,{onClick:t=>{return o=a,void m.value.splice(o,1);var o},link:""},{default:l(()=>r[0]||(r[0]=[L("\u2014")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1},8,["model","rules","disabled"])),[[O,s(z)]]),d.disabled?pe("",!0):(c(),g(M,{key:0,justify:"center",class:"mt-3"},{default:l(()=>[e($,{onClick:C,round:""},{default:l(()=>r[1]||(r[1]=[L("+ \u6DFB\u52A0\u51FA\u5E93\u4EA7\u54C1")])),_:1})]),_:1}))],64)}}});export{ce as _};
