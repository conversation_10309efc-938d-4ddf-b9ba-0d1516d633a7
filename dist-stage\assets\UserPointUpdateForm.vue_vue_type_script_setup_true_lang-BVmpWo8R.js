import{a as A,d as E}from"./index-BeQABqnP.js";import{_ as H}from"./Dialog.vue_vue_type_style_index_0_lang-DpoeWsKb.js";import{g as R,b as D}from"./index-ge1eh6aq.js";import{h as S,aj as B,k as G,l as I,s as J,u as K,ak as L,f as M}from"./form-designer-DQFPUccF.js";import{k as N,r as c,P as O,c as Q,y,m as _,z as u,A as W,u as a,H as o,E as r,h as b}from"./form-create-B86qX0W_.js";const X=N({name:"UpdatePointForm",__name:"UserPointUpdateForm",emits:["success"],setup(Y,{expose:P,emit:k}){const{t:w}=A(),f=E(),d=c(!1),s=c(!1),l=c({id:void 0,nickname:void 0,point:0,changePoint:0,changeType:1}),U=O({changePoint:[{required:!0,message:"\u53D8\u52A8\u79EF\u5206\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),p=c();P({open:async t=>{if(d.value=!0,F(),t){s.value=!0;try{l.value=await R(t),l.value.changeType=1,l.value.changePoint=0}finally{s.value=!1}}}});const T=k,x=async()=>{if(p&&await p.value.validate())if(l.value.changePoint<1)f.error("\u53D8\u52A8\u79EF\u5206\u4E0D\u80FD\u5C0F\u4E8E 1");else if(m.value<0)f.error("\u53D8\u52A8\u540E\u7684\u79EF\u5206\u4E0D\u80FD\u5C0F\u4E8E 0");else{s.value=!0;try{await D({id:l.value.id,point:l.value.changePoint*l.value.changeType}),f.success(w("common.updateSuccess")),d.value=!1,T("success")}finally{s.value=!1}}},F=()=>{var t;l.value={id:void 0,nickname:void 0,point:0,changePoint:0,changeType:1},(t=p.value)==null||t.resetFields()},m=Q(()=>l.value.point+l.value.changePoint*l.value.changeType);return(t,e)=>{const g=G,i=B,v=I,h=K,C=J,j=S,V=M,q=H,z=L;return _(),y(q,{modelValue:a(d),"onUpdate:modelValue":e[7]||(e[7]=n=>b(d)?d.value=n:null),title:"\u4FEE\u6539\u7528\u6237\u79EF\u5206",width:"600"},{footer:u(()=>[o(V,{disabled:a(s),type:"primary",onClick:x},{default:u(()=>e[10]||(e[10]=[r("\u786E \u5B9A")])),_:1},8,["disabled"]),o(V,{onClick:e[6]||(e[6]=n=>d.value=!1)},{default:u(()=>e[11]||(e[11]=[r("\u53D6 \u6D88")])),_:1})]),default:u(()=>[W((_(),y(j,{ref_key:"formRef",ref:p,model:a(l),rules:a(U),"label-width":"100px"},{default:u(()=>[o(i,{label:"\u7528\u6237\u7F16\u53F7",prop:"id"},{default:u(()=>[o(g,{modelValue:a(l).id,"onUpdate:modelValue":e[0]||(e[0]=n=>a(l).id=n),class:"!w-240px",disabled:""},null,8,["modelValue"])]),_:1}),o(i,{label:"\u7528\u6237\u6635\u79F0",prop:"nickname"},{default:u(()=>[o(g,{modelValue:a(l).nickname,"onUpdate:modelValue":e[1]||(e[1]=n=>a(l).nickname=n),class:"!w-240px",disabled:""},null,8,["modelValue"])]),_:1}),o(i,{label:"\u53D8\u52A8\u524D\u79EF\u5206",prop:"point"},{default:u(()=>[o(v,{modelValue:a(l).point,"onUpdate:modelValue":e[2]||(e[2]=n=>a(l).point=n),class:"!w-240px",disabled:""},null,8,["modelValue"])]),_:1}),o(i,{label:"\u53D8\u52A8\u7C7B\u578B",prop:"changeType"},{default:u(()=>[o(C,{modelValue:a(l).changeType,"onUpdate:modelValue":e[3]||(e[3]=n=>a(l).changeType=n)},{default:u(()=>[o(h,{value:1},{default:u(()=>e[8]||(e[8]=[r("\u589E\u52A0")])),_:1}),o(h,{value:-1},{default:u(()=>e[9]||(e[9]=[r("\u51CF\u5C11")])),_:1})]),_:1},8,["modelValue"])]),_:1}),o(i,{label:"\u53D8\u52A8\u79EF\u5206",prop:"changePoint"},{default:u(()=>[o(v,{modelValue:a(l).changePoint,"onUpdate:modelValue":e[4]||(e[4]=n=>a(l).changePoint=n),min:0,precision:0,class:"!w-240px"},null,8,["modelValue"])]),_:1}),o(i,{label:"\u53D8\u52A8\u540E\u79EF\u5206"},{default:u(()=>[o(v,{modelValue:a(m),"onUpdate:modelValue":e[5]||(e[5]=n=>b(m)?m.value=n:null),class:"!w-240px",disabled:""},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[z,a(s)]])]),_:1},8,["modelValue"])}}});export{X as _};
