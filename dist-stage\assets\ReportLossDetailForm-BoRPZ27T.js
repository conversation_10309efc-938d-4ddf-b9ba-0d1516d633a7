import{_ as q,c as D}from"./index-BeQABqnP.js";import{W as A}from"./index-B3LrXjfm.js";import{U as B}from"./index-BDU5cx5r.js";import{ak as F,h as L,Z as O,_ as R,aj as W,l as j,k as E,f as H}from"./form-designer-DQFPUccF.js";import{k as P,r as b,P as Z,b as G,A as J,u as p,y as w,m as _,z as d,H as s,C as K,v as i,F as c}from"./form-create-B86qX0W_.js";const M={class:"material-info"},S={class:"material-name"},T={class:"value"},X={class:"material-code"},Y={class:"value"},aa={class:"material-spec"},ea={class:"value"},la={class:"planned-quantity"},sa={class:"value"},ta=D(P({__name:"ReportLossDetailForm",props:{reportId:{},workId:{},disabled:{type:Boolean}},setup(g,{expose:U}){const k=g,f=b(!1),n=b([]),v=Z({materialId:[{required:!0,message:"\u7269\u6599\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],lossQuantity:[{required:!0,message:"\u635F\u8017\u6570\u91CF\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),y=b(),x=async l=>{if(!l)return"";try{const a=await B.getUnit(l);return(a==null?void 0:a.name)||""}catch{return""}};return G(()=>k.workId,async l=>{if(n.value=[],l)try{f.value=!0;const a=await A.getWorkOrderDetailListByBizOrderId(l);if(Array.isArray(a)&&a.length>0){const r=a.map((t,u)=>({id:void 0,num:u+1,materialId:t.materialId,materialName:t.materialName,materialCode:t.materialCode,spec:t.spec,plannedQuantity:t.plannedQuantity||t.quantity||0,lossQuantity:0,lossUnit:t.unit,lossUnitName:"",remark:""}));n.value=r;for(const t of n.value)t.lossUnit&&(t.lossUnitName=await x(t.lossUnit))}}catch{}finally{f.value=!1}},{immediate:!0}),U({validate:async()=>await y.value.validate(),getData:()=>n.value.filter(l=>l.materialId),setData:l=>{n.value=l.map((a,r)=>({...a,num:r+1}))}}),(l,a)=>{const r=R,t=j,u=W,h=E,V=q,I=H,Q=O,N=L,$=F;return J((_(),w(N,{ref_key:"formRef",ref:y,model:p(n),rules:p(v),"label-width":"0px","inline-message":!0},{default:d(()=>[s(Q,{data:p(n),class:"-mt-10px",border:"","max-height":400,style:{width:"100%"}},{default:d(()=>[s(r,{label:"\u5E8F\u53F7","min-width":"60",align:"center",prop:"num"}),s(r,{label:"\u7269\u6599\u4FE1\u606F","min-width":"250"},{default:d(({row:e})=>[i("div",M,[i("div",S,[a[0]||(a[0]=i("span",{class:"label"},"\u540D\u79F0\uFF1A",-1)),i("span",T,c(e.materialName||"-"),1)]),i("div",X,[a[1]||(a[1]=i("span",{class:"label"},"\u7F16\u7801\uFF1A",-1)),i("span",Y,c(e.materialCode||"-"),1)]),i("div",aa,[a[2]||(a[2]=i("span",{class:"label"},"\u89C4\u683C\uFF1A",-1)),i("span",ea,c(e.spec||"-"),1)]),i("div",la,[a[3]||(a[3]=i("span",{class:"label"},"\u8BA1\u5212\uFF1A",-1)),i("span",sa,c(e.plannedQuantity||"-"),1)])])]),_:1}),s(r,{label:"\u635F\u8017\u6570\u91CF","min-width":"160"},{default:d(({row:e,$index:m})=>[s(u,{prop:`${m}.lossQuantity`,rules:p(v).lossQuantity,class:"mb-0px!"},{default:d(()=>[s(t,{modelValue:e.lossQuantity,"onUpdate:modelValue":o=>e.lossQuantity=o,placeholder:"\u635F\u8017\u6570\u91CF",precision:4,min:0,disabled:l.disabled,class:"!w-full"},null,8,["modelValue","onUpdate:modelValue","disabled"])]),_:2},1032,["prop","rules"])]),_:1}),s(r,{label:"\u635F\u8017\u5355\u4F4D","min-width":"100"},{default:d(({row:e,$index:m})=>[s(u,{prop:`${m}.lossUnitName`,class:"mb-0px!"},{default:d(()=>[s(h,{modelValue:e.lossUnitName,"onUpdate:modelValue":o=>e.lossUnitName=o,placeholder:"\u635F\u8017\u5355\u4F4D",disabled:""},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"])]),_:1}),s(r,{label:"\u5907\u6CE8","min-width":"150"},{default:d(({row:e,$index:m})=>[s(u,{prop:`${m}.remark`,class:"mb-0px!"},{default:d(()=>[s(h,{modelValue:e.remark,"onUpdate:modelValue":o=>e.remark=o,placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8",disabled:l.disabled},null,8,["modelValue","onUpdate:modelValue","disabled"])]),_:2},1032,["prop"])]),_:1}),l.disabled?K("",!0):(_(),w(r,{key:0,label:"\u64CD\u4F5C",align:"center",width:"60",fixed:"right"},{default:d(({$index:e})=>[s(I,{link:"",type:"danger",onClick:m=>{return o=e,n.value.splice(o,1),void n.value.forEach((C,z)=>{C.num=z+1});var o},disabled:l.disabled,size:"small"},{default:d(()=>[s(V,{icon:"ep:delete"})]),_:2},1032,["onClick","disabled"])]),_:1}))]),_:1},8,["data"])]),_:1},8,["model","rules"])),[[$,p(f)]])}}}),[["__scopeId","data-v-ec406f02"]]);export{ta as default};
