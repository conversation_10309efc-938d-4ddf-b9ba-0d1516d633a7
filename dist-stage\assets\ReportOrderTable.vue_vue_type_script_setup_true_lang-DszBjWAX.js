import{D as v}from"./index-BeQABqnP.js";import{_ as R}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CxohUsTD.js";import{_ as H}from"./DictTag.vue_vue_type_script_lang-B0tTMPJ6.js";import{d as N}from"./formatTime-CN67D7Gb.js";import{R as q}from"./index-mfJlVb1-.js";import{b as _,f as h}from"./formatter-BLTmz7GT.js";import{ak as C,Z as F,_ as z}from"./form-designer-DQFPUccF.js";import{k as M,r as k,b as P,e as U,y as T,m as O,z as l,A as $,H as t,u as p,E as u,F as d}from"./form-create-B86qX0W_.js";const j=M({__name:"ReportOrderTable",props:{workOrderId:{}},setup(A,{expose:I}){const w=A,y=k(!1),n=k([]),b=async()=>{if(w.workOrderId){y.value=!0;try{const a=await q.getReportOrderPage({workId:w.workOrderId,pageNo:1,pageSize:100});a&&Array.isArray(a.list)?n.value=a.list:Array.isArray(a)?n.value=a:n.value=[]}catch{n.value=[]}finally{y.value=!1}}},E=a=>{const{columns:x,data:r}=a,i=[];return x.forEach((s,o)=>{if(o===0)return void(i[o]="\u5408\u8BA1");if(["costHeadcount","quantity","piece"].includes(s.property)){const m=r.map(e=>Number(e[s.property]));if(m.every(e=>Number.isNaN(e)))i[o]="";else{const e=m.reduce((c,g)=>{const f=Number(g);return Number.isNaN(f)?c:c+f},0);i[o]=h(e)}}else if(["costTime"].includes(s.property)){const m=r.map(e=>Number(e[s.property]));if(m.every(e=>Number.isNaN(e)))i[o]="";else{const e=m.reduce((c,g)=>{const f=Number(g);return Number.isNaN(f)?c:c+f},0);i[o]=_(e)}}else i[o]=""}),i};return P(()=>w.workOrderId,async a=>{a?await b():n.value=[]},{immediate:!0}),U(async()=>{w.workOrderId&&await b()}),I({refresh:b}),(a,x)=>{const r=z,i=H,s=F,o=R,m=C;return O(),T(o,null,{default:l(()=>[$((O(),T(s,{data:n.value,stripe:!0,border:"","show-overflow-tooltip":!0,"highlight-current-row":"","show-summary":"","summary-method":E,"max-height":n.value.length>0?600:200,style:{width:"100%"}},{default:l(()=>[t(r,{label:"\u751F\u4EA7\u7F16\u53F7",align:"center",prop:"workNo","min-width":"120px",fixed:"left"}),t(r,{label:"\u62A5\u5DE5\u7F16\u53F7",align:"center",prop:"reportCode","min-width":"120px",fixed:"left"}),t(r,{label:"\u4EFB\u52A1\u7C7B\u578B",align:"center",prop:"type","min-width":"100px"},{default:l(e=>[t(i,{type:p(v).MFG_WORK_TYPE,value:e.row.type},null,8,["type","value"])]),_:1}),t(r,{label:"\u5F00\u59CB\u65F6\u95F4",align:"center",prop:"startTime",formatter:p(N),"min-width":"160px"},null,8,["formatter"]),t(r,{label:"\u7ED3\u675F\u65F6\u95F4",align:"center",prop:"endTime",formatter:p(N),"min-width":"160px"},null,8,["formatter"]),t(r,{label:"\u7528\u65F6",align:"center",prop:"costTime","min-width":"100px"},{default:l(e=>[u(d(p(_)(e.row.costTime)),1)]),_:1}),t(r,{label:"\u4EBA\u6570",align:"center",prop:"costHeadcount","min-width":"80px"},{default:l(e=>[u(d(p(h)(e.row.costHeadcount)),1)]),_:1}),t(r,{label:"\u751F\u4EA7\u7EBF",align:"center",prop:"line","min-width":"100px"},{default:l(e=>[t(i,{type:p(v).MANUFACTURE_LINE,value:e.row.line},null,8,["type","value"])]),_:1}),t(r,{label:"\u6570\u91CF",align:"center",prop:"quantity","min-width":"100px"},{default:l(e=>[u(d(p(h)(e.row.quantity)),1)]),_:1}),t(r,{label:"\u4EF6\u6570",align:"center",prop:"piece","min-width":"100px"},{default:l(e=>[u(d(p(h)(e.row.piece)),1)]),_:1}),t(r,{label:"\u6279\u53F7",align:"center",prop:"batchNo","min-width":"120px"}),t(r,{label:"\u6E29\u5EA6",align:"center",prop:"temperature","min-width":"80px"},{default:l(e=>[u(d(e.row.temperature?`${e.row.temperature}\xB0C`:""),1)]),_:1}),t(r,{label:"\u6E7F\u5EA6",align:"center",prop:"humidity","min-width":"80px"},{default:l(e=>[u(d(e.row.humidity?`${e.row.humidity}%`:""),1)]),_:1}),t(r,{label:"\u5907\u6CE8",align:"left",prop:"remark","min-width":"150px"}),t(r,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",formatter:p(N),"min-width":"160px"},null,8,["formatter"])]),_:1},8,["data","max-height"])),[[m,y.value]])]),_:1})}}});export{j as _};
