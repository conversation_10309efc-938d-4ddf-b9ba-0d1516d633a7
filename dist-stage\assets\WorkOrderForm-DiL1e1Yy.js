import{a as ne,d as ce,au as $,D as W,c as me}from"./index-BeQABqnP.js";import{_ as pe}from"./Dialog.vue_vue_type_style_index_0_lang-DpoeWsKb.js";import{W as R}from"./index-B3LrXjfm.js";import{R as B}from"./index-Ufxn2eGP.js";import ve from"./WorkOrderDetailForm-DZA4obRf.js";import{_ as j}from"./index-CZs2S1Cj.js";import{O as ye}from"./index-B6tWTPuf.js";import{U as he}from"./index-BDU5cx5r.js";import{B as be}from"./index-CH8pxbb6.js";import{M as fe}from"./index-D4qK--X-.js";import{h as ge,aj as Qe,k as Ie,x as Ne,Q as Ve,F as ke,ak as Te,a0 as De,$ as we,f as Se}from"./form-designer-DQFPUccF.js";import{k as Ue,r as h,P as Ce,b as qe,e as _e,y as N,m as y,u as l,h as x,z as c,A as Re,H as r,l as T,G as D,$ as w,v as L,E as G,n as C}from"./form-create-B86qX0W_.js";import"./index-CASHthoJ.js";import"./tree-COGD3qag.js";import"./formatter-BLTmz7GT.js";import"./bomCalculation-DYv5_fDl.js";import"./el-infinite-scroll-l0sNRNKZ.js";const xe={class:"duration-input-group"},Oe=me(Ue({name:"WorkOrderForm",__name:"WorkOrderForm",emits:["success"],setup(Pe,{expose:J,emit:K}){const{t:O}=ne(),P=ce(),V=h(!1),A=h(""),g=h(!1),k=h(""),S=h(!1),e=h({id:void 0,workNo:void 0,planId:void 0,customerId:void 0,customerName:void 0,orderType:"mfg_request_order",orderDetailId:void 0,orderId:void 0,orderNo:void 0,requestId:void 0,requestNo:void 0,productId:void 0,productCode:void 0,productName:void 0,productSubType:void 0,productUnit:void 0,spec:void 0,status:void 0,bomId:void 0,bomCode:void 0,bomVersion:void 0,progress:void 0,orderDate:void 0,orderQuantity:void 0,orderUnit:void 0,deliverDate:void 0,scheduleStartDate:void 0,scheduleStartTime:void 0,scheduleEndDate:void 0,scheduleEndTime:void 0,scheduleQuantity:void 0,schedulePiece:void 0,scheduleCostTime:0,scheduleLine:void 0,scheduleHeadcount:void 0,requirement:void 0,remark:void 0,actualLine:void 0,actualQuantity:void 0,actualStartTime:void 0,actualEndTime:void 0,actualCostTime:void 0,actualHeadcount:void 0,actualPiece:void 0,actualBatchNo:void 0,actualRemark:void 0,shareImageUrl:void 0,scheduleTimeRange:[],bomLabel:null,slotQuantity:1,slotCount:void 0}),X=Ce({productId:[{required:!0,message:"\u4EA7\u54C1\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],productName:[{required:!0,message:"\u4EA7\u54C1\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],bomCode:[{required:!0,message:"Bom\u7F16\u7801\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],scheduleQuantity:[{required:!0,message:"\u8BA1\u5212\u6570\u91CF\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],scheduleTimeRange:[{required:!0,message:"\u8BA1\u5212\u5F00\u59CB\u65E5\u671F\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],slotQuantity:[{required:!0,message:"\u6BCF\u69FD\u6570\u91CF\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],slotCount:[{required:!0,message:"\u8BA1\u5212\u603B\u69FD\u6570\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),E=h(),Q=h("0"),I=h("0"),q=h("workOrderDetail"),p=h();J({open:async(s,t)=>{if(V.value=!0,A.value=O("action."+s),k.value=s,z(),s==="transferMfg"){g.value=!0;try{if(typeof t=="number"){const i=t,o=await B.getRequestOrder(i);if(o){e.value.orderId=o.id,e.value.orderNo=o.orderNo,e.value.requestNo=o.requestNo,e.value.requestId=o.id,e.value.customerId=o.customerId,e.value.customerName=o.customerName,e.value.productId=o.productId,e.value.productCode=o.productCode,e.value.productName=o.productName,e.value.productSubType=o.productSubType,e.value.productUnit=o.productUnit,e.value.spec=o.spec,e.value.orderDate=o.orderDate,e.value.orderQuantity=o.orderQuantity,e.value.orderUnit=o.orderUnit,e.value.deliverDate=o.deliverDate,e.value.scheduleQuantity=o.orderQuantity,e.value.schedulePiece=o.orderSpecQuantity,e.value.bomId=o.bomId,e.value.bomCode=o.bomCode,e.value.bomVersion=o.bomVersion,e.value.requirement=o.requirement,e.value.remark=o.remark;const m=Date.now(),d=o.deliverDate?new Date(o.deliverDate).getTime():m+6048e5;if(e.value.scheduleTimeRange=[m,d],e.value.scheduleStartDate=m,e.value.scheduleEndDate=d,S.value=!0,o.productId&&await U(o.productId),o.id)try{const v=await B.getRequestOrderDetailListByBizOrderId(o.id);if(await C(),p.value&&v&&v.length>0){const b=v.map(n=>({...n,materialId:n.materialId,materialName:n.materialName,materialCode:n.materialCode,materialType:n.materialType,spec:n.spec,unit:n.unit,standardUnitId:n.standardUnitId,warehouseId:n.warehouseId,locationId:n.locationId,plannedQuantity:n.plannedQuantity||0,fulfilledQuantity:n.fulfilledQuantity||0,pendingQuantity:n.pendingQuantity||0,readyQuantity:n.readyQuantity||0,slotQuantity:n.slotQuantity||0,slotSpecQuantity:n.slotSpecQuantity||0,stockQuantity:n.stockQuantity||0,shortageQuantity:n.shortageQuantity||0,purchaseQuantity:n.purchaseQuantity||0,transitQuantity:n.transitQuantity||0,lockStockQuantity:n.lockStockQuantity||0,lockTransitQuantity:n.lockTransitQuantity||0,readyStatus:n.readyStatus||0,lossRate:n.lossRate||0,lossQuantity:n.lossQuantity||0,unitPrice:n.unitPrice||0,amount:n.amount||0,remark:n.remark||"",note:n.note||"",batchNo:n.batchNo||"",num:n.num||0,version:n.version||1}));p.value.setDetails(b),setTimeout(()=>{p.value&&p.value.recalculateAll&&p.value.recalculateAll()},100)}}catch{}}}else if(t&&typeof t=="object"){if(e.value.orderId=t.orderId||t.id,e.value.orderNo=t.orderNo||t.requestNo,e.value.requestNo=t.requestNo,e.value.requestId=t.id,e.value.customerId=t.customerId,e.value.customerName=t.customerName,e.value.productId=t.productId,e.value.productCode=t.productCode,e.value.productName=t.productName,e.value.productSubType=t.productSubType,e.value.productUnit=t.productUnit,e.value.spec=t.spec,e.value.orderDate=t.orderDate,e.value.orderQuantity=t.orderQuantity||t.quantity,e.value.orderUnit=t.orderUnit||t.productUnit,e.value.deliverDate=t.deliverDate,e.value.scheduleQuantity=t.scheduleQuantity||t.quantity||t.orderQuantity||t.pendingQuantity,e.value.schedulePiece=t.schedulePiece||t.orderSpecQuantity,e.value.bomId=t.bomId,e.value.bomCode=t.bomCode,e.value.bomVersion=t.bomVersion,e.value.requirement=t.requirement,e.value.remark=t.remark,t.scheduleTimeRange)e.value.scheduleTimeRange=t.scheduleTimeRange,e.value.scheduleStartDate=t.scheduleStartDate||t.scheduleTimeRange[0],e.value.scheduleEndDate=t.scheduleEndDate||t.scheduleTimeRange[1];else{const i=Date.now(),o=t.deliverDate?new Date(t.deliverDate).getTime():i+6048e5;e.value.scheduleTimeRange=[i,o],e.value.scheduleStartDate=i,e.value.scheduleEndDate=o}if(S.value=!0,t.productId&&await U(t.productId),t.details&&t.details.length>0)await C(),p.value&&p.value.setDetails(t.details);else if(t.id||t.orderId)try{const i=t.id||t.orderId,o=await B.getRequestOrderDetailListByBizOrderId(i);if(o&&o.length>0){const m=o.map(d=>({...d,materialId:d.materialId,materialName:d.materialName,materialCode:d.materialCode,materialType:d.materialType,spec:d.spec,unit:d.unit,standardUnitId:d.standardUnitId,warehouseId:d.warehouseId,locationId:d.locationId,plannedQuantity:d.plannedQuantity||0,fulfilledQuantity:d.fulfilledQuantity||0,pendingQuantity:d.pendingQuantity||0,readyQuantity:d.readyQuantity||0,slotQuantity:d.slotQuantity||0,slotSpecQuantity:d.slotSpecQuantity||0,stockQuantity:d.stockQuantity||0,shortageQuantity:d.shortageQuantity||0,purchaseQuantity:d.purchaseQuantity||0,transitQuantity:d.transitQuantity||0,lockStockQuantity:d.lockStockQuantity||0,lockTransitQuantity:d.lockTransitQuantity||0,readyStatus:d.readyStatus||0,lossRate:d.lossRate||0,lossQuantity:d.lossQuantity||0,unitPrice:d.unitPrice||0,amount:d.amount||0,remark:d.remark||"",note:d.note||"",batchNo:d.batchNo||"",num:d.num||0,version:d.version||1}));await C(),p.value&&(p.value.setDetails(m),setTimeout(()=>{p.value&&p.value.recalculateAll&&p.value.recalculateAll()},100))}}catch{}}}catch{P.error("\u83B7\u53D6\u6216\u5904\u7406\u6570\u636E\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5")}finally{g.value=!1}}else if(t&&typeof t=="number"){const i=t;g.value=!0;try{const o=await R.getWorkOrder(i);if(o){if(e.value=o,e.value.scheduleStartDate&&e.value.scheduleEndDate&&(e.value.scheduleTimeRange=[e.value.scheduleStartDate,e.value.scheduleEndDate]),o.scheduleCostTime){const m=parseInt(o.scheduleCostTime.toString(),10)||0;e.value.scheduleCostTime=m,H(m)}if(o.productId&&await U(o.productId),await C(),p.value&&e.value.id)try{const m=await R.getWorkOrderDetailListByBizOrderId(e.value.id);m&&Array.isArray(m)&&p.value.setDetails(m)}catch{}}}catch{e.value||z()}finally{g.value=!1}}else s==="create"&&(f.value=[])}});const Y=K,Z=async()=>{e.value.scheduleTimeRange&&(e.value.scheduleStartTime=e.value.scheduleTimeRange[0],e.value.scheduleEndTime=e.value.scheduleTimeRange[1]),await E.value.validate();try{await p.value.validate()}catch{return void(q.value="workOrderDetail")}g.value=!0;try{const s=e.value;s.workOrderDetails=p.value.getData();const t=ee(s,k.value);k.value==="create"||k.value==="transferMfg"?(await R.createWorkOrder(t),P.success(O("common.createSuccess"))):(await R.updateWorkOrder(t),P.success(O("common.updateSuccess"))),V.value=!1,Y("success")}finally{g.value=!1}},ee=(s,t)=>{const i={},o=["id","workNo","planId","planNo","customerId","customerName","orderType","orderId","orderNo","productId","productCode","productName","productSubType","productUnit","spec","status","bomId","bomCode","bomVersion","progress","orderDate","orderQuantity","orderUnit","deliverDate","scheduleStartDate","scheduleStartTime","scheduleEndDate","scheduleEndTime","scheduleQuantity","schedulePiece","scheduleCostTime","scheduleLine","scheduleHeadcount","requirement","remark","actualLine","actualQuantity","actualStartTime","actualEndTime","actualCostTime","actualHeadcount","actualPiece","actualBatchNo","actualRemark","shareImageUrl","slotQuantity","slotCount","approveStatus","pickingStatus","inStockStatus","reportStatus","qualityStatus","approverName","requestId","requestNo"];if(t==="transferMfg"&&o.push("requestNo"),o.forEach(m=>{s[m]!==void 0&&(i[m]=s[m])}),s.workOrderDetails&&Array.isArray(s.workOrderDetails)){const m=["id","num","bizOrderId","bizOrderNo","warehouseId","locationId","materialId","materialName","materialCode","materialType","spec","unit","unitPrice","amount","remark","note","batchNo","quantity","plannedQuantity","fulfilledQuantity","pendingQuantity","standardPlannedQuantity","standardFulfilledQuantity","standardUnit","plannedSpecQuantity","fulfilledSpecQuantity","slotQuantity","slotSpecQuantity","lockStockQuantity","lockTransitQuantity","readyQuantity","stockQuantity","shortageQuantity","purchaseQuantity","transitQuantity","lockStockQuantity","readyStatus","lossRate","lossQuantity","taxPrice","taxAmount","invoiceQuantity","invoiceAmount","standardInvoiceQuantity","costObjectId","costObjectName","accountingVoucherNumber","kdId","kdOrderId","version"];i.workOrderDetails=s.workOrderDetails.map(d=>{const v={};return m.forEach(b=>{d[b]!==void 0&&(v[b]=d[b])}),v})}return i},z=()=>{var s;e.value={id:void 0,workNo:void 0,planId:void 0,customerId:void 0,customerName:void 0,orderType:"mfg_request_order",orderDetailId:void 0,orderId:void 0,orderNo:void 0,requestNo:void 0,requestId:void 0,productId:void 0,productCode:void 0,productName:void 0,productSubType:void 0,productUnit:void 0,spec:void 0,status:void 0,bomId:void 0,bomCode:void 0,bomVersion:void 0,progress:void 0,orderDate:void 0,orderQuantity:void 0,orderUnit:void 0,deliverDate:void 0,scheduleStartDate:void 0,scheduleStartTime:void 0,scheduleEndDate:void 0,scheduleEndTime:void 0,scheduleQuantity:void 0,schedulePiece:void 0,scheduleCostTime:0,scheduleLine:void 0,scheduleHeadcount:void 0,requirement:void 0,remark:void 0,actualLine:void 0,actualQuantity:void 0,actualStartTime:void 0,actualEndTime:void 0,actualCostTime:void 0,actualHeadcount:void 0,actualPiece:void 0,actualBatchNo:void 0,actualRemark:void 0,shareImageUrl:void 0,scheduleTimeRange:[],bomLabel:null,slotQuantity:1,slotCount:void 0},(s=E.value)==null||s.resetFields(),S.value=!1},te=async s=>{const{pageNo:t,pageSize:i,query:o,...m}=s,d=await ye.getSimpleOrderDetailPage({pageNo:t,pageSize:i,productName:o,...m}),{list:v,total:b}=await d;return{list:v.map(n=>({...n,productName:n.productName||"\u65E0\u4EA7\u54C1\u540D\u79F0",customerName:n.customerName||"\u65E0\u5BA2\u6237\u540D\u79F0",orderNo:n.orderNo||"\u65E0\u8BA2\u5355\u7F16\u53F7"})),total:b}},_=h([]),f=h([]),U=async s=>{if(!s)return void(f.value=[]);const t=await be.getSimpleBomPage({pageSize:20,pageNo:1,materialId:s});if(f.value=t.list,e.value.bomId&&f.value.length>0){const i=f.value.find(o=>o.id===e.value.bomId);i&&!e.value.bomCode&&(e.value.bomCode=i.code,e.value.bomVersion=i.versionNumber||"1.0")}else if(!e.value.bomId&&f.value.length>0&&k.value==="transferMfg"){const i=f.value[0];e.value.bomId=i.id,e.value.bomCode=i.code,e.value.bomVersion=i.versionNumber||"1.0"}},le=async s=>{const{pageNo:t,pageSize:i,query:o,...m}=s,d=await fe.getSimpleMaterialPage({pageNo:t,pageSize:i,name:o,...m}),{list:v,total:b}=await d;return{list:v,total:b}},ae=s=>{const t=s.replace(/[^\d]/g,"");Q.value=t},ue=s=>{let t=s.replace(/[^\d]/g,"");parseInt(t)>59&&(t="59"),I.value=t},M=()=>{const s=60*(parseInt(Q.value)||0)+(parseInt(I.value)||0);e.value.scheduleCostTime=s},H=s=>{const t=Math.floor(s/60),i=s%60;Q.value=t.toString(),I.value=i.toString()},oe=()=>{if(e.value.scheduleTimeRange&&e.value.scheduleTimeRange.length===2){const s=new Date(e.value.scheduleTimeRange[0]),t=new Date(e.value.scheduleTimeRange[1]).getTime()-s.getTime();if(t>0){const i=Math.round(t/6e4);e.value.scheduleCostTime=i,H(i)}else e.value.scheduleCostTime=0,Q.value="0",I.value="0"}else e.value.scheduleCostTime=0,Q.value="0",I.value="0"};return qe(()=>{var s,t;return{q:(s=e.value)==null?void 0:s.scheduleQuantity,s:(t=e.value)==null?void 0:t.slotQuantity}},()=>{(()=>{if(!e.value)return;const{scheduleQuantity:s,slotQuantity:t}=e.value;!s||!t||t<=0?e.value&&(e.value.slotCount=void 0):(e.value&&(e.value.slotCount=Math.ceil(s/t)),C(()=>{p.value&&p.value.recalculateAll&&p.value.recalculateAll()}))})()},{immediate:!0,deep:!0}),_e(async()=>{await(async()=>{const s=await he.getUnitPage({pageSize:100,pageNo:1});_.value=s.list.map(t=>({value:t.id,label:t.name}))})()}),(s,t)=>{const i=Ie,o=Qe,m=Ve,d=Ne,v=ke,b=ge,n=we,de=De,F=Se,re=pe,ie=Te;return y(),N(re,{title:l(A),modelValue:l(V),"onUpdate:modelValue":t[29]||(t[29]=a=>x(V)?V.value=a:null),width:"80%",height:"100%",fullscreen:!0},{footer:c(()=>[r(F,{onClick:Z,type:"primary",disabled:l(g)},{default:c(()=>t[32]||(t[32]=[G("\u786E \u5B9A")])),_:1},8,["disabled"]),r(F,{onClick:t[28]||(t[28]=a=>V.value=!1)},{default:c(()=>t[33]||(t[33]=[G("\u53D6 \u6D88")])),_:1})]),default:c(()=>[Re((y(),N(b,{ref_key:"formRef",ref:E,model:l(e),rules:l(X),"label-width":"100px",inline:!0},{default:c(()=>[r(o,{label:"\u751F\u4EA7\u5355\u53F7",prop:"workNo"},{default:c(()=>[r(i,{modelValue:l(e).workNo,"onUpdate:modelValue":t[0]||(t[0]=a=>l(e).workNo=a),placeholder:"\u4FDD\u5B58\u65F6\u81EA\u52A8\u751F\u6210",class:"!w-240px",disabled:""},null,8,["modelValue"])]),_:1}),r(o,{label:"\u6765\u6E90\u7C7B\u578B",prop:"orderType"},{default:c(()=>[r(d,{modelValue:l(e).orderType,"onUpdate:modelValue":t[1]||(t[1]=a=>l(e).orderType=a),placeholder:"\u8BF7\u9009\u62E9\u6765\u6E90\u7C7B\u578B",class:"!w-240px"},{default:c(()=>[(y(!0),T(D,null,w(l($)(l(W).MFG_ORDER_SOURCE),a=>(y(),N(m,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),r(o,{label:"\u9500\u552E\u5355\u53F7",prop:"orderNo"},{default:c(()=>[r(j,{filterable:"",clearable:"",class:"!w-240px",modelValue:l(e).orderNo,"onUpdate:modelValue":t[2]||(t[2]=a=>l(e).orderNo=a),placeholder:"\u8BF7\u9009\u62E9\u9500\u552E\u8BA2\u5355","load-method":te,"label-key":a=>`${a.orderNo} - ${a.productName} - ${a.customerName}`,"value-key":"id","default-value":{value:l(e).orderDetailId,label:(l(e).orderNo||"")+" - "+(l(e).productName||"")+" - "+(l(e).customerName||"")},"query-key":l(e).productName,"extra-params":{productionStatus:"not_planned"},onChange:t[3]||(t[3]=(a,u)=>{l(e).orderDetailId=a,l(e).orderId=u==null?void 0:u.orderId,l(e).orderNo=u==null?void 0:u.orderNo,l(e).productId=u==null?void 0:u.productId,l(e).productName=u==null?void 0:u.productName,l(e).productCode=u==null?void 0:u.productCode,l(e).productUnit=u==null?void 0:u.unit,l(e).spec=u==null?void 0:u.spec,l(e).productSubType=u==null?void 0:u.subType,l(e).customerId=u==null?void 0:u.customerId,l(e).customerName=u==null?void 0:u.customerName,l(e).orderUnit=u==null?void 0:u.unit,l(e).orderQuantity=u==null?void 0:u.quantity,l(e).orderDate=u==null?void 0:u.orderDate,l(e).deliverDate=u==null?void 0:u.deliverDate,l(e).scheduleQuantity=u==null?void 0:u.quantity,l(e).schedulePiece=u==null?void 0:u.specQuantityTotal,l(e).bomLabel=null,l(e).bomId=void 0,l(e).bomCode=void 0,S.value=!!a,U(u==null?void 0:u.productId)})},null,8,["modelValue","label-key","default-value","query-key"])]),_:1}),r(o,{label:"\u4EA7\u54C1\u540D\u79F0",prop:"productName"},{default:c(()=>[r(j,{filterable:"",clearable:"",class:"!w-240px",modelValue:l(e).productName,"onUpdate:modelValue":t[4]||(t[4]=a=>l(e).productName=a),placeholder:"\u8BF7\u9009\u62E9\u4EA7\u54C1\u540D\u79F0","load-method":le,"label-key":a=>`${a.name} - ${a.fullCode} - ${a.spec}`,disabled:l(S)===!0,"value-key":"id","default-value":{value:l(e).productName,label:l(e).productName},"query-key":s.productName,"extra-params":{types:[2,3]},onChange:t[5]||(t[5]=(a,u)=>{l(e).productId=u.id,l(e).productName=u==null?void 0:u.name,l(e).productCode=u==null?void 0:u.fullCode,l(e).productSubType=u==null?void 0:u.subType,l(e).productUnit=Number(u==null?void 0:u.unit),l(e).spec=u==null?void 0:u.spec,l(e).orderUnit=u==null?void 0:u.mfgUnit,U(u==null?void 0:u.id)})},null,8,["modelValue","label-key","disabled","default-value","query-key"])]),_:1}),r(o,{label:"\u8BA2\u5355\u6570\u91CF",prop:"orderQuantity"},{default:c(()=>[r(i,{modelValue:l(e).orderQuantity,"onUpdate:modelValue":t[7]||(t[7]=a=>l(e).orderQuantity=a),placeholder:"\u8BF7\u8F93\u5165\u8BA2\u5355\u6570\u91CF",class:"!w-240px",disabled:""},{append:c(()=>[r(d,{modelValue:l(e).productUnit,"onUpdate:modelValue":t[6]||(t[6]=a=>l(e).productUnit=a),placeholder:"\u8BF7\u9009\u62E9\u4EA7\u54C1\u5355\u4F4D",class:"!w-100px",disabled:""},{default:c(()=>[(y(!0),T(D,null,w(l(_),a=>(y(),N(m,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["modelValue"])]),_:1}),r(o,{label:"\u89C4\u683C",prop:"spec"},{default:c(()=>[r(i,{modelValue:l(e).spec,"onUpdate:modelValue":t[8]||(t[8]=a=>l(e).spec=a),placeholder:"\u8BF7\u8F93\u5165\u89C4\u683C",class:"!w-240px",disabled:""},null,8,["modelValue"])]),_:1}),r(o,{label:"\u5BA2\u6237",prop:"customerName"},{default:c(()=>[r(i,{modelValue:l(e).customerName,"onUpdate:modelValue":t[9]||(t[9]=a=>l(e).customerName=a),placeholder:"\u8BF7\u8F93\u5165\u5BA2\u6237\u540D\u79F0",class:"!w-240px",disabled:""},null,8,["modelValue"])]),_:1}),r(o,{label:"\u4E0B\u5355\u65F6\u95F4",prop:"orderDate"},{default:c(()=>[r(v,{modelValue:l(e).orderDate,"onUpdate:modelValue":t[10]||(t[10]=a=>l(e).orderDate=a),type:"date","value-format":"x",placeholder:"\u9009\u62E9\u4E0B\u5355\u65F6\u95F4",class:"!w-240px"},null,8,["modelValue"])]),_:1}),r(o,{label:"\u4EA4\u671F",prop:"deliverDate"},{default:c(()=>[r(v,{modelValue:l(e).deliverDate,"onUpdate:modelValue":t[11]||(t[11]=a=>l(e).deliverDate=a),type:"date","value-format":"x",placeholder:"\u9009\u62E9\u4EA4\u671F",class:"!w-240px"},null,8,["modelValue"])]),_:1}),r(o,{label:"\u8BA1\u5212\u65E5\u671F",prop:"scheduleTimeRange"},{default:c(()=>[r(v,{modelValue:l(e).scheduleTimeRange,"onUpdate:modelValue":t[12]||(t[12]=a=>l(e).scheduleTimeRange=a),type:"datetimerange","value-format":"x","range-separator":"\u81F3","start-placeholder":"\u5F00\u59CB\u65E5\u671F\u65F6\u95F4","end-placeholder":"\u7ED3\u675F\u65E5\u671F\u65F6\u95F4",class:"!w-full",onChange:oe},null,8,["modelValue"])]),_:1}),r(o,{label:"\u8BA1\u5212\u7528\u65F6",prop:"scheduleCostTime"},{default:c(()=>[L("div",xe,[r(i,{modelValue:l(Q),"onUpdate:modelValue":t[13]||(t[13]=a=>x(Q)?Q.value=a:null),placeholder:"0",class:"duration-input",onInput:ae,onBlur:M},null,8,["modelValue"]),t[30]||(t[30]=L("span",{class:"duration-label"},"\u5C0F\u65F6",-1)),r(i,{modelValue:l(I),"onUpdate:modelValue":t[14]||(t[14]=a=>x(I)?I.value=a:null),placeholder:"0",class:"duration-input",onInput:ue,onBlur:M},null,8,["modelValue"]),t[31]||(t[31]=L("span",{class:"duration-label"},"\u5206\u949F",-1))])]),_:1}),r(o,{label:"Bom\u7F16\u7801",prop:"bomId"},{default:c(()=>[r(d,{modelValue:l(e).bomCode,"onUpdate:modelValue":t[15]||(t[15]=a=>l(e).bomCode=a),placeholder:"\u8BF7\u9009\u62E9Bom\u7F16\u7801",clearable:"",class:"!w-240px",onChange:t[16]||(t[16]=a=>{const u=l(f).find(se=>se.code===a);u?(l(e).bomId=u.id,l(e).bomVersion=u.versionNumber||"1.0"):(l(e).bomId=void 0,l(e).bomVersion=void 0)})},{default:c(()=>[(y(!0),T(D,null,w(l(f),a=>(y(),N(m,{key:a.id,label:a.name||a.materialName+" - "+a.code+" - "+(a.versionNumber||"1.0"),value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),r(o,{label:"\u8BA1\u5212\u6570\u91CF",prop:"scheduleQuantity"},{default:c(()=>[r(i,{modelValue:l(e).scheduleQuantity,"onUpdate:modelValue":t[18]||(t[18]=a=>l(e).scheduleQuantity=a),placeholder:"\u8BF7\u8F93\u5165\u8BA1\u5212\u6570\u91CF",class:"!w-240px"},{append:c(()=>[r(d,{modelValue:l(e).productUnit,"onUpdate:modelValue":t[17]||(t[17]=a=>l(e).productUnit=a),placeholder:"\u8BF7\u9009\u62E9\u4EA7\u54C1\u5355\u4F4D",class:"!w-100px",disabled:""},{default:c(()=>[(y(!0),T(D,null,w(l(_),a=>(y(),N(m,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["modelValue"])]),_:1}),r(o,{label:"\u6BCF\u69FD\u6570\u91CF",prop:"slotQuantity"},{default:c(()=>[r(i,{modelValue:l(e).slotQuantity,"onUpdate:modelValue":t[20]||(t[20]=a=>l(e).slotQuantity=a),placeholder:"\u8BF7\u8F93\u5165\u6BCF\u69FD\u6570\u91CF",class:"!w-240px",type:"number"},{append:c(()=>[r(d,{modelValue:l(e).productUnit,"onUpdate:modelValue":t[19]||(t[19]=a=>l(e).productUnit=a),placeholder:"\u8BF7\u9009\u62E9\u4EA7\u54C1\u5355\u4F4D",class:"!w-100px",disabled:""},{default:c(()=>[(y(!0),T(D,null,w(l(_),a=>(y(),N(m,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["modelValue"])]),_:1}),r(o,{label:"\u8BA1\u5212\u603B\u69FD\u6570",prop:"slotCount"},{default:c(()=>[r(i,{modelValue:l(e).slotCount,"onUpdate:modelValue":t[21]||(t[21]=a=>l(e).slotCount=a),placeholder:"\u8BF7\u8F93\u5165\u8BA1\u5212\u603B\u69FD\u6570",class:"!w-240px",disabled:""},null,8,["modelValue"])]),_:1}),r(o,{label:"\u8BA1\u5212\u4EF6\u6570",prop:"schedulePiece"},{default:c(()=>[r(i,{modelValue:l(e).schedulePiece,"onUpdate:modelValue":t[22]||(t[22]=a=>l(e).schedulePiece=a),placeholder:"\u8BF7\u8F93\u5165\u8BA1\u5212\u4EF6\u6570",class:"!w-240px"},null,8,["modelValue"])]),_:1}),r(o,{label:"\u8BA1\u5212\u4EA7\u7EBF",prop:"scheduleLine"},{default:c(()=>[r(d,{modelValue:l(e).scheduleLine,"onUpdate:modelValue":t[23]||(t[23]=a=>l(e).scheduleLine=a),placeholder:"\u8BF7\u9009\u62E9\u751F\u4EA7\u7EBF",clearable:"",class:"!w-240px"},{default:c(()=>[(y(!0),T(D,null,w(l($)(l(W).MANUFACTURE_LINE),a=>(y(),N(m,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),r(o,{label:"\u8BA1\u5212\u7528\u4EBA",prop:"scheduleHeadcount"},{default:c(()=>[r(i,{modelValue:l(e).scheduleHeadcount,"onUpdate:modelValue":t[24]||(t[24]=a=>l(e).scheduleHeadcount=a),placeholder:"\u8BF7\u8F93\u5165\u8BA1\u5212\u7528\u4EBA",class:"!w-240px"},null,8,["modelValue"])]),_:1}),r(o,{label:"\u751F\u4EA7\u8981\u6C42",prop:"requirement"},{default:c(()=>[r(i,{modelValue:l(e).requirement,"onUpdate:modelValue":t[25]||(t[25]=a=>l(e).requirement=a),placeholder:"\u8BF7\u8F93\u5165\u751F\u4EA7\u8981\u6C42",class:"!w-240px"},null,8,["modelValue"])]),_:1}),r(o,{label:"\u5907\u6CE8",prop:"remark"},{default:c(()=>[r(i,{modelValue:l(e).remark,"onUpdate:modelValue":t[26]||(t[26]=a=>l(e).remark=a),placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8",class:"!w-240px"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[ie,l(g)]]),r(de,{modelValue:l(q),"onUpdate:modelValue":t[27]||(t[27]=a=>x(q)?q.value=a:null)},{default:c(()=>[r(n,{label:"\u4EFB\u52A1\u5355\u660E\u7EC6",name:"workOrderDetail"},{default:c(()=>[r(ve,{ref_key:"workOrderDetailFormRef",ref:p,"biz-order-id":l(e).id,"bom-id":l(e).bomId,"is-transfer-mode":l(k)==="transferMfg","slot-quantity":l(e).slotQuantity,"order-quantity":l(e).scheduleQuantity,"order-spec-quantity":l(e).schedulePiece},null,8,["biz-order-id","bom-id","is-transfer-mode","slot-quantity","order-quantity","order-spec-quantity"])]),_:1})]),_:1},8,["modelValue"])]),_:1},8,["title","modelValue"])}}}),[["__scopeId","data-v-6a8ba0d7"]]);export{Oe as default};
